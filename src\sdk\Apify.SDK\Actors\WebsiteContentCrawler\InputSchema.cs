﻿/*
 * Website Content Crawler
 *
 * Crawl websites and extract text content to feed AI models, LLM applications, vector databases, or RAG pipelines. The Actor supports rich formatting using Markdown, cleans the HTML, downloads files, and integrates well with 🦜🔗 LangChain, LlamaIndex, and the wider LLM ecosystem.
 *
 * The version of the OpenAPI document: v1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Apify.SDK.Client.FileParameter;
using OpenAPIDateConverter = Apify.SDK.Client.OpenAPIDateConverter;

namespace Apify.SDK.Actors.WebsiteContentCrawler
{     
    /// <summary>
    /// InputSchema
    /// </summary>
    [DataContract(Name = "inputSchema")]
    public partial class InputSchema : IValidatableObject
    {
        /// <summary>
        /// Select the crawling engine: - **Headless web browser** - Useful for modern websites with anti-scraping protections and JavaScript rendering. It recognizes common blocking patterns like CAPTCHAs and automatically retries blocked requests through new sessions. However, running web browsers is more expensive as it requires more computing resources and is slower. It is recommended to use at least 8 GB of RAM. - **Stealthy web browser** (default) - Another headless web browser with anti-blocking measures enabled. Try this if you encounter bot protection while scraping. For best performance, use with Apify Proxy residential IPs.  - **Adaptive switching between Chrome and raw HTTP client** - The crawler automatically switches between raw HTTP for static pages and Chrome browser (via Playwright) for dynamic pages, to get the maximum performance wherever possible.  - **Raw HTTP client** - High-performance crawling mode that uses raw HTTP requests to fetch the pages. It is faster and cheaper, but it might not work on all websites.  Beware that with the raw HTTP client or adaptive crawling mode, some features are not available, e.g. wait for dynamic content, maximum scroll height, or remove cookie warnings.
        /// </summary>
        /// <value>Select the crawling engine: - **Headless web browser** - Useful for modern websites with anti-scraping protections and JavaScript rendering. It recognizes common blocking patterns like CAPTCHAs and automatically retries blocked requests through new sessions. However, running web browsers is more expensive as it requires more computing resources and is slower. It is recommended to use at least 8 GB of RAM. - **Stealthy web browser** (default) - Another headless web browser with anti-blocking measures enabled. Try this if you encounter bot protection while scraping. For best performance, use with Apify Proxy residential IPs.  - **Adaptive switching between Chrome and raw HTTP client** - The crawler automatically switches between raw HTTP for static pages and Chrome browser (via Playwright) for dynamic pages, to get the maximum performance wherever possible.  - **Raw HTTP client** - High-performance crawling mode that uses raw HTTP requests to fetch the pages. It is faster and cheaper, but it might not work on all websites.  Beware that with the raw HTTP client or adaptive crawling mode, some features are not available, e.g. wait for dynamic content, maximum scroll height, or remove cookie warnings.</value>
        [JsonConverter(typeof(StringEnumConverter))]
        public enum CrawlerTypeEnum
        {
            /// <summary>
            /// Enum Playwrightadaptive for value: playwright:adaptive
            /// </summary>
            [EnumMember(Value = "playwright:adaptive")]
            Playwrightadaptive = 1,

            /// <summary>
            /// Enum Playwrightfirefox for value: playwright:firefox
            /// </summary>
            [EnumMember(Value = "playwright:firefox")]
            Playwrightfirefox = 2,

            /// <summary>
            /// Enum Cheerio for value: cheerio
            /// </summary>
            [EnumMember(Value = "cheerio")]
            Cheerio = 3,

            /// <summary>
            /// Enum Jsdom for value: jsdom
            /// </summary>
            [EnumMember(Value = "jsdom")]
            Jsdom = 4,

            /// <summary>
            /// Enum Playwrightchrome for value: playwright:chrome
            /// </summary>
            [EnumMember(Value = "playwright:chrome")]
            Playwrightchrome = 5
        }


        /// <summary>
        /// Select the crawling engine: - **Headless web browser** - Useful for modern websites with anti-scraping protections and JavaScript rendering. It recognizes common blocking patterns like CAPTCHAs and automatically retries blocked requests through new sessions. However, running web browsers is more expensive as it requires more computing resources and is slower. It is recommended to use at least 8 GB of RAM. - **Stealthy web browser** (default) - Another headless web browser with anti-blocking measures enabled. Try this if you encounter bot protection while scraping. For best performance, use with Apify Proxy residential IPs.  - **Adaptive switching between Chrome and raw HTTP client** - The crawler automatically switches between raw HTTP for static pages and Chrome browser (via Playwright) for dynamic pages, to get the maximum performance wherever possible.  - **Raw HTTP client** - High-performance crawling mode that uses raw HTTP requests to fetch the pages. It is faster and cheaper, but it might not work on all websites.  Beware that with the raw HTTP client or adaptive crawling mode, some features are not available, e.g. wait for dynamic content, maximum scroll height, or remove cookie warnings.
        /// </summary>
        /// <value>Select the crawling engine: - **Headless web browser** - Useful for modern websites with anti-scraping protections and JavaScript rendering. It recognizes common blocking patterns like CAPTCHAs and automatically retries blocked requests through new sessions. However, running web browsers is more expensive as it requires more computing resources and is slower. It is recommended to use at least 8 GB of RAM. - **Stealthy web browser** (default) - Another headless web browser with anti-blocking measures enabled. Try this if you encounter bot protection while scraping. For best performance, use with Apify Proxy residential IPs.  - **Adaptive switching between Chrome and raw HTTP client** - The crawler automatically switches between raw HTTP for static pages and Chrome browser (via Playwright) for dynamic pages, to get the maximum performance wherever possible.  - **Raw HTTP client** - High-performance crawling mode that uses raw HTTP requests to fetch the pages. It is faster and cheaper, but it might not work on all websites.  Beware that with the raw HTTP client or adaptive crawling mode, some features are not available, e.g. wait for dynamic content, maximum scroll height, or remove cookie warnings.</value>
        [DataMember(Name = "crawlerType", EmitDefaultValue = false)]
        public CrawlerTypeEnum? CrawlerType { get; set; }
        /// <summary>
        /// Specify how to transform the HTML to extract meaningful content without any extra fluff, like navigation or modals. The HTML transformation happens after removing and clicking the DOM elements.  - **Readable text with fallback** - Extracts the main contents of the webpage, without navigation and other fluff while carefully checking the content integrality.  - **Readable text** (default) - Extracts the main contents of the webpage, without navigation and other fluff. - **Extractus** - Uses Extractus library. - **None** - Only removes the HTML elements specified via &#39;Remove HTML elements&#39; option.  You can examine output of all transformers by enabling the debug mode. 
        /// </summary>
        /// <value>Specify how to transform the HTML to extract meaningful content without any extra fluff, like navigation or modals. The HTML transformation happens after removing and clicking the DOM elements.  - **Readable text with fallback** - Extracts the main contents of the webpage, without navigation and other fluff while carefully checking the content integrality.  - **Readable text** (default) - Extracts the main contents of the webpage, without navigation and other fluff. - **Extractus** - Uses Extractus library. - **None** - Only removes the HTML elements specified via &#39;Remove HTML elements&#39; option.  You can examine output of all transformers by enabling the debug mode. </value>
        [JsonConverter(typeof(StringEnumConverter))]
        public enum HtmlTransformerEnum
        {
            /// <summary>
            /// Enum ReadableTextIfPossible for value: readableTextIfPossible
            /// </summary>
            [EnumMember(Value = "readableTextIfPossible")]
            ReadableTextIfPossible = 1,

            /// <summary>
            /// Enum ReadableText for value: readableText
            /// </summary>
            [EnumMember(Value = "readableText")]
            ReadableText = 2,

            /// <summary>
            /// Enum Extractus for value: extractus
            /// </summary>
            [EnumMember(Value = "extractus")]
            Extractus = 3,

            /// <summary>
            /// Enum None for value: none
            /// </summary>
            [EnumMember(Value = "none")]
            None = 4
        }


        /// <summary>
        /// Specify how to transform the HTML to extract meaningful content without any extra fluff, like navigation or modals. The HTML transformation happens after removing and clicking the DOM elements.  - **Readable text with fallback** - Extracts the main contents of the webpage, without navigation and other fluff while carefully checking the content integrality.  - **Readable text** (default) - Extracts the main contents of the webpage, without navigation and other fluff. - **Extractus** - Uses Extractus library. - **None** - Only removes the HTML elements specified via &#39;Remove HTML elements&#39; option.  You can examine output of all transformers by enabling the debug mode. 
        /// </summary>
        /// <value>Specify how to transform the HTML to extract meaningful content without any extra fluff, like navigation or modals. The HTML transformation happens after removing and clicking the DOM elements.  - **Readable text with fallback** - Extracts the main contents of the webpage, without navigation and other fluff while carefully checking the content integrality.  - **Readable text** (default) - Extracts the main contents of the webpage, without navigation and other fluff. - **Extractus** - Uses Extractus library. - **None** - Only removes the HTML elements specified via &#39;Remove HTML elements&#39; option.  You can examine output of all transformers by enabling the debug mode. </value>
        [DataMember(Name = "htmlTransformer", EmitDefaultValue = false)]
        public HtmlTransformerEnum? HtmlTransformer { get; set; }
        /// <summary>
        /// Initializes a new instance of the <see cref="InputSchema" /> class.
        /// </summary>
        [JsonConstructorAttribute]
        protected InputSchema() { }
        /// <summary>
        /// Initializes a new instance of the <see cref="InputSchema" /> class.
        /// </summary>
        /// <param name="startUrls">One or more URLs of pages where the crawler will start.  By default, the Actor will also crawl sub-pages of these URLs. For example, for start URL &#x60;https://example.com/blog&#x60;, it will crawl also &#x60;https://example.com/blog/post&#x60; or &#x60;https://example.com/blog/article&#x60;. The **Include URLs (globs)** option overrides this automation behavior. (required).</param>
        /// <param name="useSitemaps">If enabled, the crawler will look for [Sitemaps](https://en.wikipedia.org/wiki/Sitemaps) at the domains of the provided *Start URLs* and enqueue matching URLs similarly as the links found on crawled pages. You can also reference a &#x60;sitemap.xml&#x60; file directly by adding it as another Start URL (e.g. &#x60;https://www.example.com/sitemap.xml&#x60;)  This feature makes the crawling more robust on websites that support Sitemaps, as it includes pages that might be not reachable from Start URLs. However, **loading and processing Sitemaps can take a lot of time, especially for large sites**. Note that if a page is found via Sitemaps, it will have &#x60;depth&#x60; of &#x60;1&#x60;. (default to false).</param>
        /// <param name="crawlerType">Select the crawling engine: - **Headless web browser** - Useful for modern websites with anti-scraping protections and JavaScript rendering. It recognizes common blocking patterns like CAPTCHAs and automatically retries blocked requests through new sessions. However, running web browsers is more expensive as it requires more computing resources and is slower. It is recommended to use at least 8 GB of RAM. - **Stealthy web browser** (default) - Another headless web browser with anti-blocking measures enabled. Try this if you encounter bot protection while scraping. For best performance, use with Apify Proxy residential IPs.  - **Adaptive switching between Chrome and raw HTTP client** - The crawler automatically switches between raw HTTP for static pages and Chrome browser (via Playwright) for dynamic pages, to get the maximum performance wherever possible.  - **Raw HTTP client** - High-performance crawling mode that uses raw HTTP requests to fetch the pages. It is faster and cheaper, but it might not work on all websites.  Beware that with the raw HTTP client or adaptive crawling mode, some features are not available, e.g. wait for dynamic content, maximum scroll height, or remove cookie warnings. (default to CrawlerTypeEnum.Playwrightfirefox).</param>
        /// <param name="includeUrlGlobs">Glob patterns matching URLs of pages that will be included in crawling.   Setting this option will disable the default Start URLs based scoping and will allow you to customize the crawling scope yourself. Note that this affects only links found on pages, but not **Start URLs** - if you want to crawl a page, make sure to specify its URL in the **Start URLs** field.   For example &#x60;https://{store,docs}.example.com/_**&#x60; lets the crawler to access all URLs starting with &#x60;https://store.example.com/&#x60; or &#x60;https://docs.example.com/&#x60;, and &#x60;https://example.com/_**_/_*\\?*foo&#x3D;*&#x60; allows the crawler to access all URLs that contain &#x60;foo&#x60; query parameter with any value.  Learn more about globs and test them [here](https://www.digitalocean.com/community/tools/glob?comments&#x3D;true&amp;glob&#x3D;https%3A%2F%2Fexample.com%2Fscrape_this%2F%2A%2A&amp;matches&#x3D;false&amp;tests&#x3D;https%3A%2F%2Fexample.com%2Ftools%2F&amp;tests&#x3D;https%3A%2F%2Fexample.com%2Fscrape_this%2F&amp;tests&#x3D;https%3A%2F%2Fexample.com%2Fscrape_this%2F123%3Ftest%3Dabc&amp;tests&#x3D;https%3A%2F%2Fexample.com%2Fdont_scrape_this)..</param>
        /// <param name="excludeUrlGlobs">Glob patterns matching URLs of pages that will be excluded from crawling. Note that this affects only links found on pages, but not **Start URLs**, which are always crawled.   For example &#x60;https://{store,docs}.example.com/_**&#x60; excludes all URLs starting with &#x60;https://store.example.com/&#x60; or &#x60;https://docs.example.com/&#x60;, and &#x60;https://example.com/_**_/_*\\?*foo&#x3D;*&#x60; excludes all URLs that contain &#x60;foo&#x60; query parameter with any value.  Learn more about globs and test them [here](https://www.digitalocean.com/community/tools/glob?comments&#x3D;true&amp;glob&#x3D;https%3A%2F%2Fexample.com%2Fdont_scrape_this%2F%2A%2A&amp;matches&#x3D;false&amp;tests&#x3D;https%3A%2F%2Fexample.com%2Ftools%2F&amp;tests&#x3D;https%3A%2F%2Fexample.com%2Fdont_scrape_this%2F&amp;tests&#x3D;https%3A%2F%2Fexample.com%2Fdont_scrape_this%2F123%3Ftest%3Dabc&amp;tests&#x3D;https%3A%2F%2Fexample.com%2Fscrape_this)..</param>
        /// <param name="keepUrlFragments">Indicates that URL fragments (e.g. &lt;code&gt;http://example.com&lt;b&gt;#fragment&lt;/b&gt;&lt;/code&gt;) should be included when checking whether a URL has already been visited or not. Typically, URL fragments are used for page navigation only and therefore they should be ignored, as they don&#39;t identify separate pages. However, some single-page websites use URL fragments to display different pages; in such a case, this option should be enabled. (default to false).</param>
        /// <param name="ignoreCanonicalUrl">If enabled, the Actor will ignore the canonical URL reported by the page, and use the actual URL instead. You can use this feature for websites that report invalid canonical URLs, which causes the Actor to skip those pages in results. (default to false).</param>
        /// <param name="maxCrawlDepth">The maximum number of links starting from the start URL that the crawler will recursively follow. The start URLs have depth &#x60;0&#x60;, the pages linked directly from the start URLs have depth &#x60;1&#x60;, and so on.  This setting is useful to prevent accidental crawler runaway. By setting it to &#x60;0&#x60;, the Actor will only crawl the Start URLs. (default to 20).</param>
        /// <param name="maxCrawlPages">The maximum number pages to crawl. It includes the start URLs, pagination pages, pages with no content, etc. The crawler will automatically finish after reaching this number. This setting is useful to prevent accidental crawler runaway. (default to 9999999).</param>
        /// <param name="initialConcurrency">The initial number of web browsers or HTTP clients running in parallel. The system scales the concurrency up and down based on the current CPU and memory load. If the value is set to 0 (default), the Actor uses the default setting for the specific crawler type.  Note that if you set this value too high, the Actor will run out of memory and crash. If too low, it will be slow at start before it scales the concurrency up. (default to 0).</param>
        /// <param name="maxConcurrency">The maximum number of web browsers or HTTP clients running in parallel. This setting is useful to avoid overloading the target websites and to avoid getting blocked. (default to 200).</param>
        /// <param name="initialCookies">Cookies that will be pre-set to all pages the scraper opens. This is useful for pages that require login. The value is expected to be a JSON array of objects with &#x60;name&#x60; and &#x60;value&#x60; properties. For example: &#x60;[{\&quot;name\&quot;: \&quot;cookieName\&quot;, \&quot;value\&quot;: \&quot;cookieValue\&quot;}]&#x60;.  You can use the [EditThisCookie](https://chrome.google.com/webstore/detail/editthiscookie/fngmhnnpilhplaeedifhccceomclgfbg) browser extension to copy browser cookies in this format, and paste it here..</param>
        /// <param name="proxyConfiguration">Enables loading the websites from IP addresses in specific geographies and to circumvent blocking. (required).</param>
        /// <param name="maxSessionRotations">The maximum number of times the crawler will rotate the session (IP address + browser configuration) on anti-scraping measures like CAPTCHAs. If the crawler rotates the session more than this number and the page is still blocked, it will finish with an error. (default to 10).</param>
        /// <param name="maxRequestRetries">The maximum number of times the crawler will retry the request on network, proxy or server errors. If the (n+1)-th request still fails, the crawler will mark this request as failed. (default to 5).</param>
        /// <param name="requestTimeoutSecs">Timeout in seconds for making the request and processing its response. Defaults to 60s. (default to 60).</param>
        /// <param name="minFileDownloadSpeedKBps">The minimum viable file download speed in kilobytes per seconds. If the file download speed is lower than this value for a prolonged duration, the crawler will consider the file download as failing, abort it, and retry it again (up to \&quot;Maximum number of retries\&quot; times). This is useful to avoid your crawls being stuck on slow file downloads. (default to 128).</param>
        /// <param name="dynamicContentWaitSecs">The maximum time in seconds to wait for dynamic page content to load. By default, it is 10 seconds. The crawler will continue processing the page either if this time elapses, or if it detects the network became idle as there are no more requests for additional resources.  When using the **Wait for selector** option, the crawler will wait for the selector to appear for this amount of time. If the selector doesn&#39;t appear within this period, the request will fail and will be retried.  Note that this setting is ignored for the raw HTTP client, because it doesn&#39;t execute JavaScript or loads any dynamic resources. Similarly, if the value is set to &#x60;0&#x60;, the crawler doesn&#39;t wait for any dynamic to load and processes the HTML as provided on load. (default to 10).</param>
        /// <param name="waitForSelector">If set, the crawler will wait for the specified CSS selector to appear in the page before proceeding with the content extraction. This is useful for pages for which the default content load recognition by idle network fails. Setting this option completely disables the default behavior, and the page will be processed only if the element specified by this selector appears. If the element doesn&#39;t appear within the **Wait for dynamic content** timeout, the request will fail and will be retried later. The value must be a valid CSS selector as accepted by the &#x60;document.querySelectorAll()&#x60; function.  With the raw HTTP client, this option checks for the presence of the selector in the HTML content and throws an error if it&#39;s not found. (default to &quot;&quot;).</param>
        /// <param name="softWaitForSelector">If set, the crawler will wait for the specified CSS selector to appear in the page before proceeding with the content extraction. Unlike the &#x60;waitForSelector&#x60; option, this option doesn&#39;t fail the request if the selector doesn&#39;t appear within the timeout (the request processing will continue). (default to &quot;&quot;).</param>
        /// <param name="maxScrollHeightPixels">The crawler will scroll down the page until all content is loaded (and network becomes idle), or until this maximum scrolling height is reached. Setting this value to &#x60;0&#x60; disables scrolling altogether.  Note that this setting is ignored for the raw HTTP client, because it doesn&#39;t execute JavaScript or loads any dynamic resources. (default to 5000).</param>
        /// <param name="keepElementsCssSelector">An optional CSS selector matching HTML elements that should be preserved in the DOM. If provided, all HTML elements which are not matching the CSS selectors or their descendants are removed from the DOM. This is useful to extract only relevant page content. The value must be a valid CSS selector as accepted by the &#x60;document.querySelectorAll()&#x60; function.   This option runs before the &#x60;HTML transformer&#x60; option. If you are missing content in the output despite using this option, try disabling the &#x60;HTML transformer&#x60;. (default to &quot;&quot;).</param>
        /// <param name="removeElementsCssSelector">A CSS selector matching HTML elements that will be removed from the DOM, before converting it to text, Markdown, or saving as HTML. This is useful to skip irrelevant page content. The value must be a valid CSS selector as accepted by the &#x60;document.querySelectorAll()&#x60; function.   By default, the Actor removes common navigation elements, headers, footers, modals, scripts, and inline image. You can disable the removal by setting this value to some non-existent CSS selector like &#x60;dummy_keep_everything&#x60;. (default to &quot;nav, footer, script, style, noscript, svg, img[src^&#x3D;&#39;data:&#39;],[role&#x3D;&quot;alert&quot;],[role&#x3D;&quot;banner&quot;],[role&#x3D;&quot;dialog&quot;],[role&#x3D;&quot;alertdialog&quot;],[role&#x3D;&quot;region&quot;][aria-label*&#x3D;&quot;skip&quot; i],[aria-modal&#x3D;&quot;true&quot;]&quot;).</param>
        /// <param name="removeCookieWarnings">If enabled, the Actor will try to remove cookies consent dialogs or modals, using the [I don&#39;t care about cookies](https://addons.mozilla.org/en-US/firefox/addon/i-dont-care-about-cookies/) browser extension, to improve the accuracy of the extracted text. Note that there is a small performance penalty if this feature is enabled.  This setting is ignored when using the raw HTTP crawler type. (default to true).</param>
        /// <param name="expandIframes">By default, the Actor will extract content from &#x60;iframe&#x60; elements. If you want to specifically skip &#x60;iframe&#x60; processing, disable this option. Works only for the &#x60;playwright:firefox&#x60; crawler type. (default to true).</param>
        /// <param name="clickElementsCssSelector">A CSS selector matching DOM elements that will be clicked. This is useful for expanding collapsed sections, in order to capture their text content. The value must be a valid CSS selector as accepted by the &#x60;document.querySelectorAll()&#x60; function.  (default to &quot;[aria-expanded&#x3D;&quot;false&quot;]&quot;).</param>
        /// <param name="htmlTransformer">Specify how to transform the HTML to extract meaningful content without any extra fluff, like navigation or modals. The HTML transformation happens after removing and clicking the DOM elements.  - **Readable text with fallback** - Extracts the main contents of the webpage, without navigation and other fluff while carefully checking the content integrality.  - **Readable text** (default) - Extracts the main contents of the webpage, without navigation and other fluff. - **Extractus** - Uses Extractus library. - **None** - Only removes the HTML elements specified via &#39;Remove HTML elements&#39; option.  You can examine output of all transformers by enabling the debug mode.  (default to HtmlTransformerEnum.ReadableText).</param>
        /// <param name="readableTextCharThreshold">A configuration options for the \&quot;Readable text\&quot; HTML transformer. It contains the minimum number of characters an article must have in order to be considered relevant. (default to 100).</param>
        /// <param name="aggressivePrune">This is an **experimental feature**. If enabled, the crawler will prune content lines that are very similar to the ones already crawled on other pages, using the Count-Min Sketch algorithm. This is useful to strip repeating content in the scraped data like menus, headers, footers, etc. In some (not very likely) cases, it might remove relevant content from some pages. (default to false).</param>
        /// <param name="debugMode">If enabled, the Actor will store the output of all types of HTML transformers, including the ones that are not used by default, and it will also store the HTML to Key-value Store with a link. All this data is stored under the &#x60;debug&#x60; field in the resulting Dataset. (default to false).</param>
        /// <param name="debugLog">If enabled, the actor log will include debug messages. Beware that this can be quite verbose. (default to false).</param>
        /// <param name="saveHtml">If enabled, the crawler stores full transformed HTML of all pages found to the output dataset under the &#x60;html&#x60; field. **This option has been deprecated** in favor of the &#x60;saveHtmlAsFile&#x60; option, because the dataset records have a size of approximately 10MB and it&#39;s harder to review the HTML for debugging. (default to false).</param>
        /// <param name="saveHtmlAsFile">If enabled, the crawler stores full transformed HTML of all pages found to the default key-value store and saves links to the files as &#x60;htmlUrl&#x60; field in the output dataset. Storing HTML in key-value store is preferred to storing it into the dataset with the &#x60;saveHtml&#x60; option, because there&#39;s no size limit and it&#39;s easier for debugging as you can easily view the HTML. (default to false).</param>
        /// <param name="saveMarkdown">If enabled, the crawler converts the transformed HTML of all pages found to Markdown, and stores it under the &#x60;markdown&#x60; field in the output dataset. (default to true).</param>
        /// <param name="saveFiles">If enabled, the crawler downloads files linked from the web pages, as long as their URL has one of the following file extensions: PDF, DOC, DOCX, XLS, XLSX, and CSV. Note that unlike web pages, the files are downloaded regardless if they are under **Start URLs** or not. The files are stored to the default key-value store, and metadata about them to the output dataset, similarly as for web pages. (default to false).</param>
        /// <param name="saveScreenshots">If enabled, the crawler stores a screenshot for each article page to the default key-value store. The link to the screenshot is stored under the &#x60;screenshotUrl&#x60; field in the output dataset. It is useful for debugging, but reduces performance and increases storage costs.  Note that this feature only works with the &#x60;playwright:firefox&#x60; crawler type. (default to false).</param>
        /// <param name="maxResults">The maximum number of resulting web pages to store. The crawler will automatically finish after reaching this number. This setting is useful to prevent accidental crawler runaway. If both **Max pages** and **Max results** are defined, then the crawler will finish when the first limit is reached. Note that the crawler skips pages with the canonical URL of a page that has already been crawled, hence it might crawl more pages than there are results. (default to 9999999).</param>
        /// <param name="textExtractor">Removed in favor of the &#x60;htmlTransformer&#x60; option. Will be removed soon..</param>
        /// <param name="clientSideMinChangePercentage">The least amount of content (as a percentage) change after the initial load required to consider the pages client-side rendered (default to 15).</param>
        /// <param name="renderingTypeDetectionPercentage">How often should the adaptive attempt to detect page rendering type (default to 10).</param>
        public InputSchema(List<StartURLsInner> startUrls = default(List<StartURLsInner>), bool useSitemaps = false, CrawlerTypeEnum? crawlerType = CrawlerTypeEnum.Playwrightfirefox, List<IncludeURLsGlobsInner> includeUrlGlobs = default(List<IncludeURLsGlobsInner>), List<IncludeURLsGlobsInner> excludeUrlGlobs = default(List<IncludeURLsGlobsInner>), bool keepUrlFragments = false, bool ignoreCanonicalUrl = false, int maxCrawlDepth = 20, int maxCrawlPages = 9999999, int initialConcurrency = 0, int maxConcurrency = 200, List<string> initialCookies = default(List<string>), ProxyConfiguration proxyConfiguration = default(ProxyConfiguration), int maxSessionRotations = 10, int maxRequestRetries = 5, int requestTimeoutSecs = 60, int minFileDownloadSpeedKBps = 128, int dynamicContentWaitSecs = 10, string waitForSelector = @"", string softWaitForSelector = @"", int maxScrollHeightPixels = 5000, string keepElementsCssSelector = @"", string removeElementsCssSelector = "nav, footer, script, style, noscript, svg, img[src^='data:'],[role=\"alert\"],[role=\"banner\"],[role=\"dialog\"],[role=\"alertdialog\"],[role=\"region\"][aria-label*=\"skip\" i],[aria-modal=\"true\"]", bool removeCookieWarnings = true, bool expandIframes = true, string clickElementsCssSelector = "[aria-expanded=\"false\"]", HtmlTransformerEnum? htmlTransformer = HtmlTransformerEnum.ReadableText, int readableTextCharThreshold = 100, bool aggressivePrune = false, bool debugMode = false, bool debugLog = false, bool saveHtml = false, bool saveHtmlAsFile = false, bool saveMarkdown = true, bool saveFiles = false, bool saveScreenshots = false, int maxResults = 9999999, string textExtractor = default(string), int clientSideMinChangePercentage = 15, int renderingTypeDetectionPercentage = 10)
        {
            // to ensure "startUrls" is required (not null)
            if (startUrls == null)
            {
                throw new ArgumentNullException("startUrls is a required property for InputSchema and cannot be null");
            }
            this.StartUrls = startUrls;
            // to ensure "proxyConfiguration" is required (not null)
            if (proxyConfiguration == null)
            {
                throw new ArgumentNullException("proxyConfiguration is a required property for InputSchema and cannot be null");
            }
            this.ProxyConfiguration = proxyConfiguration;
            this.UseSitemaps = useSitemaps;
            this.CrawlerType = crawlerType;
            this.IncludeUrlGlobs = includeUrlGlobs;
            this.ExcludeUrlGlobs = excludeUrlGlobs;
            this.KeepUrlFragments = keepUrlFragments;
            this.IgnoreCanonicalUrl = ignoreCanonicalUrl;
            this.MaxCrawlDepth = maxCrawlDepth;
            this.MaxCrawlPages = maxCrawlPages;
            this.InitialConcurrency = initialConcurrency;
            this.MaxConcurrency = maxConcurrency;
            this.InitialCookies = initialCookies;
            this.MaxSessionRotations = maxSessionRotations;
            this.MaxRequestRetries = maxRequestRetries;
            this.RequestTimeoutSecs = requestTimeoutSecs;
            this.MinFileDownloadSpeedKBps = minFileDownloadSpeedKBps;
            this.DynamicContentWaitSecs = dynamicContentWaitSecs;
            // use default value if no "waitForSelector" provided
            this.WaitForSelector = waitForSelector ?? @"";
            // use default value if no "softWaitForSelector" provided
            this.SoftWaitForSelector = softWaitForSelector ?? @"";
            this.MaxScrollHeightPixels = maxScrollHeightPixels;
            // use default value if no "keepElementsCssSelector" provided
            this.KeepElementsCssSelector = keepElementsCssSelector ?? @"";
            // use default value if no "removeElementsCssSelector" provided
            this.RemoveElementsCssSelector = removeElementsCssSelector ?? "nav, footer, script, style, noscript, svg, img[src^='data:'],[role=\"alert\"],[role=\"banner\"],[role=\"dialog\"],[role=\"alertdialog\"],[role=\"region\"][aria-label*=\"skip\" i],[aria-modal=\"true\"]";
            this.RemoveCookieWarnings = removeCookieWarnings;
            this.ExpandIframes = expandIframes;
            // use default value if no "clickElementsCssSelector" provided
            this.ClickElementsCssSelector = clickElementsCssSelector ?? "[aria-expanded=\"false\"]";
            this.HtmlTransformer = htmlTransformer;
            this.ReadableTextCharThreshold = readableTextCharThreshold;
            this.AggressivePrune = aggressivePrune;
            this.DebugMode = debugMode;
            this.DebugLog = debugLog;
            this.SaveHtml = saveHtml;
            this.SaveHtmlAsFile = saveHtmlAsFile;
            this.SaveMarkdown = saveMarkdown;
            this.SaveFiles = saveFiles;
            this.SaveScreenshots = saveScreenshots;
            this.MaxResults = maxResults;
            this.TextExtractor = textExtractor;
            this.ClientSideMinChangePercentage = clientSideMinChangePercentage;
            this.RenderingTypeDetectionPercentage = renderingTypeDetectionPercentage;
        }

        /// <summary>
        /// One or more URLs of pages where the crawler will start.  By default, the Actor will also crawl sub-pages of these URLs. For example, for start URL &#x60;https://example.com/blog&#x60;, it will crawl also &#x60;https://example.com/blog/post&#x60; or &#x60;https://example.com/blog/article&#x60;. The **Include URLs (globs)** option overrides this automation behavior.
        /// </summary>
        /// <value>One or more URLs of pages where the crawler will start.  By default, the Actor will also crawl sub-pages of these URLs. For example, for start URL &#x60;https://example.com/blog&#x60;, it will crawl also &#x60;https://example.com/blog/post&#x60; or &#x60;https://example.com/blog/article&#x60;. The **Include URLs (globs)** option overrides this automation behavior.</value>
        [DataMember(Name = "startUrls", IsRequired = true, EmitDefaultValue = true)]
        public List<StartURLsInner> StartUrls { get; set; }

        /// <summary>
        /// If enabled, the crawler will look for [Sitemaps](https://en.wikipedia.org/wiki/Sitemaps) at the domains of the provided *Start URLs* and enqueue matching URLs similarly as the links found on crawled pages. You can also reference a &#x60;sitemap.xml&#x60; file directly by adding it as another Start URL (e.g. &#x60;https://www.example.com/sitemap.xml&#x60;)  This feature makes the crawling more robust on websites that support Sitemaps, as it includes pages that might be not reachable from Start URLs. However, **loading and processing Sitemaps can take a lot of time, especially for large sites**. Note that if a page is found via Sitemaps, it will have &#x60;depth&#x60; of &#x60;1&#x60;.
        /// </summary>
        /// <value>If enabled, the crawler will look for [Sitemaps](https://en.wikipedia.org/wiki/Sitemaps) at the domains of the provided *Start URLs* and enqueue matching URLs similarly as the links found on crawled pages. You can also reference a &#x60;sitemap.xml&#x60; file directly by adding it as another Start URL (e.g. &#x60;https://www.example.com/sitemap.xml&#x60;)  This feature makes the crawling more robust on websites that support Sitemaps, as it includes pages that might be not reachable from Start URLs. However, **loading and processing Sitemaps can take a lot of time, especially for large sites**. Note that if a page is found via Sitemaps, it will have &#x60;depth&#x60; of &#x60;1&#x60;.</value>
        [DataMember(Name = "useSitemaps", EmitDefaultValue = true)]
        public bool UseSitemaps { get; set; }

        /// <summary>
        /// Glob patterns matching URLs of pages that will be included in crawling.   Setting this option will disable the default Start URLs based scoping and will allow you to customize the crawling scope yourself. Note that this affects only links found on pages, but not **Start URLs** - if you want to crawl a page, make sure to specify its URL in the **Start URLs** field.   For example &#x60;https://{store,docs}.example.com/_**&#x60; lets the crawler to access all URLs starting with &#x60;https://store.example.com/&#x60; or &#x60;https://docs.example.com/&#x60;, and &#x60;https://example.com/_**_/_*\\?*foo&#x3D;*&#x60; allows the crawler to access all URLs that contain &#x60;foo&#x60; query parameter with any value.  Learn more about globs and test them [here](https://www.digitalocean.com/community/tools/glob?comments&#x3D;true&amp;glob&#x3D;https%3A%2F%2Fexample.com%2Fscrape_this%2F%2A%2A&amp;matches&#x3D;false&amp;tests&#x3D;https%3A%2F%2Fexample.com%2Ftools%2F&amp;tests&#x3D;https%3A%2F%2Fexample.com%2Fscrape_this%2F&amp;tests&#x3D;https%3A%2F%2Fexample.com%2Fscrape_this%2F123%3Ftest%3Dabc&amp;tests&#x3D;https%3A%2F%2Fexample.com%2Fdont_scrape_this).
        /// </summary>
        /// <value>Glob patterns matching URLs of pages that will be included in crawling.   Setting this option will disable the default Start URLs based scoping and will allow you to customize the crawling scope yourself. Note that this affects only links found on pages, but not **Start URLs** - if you want to crawl a page, make sure to specify its URL in the **Start URLs** field.   For example &#x60;https://{store,docs}.example.com/_**&#x60; lets the crawler to access all URLs starting with &#x60;https://store.example.com/&#x60; or &#x60;https://docs.example.com/&#x60;, and &#x60;https://example.com/_**_/_*\\?*foo&#x3D;*&#x60; allows the crawler to access all URLs that contain &#x60;foo&#x60; query parameter with any value.  Learn more about globs and test them [here](https://www.digitalocean.com/community/tools/glob?comments&#x3D;true&amp;glob&#x3D;https%3A%2F%2Fexample.com%2Fscrape_this%2F%2A%2A&amp;matches&#x3D;false&amp;tests&#x3D;https%3A%2F%2Fexample.com%2Ftools%2F&amp;tests&#x3D;https%3A%2F%2Fexample.com%2Fscrape_this%2F&amp;tests&#x3D;https%3A%2F%2Fexample.com%2Fscrape_this%2F123%3Ftest%3Dabc&amp;tests&#x3D;https%3A%2F%2Fexample.com%2Fdont_scrape_this).</value>
        [DataMember(Name = "includeUrlGlobs", EmitDefaultValue = false)]
        public List<IncludeURLsGlobsInner> IncludeUrlGlobs { get; set; }

        /// <summary>
        /// Glob patterns matching URLs of pages that will be excluded from crawling. Note that this affects only links found on pages, but not **Start URLs**, which are always crawled.   For example &#x60;https://{store,docs}.example.com/_**&#x60; excludes all URLs starting with &#x60;https://store.example.com/&#x60; or &#x60;https://docs.example.com/&#x60;, and &#x60;https://example.com/_**_/_*\\?*foo&#x3D;*&#x60; excludes all URLs that contain &#x60;foo&#x60; query parameter with any value.  Learn more about globs and test them [here](https://www.digitalocean.com/community/tools/glob?comments&#x3D;true&amp;glob&#x3D;https%3A%2F%2Fexample.com%2Fdont_scrape_this%2F%2A%2A&amp;matches&#x3D;false&amp;tests&#x3D;https%3A%2F%2Fexample.com%2Ftools%2F&amp;tests&#x3D;https%3A%2F%2Fexample.com%2Fdont_scrape_this%2F&amp;tests&#x3D;https%3A%2F%2Fexample.com%2Fdont_scrape_this%2F123%3Ftest%3Dabc&amp;tests&#x3D;https%3A%2F%2Fexample.com%2Fscrape_this).
        /// </summary>
        /// <value>Glob patterns matching URLs of pages that will be excluded from crawling. Note that this affects only links found on pages, but not **Start URLs**, which are always crawled.   For example &#x60;https://{store,docs}.example.com/_**&#x60; excludes all URLs starting with &#x60;https://store.example.com/&#x60; or &#x60;https://docs.example.com/&#x60;, and &#x60;https://example.com/_**_/_*\\?*foo&#x3D;*&#x60; excludes all URLs that contain &#x60;foo&#x60; query parameter with any value.  Learn more about globs and test them [here](https://www.digitalocean.com/community/tools/glob?comments&#x3D;true&amp;glob&#x3D;https%3A%2F%2Fexample.com%2Fdont_scrape_this%2F%2A%2A&amp;matches&#x3D;false&amp;tests&#x3D;https%3A%2F%2Fexample.com%2Ftools%2F&amp;tests&#x3D;https%3A%2F%2Fexample.com%2Fdont_scrape_this%2F&amp;tests&#x3D;https%3A%2F%2Fexample.com%2Fdont_scrape_this%2F123%3Ftest%3Dabc&amp;tests&#x3D;https%3A%2F%2Fexample.com%2Fscrape_this).</value>
        [DataMember(Name = "excludeUrlGlobs", EmitDefaultValue = false)]
        public List<IncludeURLsGlobsInner> ExcludeUrlGlobs { get; set; }

        /// <summary>
        /// Indicates that URL fragments (e.g. &lt;code&gt;http://example.com&lt;b&gt;#fragment&lt;/b&gt;&lt;/code&gt;) should be included when checking whether a URL has already been visited or not. Typically, URL fragments are used for page navigation only and therefore they should be ignored, as they don&#39;t identify separate pages. However, some single-page websites use URL fragments to display different pages; in such a case, this option should be enabled.
        /// </summary>
        /// <value>Indicates that URL fragments (e.g. &lt;code&gt;http://example.com&lt;b&gt;#fragment&lt;/b&gt;&lt;/code&gt;) should be included when checking whether a URL has already been visited or not. Typically, URL fragments are used for page navigation only and therefore they should be ignored, as they don&#39;t identify separate pages. However, some single-page websites use URL fragments to display different pages; in such a case, this option should be enabled.</value>
        [DataMember(Name = "keepUrlFragments", EmitDefaultValue = true)]
        public bool KeepUrlFragments { get; set; }

        /// <summary>
        /// If enabled, the Actor will ignore the canonical URL reported by the page, and use the actual URL instead. You can use this feature for websites that report invalid canonical URLs, which causes the Actor to skip those pages in results.
        /// </summary>
        /// <value>If enabled, the Actor will ignore the canonical URL reported by the page, and use the actual URL instead. You can use this feature for websites that report invalid canonical URLs, which causes the Actor to skip those pages in results.</value>
        [DataMember(Name = "ignoreCanonicalUrl", EmitDefaultValue = true)]
        public bool IgnoreCanonicalUrl { get; set; }

        /// <summary>
        /// The maximum number of links starting from the start URL that the crawler will recursively follow. The start URLs have depth &#x60;0&#x60;, the pages linked directly from the start URLs have depth &#x60;1&#x60;, and so on.  This setting is useful to prevent accidental crawler runaway. By setting it to &#x60;0&#x60;, the Actor will only crawl the Start URLs.
        /// </summary>
        /// <value>The maximum number of links starting from the start URL that the crawler will recursively follow. The start URLs have depth &#x60;0&#x60;, the pages linked directly from the start URLs have depth &#x60;1&#x60;, and so on.  This setting is useful to prevent accidental crawler runaway. By setting it to &#x60;0&#x60;, the Actor will only crawl the Start URLs.</value>
        [DataMember(Name = "maxCrawlDepth", EmitDefaultValue = false)]
        public int MaxCrawlDepth { get; set; }

        /// <summary>
        /// The maximum number pages to crawl. It includes the start URLs, pagination pages, pages with no content, etc. The crawler will automatically finish after reaching this number. This setting is useful to prevent accidental crawler runaway.
        /// </summary>
        /// <value>The maximum number pages to crawl. It includes the start URLs, pagination pages, pages with no content, etc. The crawler will automatically finish after reaching this number. This setting is useful to prevent accidental crawler runaway.</value>
        [DataMember(Name = "maxCrawlPages", EmitDefaultValue = false)]
        public int MaxCrawlPages { get; set; }

        /// <summary>
        /// The initial number of web browsers or HTTP clients running in parallel. The system scales the concurrency up and down based on the current CPU and memory load. If the value is set to 0 (default), the Actor uses the default setting for the specific crawler type.  Note that if you set this value too high, the Actor will run out of memory and crash. If too low, it will be slow at start before it scales the concurrency up.
        /// </summary>
        /// <value>The initial number of web browsers or HTTP clients running in parallel. The system scales the concurrency up and down based on the current CPU and memory load. If the value is set to 0 (default), the Actor uses the default setting for the specific crawler type.  Note that if you set this value too high, the Actor will run out of memory and crash. If too low, it will be slow at start before it scales the concurrency up.</value>
        [DataMember(Name = "initialConcurrency", EmitDefaultValue = false)]
        public int InitialConcurrency { get; set; }

        /// <summary>
        /// The maximum number of web browsers or HTTP clients running in parallel. This setting is useful to avoid overloading the target websites and to avoid getting blocked.
        /// </summary>
        /// <value>The maximum number of web browsers or HTTP clients running in parallel. This setting is useful to avoid overloading the target websites and to avoid getting blocked.</value>
        [DataMember(Name = "maxConcurrency", EmitDefaultValue = false)]
        public int MaxConcurrency { get; set; }

        /// <summary>
        /// Cookies that will be pre-set to all pages the scraper opens. This is useful for pages that require login. The value is expected to be a JSON array of objects with &#x60;name&#x60; and &#x60;value&#x60; properties. For example: &#x60;[{\&quot;name\&quot;: \&quot;cookieName\&quot;, \&quot;value\&quot;: \&quot;cookieValue\&quot;}]&#x60;.  You can use the [EditThisCookie](https://chrome.google.com/webstore/detail/editthiscookie/fngmhnnpilhplaeedifhccceomclgfbg) browser extension to copy browser cookies in this format, and paste it here.
        /// </summary>
        /// <value>Cookies that will be pre-set to all pages the scraper opens. This is useful for pages that require login. The value is expected to be a JSON array of objects with &#x60;name&#x60; and &#x60;value&#x60; properties. For example: &#x60;[{\&quot;name\&quot;: \&quot;cookieName\&quot;, \&quot;value\&quot;: \&quot;cookieValue\&quot;}]&#x60;.  You can use the [EditThisCookie](https://chrome.google.com/webstore/detail/editthiscookie/fngmhnnpilhplaeedifhccceomclgfbg) browser extension to copy browser cookies in this format, and paste it here.</value>
        [DataMember(Name = "initialCookies", EmitDefaultValue = false)]
        public List<string> InitialCookies { get; set; }

        /// <summary>
        /// Enables loading the websites from IP addresses in specific geographies and to circumvent blocking.
        /// </summary>
        /// <value>Enables loading the websites from IP addresses in specific geographies and to circumvent blocking.</value>
        [DataMember(Name = "proxyConfiguration", IsRequired = true, EmitDefaultValue = true)]
        public ProxyConfiguration ProxyConfiguration { get; set; }

        /// <summary>
        /// The maximum number of times the crawler will rotate the session (IP address + browser configuration) on anti-scraping measures like CAPTCHAs. If the crawler rotates the session more than this number and the page is still blocked, it will finish with an error.
        /// </summary>
        /// <value>The maximum number of times the crawler will rotate the session (IP address + browser configuration) on anti-scraping measures like CAPTCHAs. If the crawler rotates the session more than this number and the page is still blocked, it will finish with an error.</value>
        [DataMember(Name = "maxSessionRotations", EmitDefaultValue = false)]
        public int MaxSessionRotations { get; set; }

        /// <summary>
        /// The maximum number of times the crawler will retry the request on network, proxy or server errors. If the (n+1)-th request still fails, the crawler will mark this request as failed.
        /// </summary>
        /// <value>The maximum number of times the crawler will retry the request on network, proxy or server errors. If the (n+1)-th request still fails, the crawler will mark this request as failed.</value>
        [DataMember(Name = "maxRequestRetries", EmitDefaultValue = false)]
        public int MaxRequestRetries { get; set; }

        /// <summary>
        /// Timeout in seconds for making the request and processing its response. Defaults to 60s.
        /// </summary>
        /// <value>Timeout in seconds for making the request and processing its response. Defaults to 60s.</value>
        [DataMember(Name = "requestTimeoutSecs", EmitDefaultValue = false)]
        public int RequestTimeoutSecs { get; set; }

        /// <summary>
        /// The minimum viable file download speed in kilobytes per seconds. If the file download speed is lower than this value for a prolonged duration, the crawler will consider the file download as failing, abort it, and retry it again (up to \&quot;Maximum number of retries\&quot; times). This is useful to avoid your crawls being stuck on slow file downloads.
        /// </summary>
        /// <value>The minimum viable file download speed in kilobytes per seconds. If the file download speed is lower than this value for a prolonged duration, the crawler will consider the file download as failing, abort it, and retry it again (up to \&quot;Maximum number of retries\&quot; times). This is useful to avoid your crawls being stuck on slow file downloads.</value>
        [DataMember(Name = "minFileDownloadSpeedKBps", EmitDefaultValue = false)]
        public int MinFileDownloadSpeedKBps { get; set; }

        /// <summary>
        /// The maximum time in seconds to wait for dynamic page content to load. By default, it is 10 seconds. The crawler will continue processing the page either if this time elapses, or if it detects the network became idle as there are no more requests for additional resources.  When using the **Wait for selector** option, the crawler will wait for the selector to appear for this amount of time. If the selector doesn&#39;t appear within this period, the request will fail and will be retried.  Note that this setting is ignored for the raw HTTP client, because it doesn&#39;t execute JavaScript or loads any dynamic resources. Similarly, if the value is set to &#x60;0&#x60;, the crawler doesn&#39;t wait for any dynamic to load and processes the HTML as provided on load.
        /// </summary>
        /// <value>The maximum time in seconds to wait for dynamic page content to load. By default, it is 10 seconds. The crawler will continue processing the page either if this time elapses, or if it detects the network became idle as there are no more requests for additional resources.  When using the **Wait for selector** option, the crawler will wait for the selector to appear for this amount of time. If the selector doesn&#39;t appear within this period, the request will fail and will be retried.  Note that this setting is ignored for the raw HTTP client, because it doesn&#39;t execute JavaScript or loads any dynamic resources. Similarly, if the value is set to &#x60;0&#x60;, the crawler doesn&#39;t wait for any dynamic to load and processes the HTML as provided on load.</value>
        [DataMember(Name = "dynamicContentWaitSecs", EmitDefaultValue = false)]
        public int DynamicContentWaitSecs { get; set; }

        /// <summary>
        /// If set, the crawler will wait for the specified CSS selector to appear in the page before proceeding with the content extraction. This is useful for pages for which the default content load recognition by idle network fails. Setting this option completely disables the default behavior, and the page will be processed only if the element specified by this selector appears. If the element doesn&#39;t appear within the **Wait for dynamic content** timeout, the request will fail and will be retried later. The value must be a valid CSS selector as accepted by the &#x60;document.querySelectorAll()&#x60; function.  With the raw HTTP client, this option checks for the presence of the selector in the HTML content and throws an error if it&#39;s not found.
        /// </summary>
        /// <value>If set, the crawler will wait for the specified CSS selector to appear in the page before proceeding with the content extraction. This is useful for pages for which the default content load recognition by idle network fails. Setting this option completely disables the default behavior, and the page will be processed only if the element specified by this selector appears. If the element doesn&#39;t appear within the **Wait for dynamic content** timeout, the request will fail and will be retried later. The value must be a valid CSS selector as accepted by the &#x60;document.querySelectorAll()&#x60; function.  With the raw HTTP client, this option checks for the presence of the selector in the HTML content and throws an error if it&#39;s not found.</value>
        [DataMember(Name = "waitForSelector", EmitDefaultValue = false)]
        public string WaitForSelector { get; set; }

        /// <summary>
        /// If set, the crawler will wait for the specified CSS selector to appear in the page before proceeding with the content extraction. Unlike the &#x60;waitForSelector&#x60; option, this option doesn&#39;t fail the request if the selector doesn&#39;t appear within the timeout (the request processing will continue).
        /// </summary>
        /// <value>If set, the crawler will wait for the specified CSS selector to appear in the page before proceeding with the content extraction. Unlike the &#x60;waitForSelector&#x60; option, this option doesn&#39;t fail the request if the selector doesn&#39;t appear within the timeout (the request processing will continue).</value>
        [DataMember(Name = "softWaitForSelector", EmitDefaultValue = false)]
        public string SoftWaitForSelector { get; set; }

        /// <summary>
        /// The crawler will scroll down the page until all content is loaded (and network becomes idle), or until this maximum scrolling height is reached. Setting this value to &#x60;0&#x60; disables scrolling altogether.  Note that this setting is ignored for the raw HTTP client, because it doesn&#39;t execute JavaScript or loads any dynamic resources.
        /// </summary>
        /// <value>The crawler will scroll down the page until all content is loaded (and network becomes idle), or until this maximum scrolling height is reached. Setting this value to &#x60;0&#x60; disables scrolling altogether.  Note that this setting is ignored for the raw HTTP client, because it doesn&#39;t execute JavaScript or loads any dynamic resources.</value>
        [DataMember(Name = "maxScrollHeightPixels", EmitDefaultValue = false)]
        public int MaxScrollHeightPixels { get; set; }

        /// <summary>
        /// An optional CSS selector matching HTML elements that should be preserved in the DOM. If provided, all HTML elements which are not matching the CSS selectors or their descendants are removed from the DOM. This is useful to extract only relevant page content. The value must be a valid CSS selector as accepted by the &#x60;document.querySelectorAll()&#x60; function.   This option runs before the &#x60;HTML transformer&#x60; option. If you are missing content in the output despite using this option, try disabling the &#x60;HTML transformer&#x60;.
        /// </summary>
        /// <value>An optional CSS selector matching HTML elements that should be preserved in the DOM. If provided, all HTML elements which are not matching the CSS selectors or their descendants are removed from the DOM. This is useful to extract only relevant page content. The value must be a valid CSS selector as accepted by the &#x60;document.querySelectorAll()&#x60; function.   This option runs before the &#x60;HTML transformer&#x60; option. If you are missing content in the output despite using this option, try disabling the &#x60;HTML transformer&#x60;.</value>
        [DataMember(Name = "keepElementsCssSelector", EmitDefaultValue = false)]
        public string KeepElementsCssSelector { get; set; }

        /// <summary>
        /// A CSS selector matching HTML elements that will be removed from the DOM, before converting it to text, Markdown, or saving as HTML. This is useful to skip irrelevant page content. The value must be a valid CSS selector as accepted by the &#x60;document.querySelectorAll()&#x60; function.   By default, the Actor removes common navigation elements, headers, footers, modals, scripts, and inline image. You can disable the removal by setting this value to some non-existent CSS selector like &#x60;dummy_keep_everything&#x60;.
        /// </summary>
        /// <value>A CSS selector matching HTML elements that will be removed from the DOM, before converting it to text, Markdown, or saving as HTML. This is useful to skip irrelevant page content. The value must be a valid CSS selector as accepted by the &#x60;document.querySelectorAll()&#x60; function.   By default, the Actor removes common navigation elements, headers, footers, modals, scripts, and inline image. You can disable the removal by setting this value to some non-existent CSS selector like &#x60;dummy_keep_everything&#x60;.</value>
        [DataMember(Name = "removeElementsCssSelector", EmitDefaultValue = false)]
        public string RemoveElementsCssSelector { get; set; }

        /// <summary>
        /// If enabled, the Actor will try to remove cookies consent dialogs or modals, using the [I don&#39;t care about cookies](https://addons.mozilla.org/en-US/firefox/addon/i-dont-care-about-cookies/) browser extension, to improve the accuracy of the extracted text. Note that there is a small performance penalty if this feature is enabled.  This setting is ignored when using the raw HTTP crawler type.
        /// </summary>
        /// <value>If enabled, the Actor will try to remove cookies consent dialogs or modals, using the [I don&#39;t care about cookies](https://addons.mozilla.org/en-US/firefox/addon/i-dont-care-about-cookies/) browser extension, to improve the accuracy of the extracted text. Note that there is a small performance penalty if this feature is enabled.  This setting is ignored when using the raw HTTP crawler type.</value>
        [DataMember(Name = "removeCookieWarnings", EmitDefaultValue = true)]
        public bool RemoveCookieWarnings { get; set; }

        /// <summary>
        /// By default, the Actor will extract content from &#x60;iframe&#x60; elements. If you want to specifically skip &#x60;iframe&#x60; processing, disable this option. Works only for the &#x60;playwright:firefox&#x60; crawler type.
        /// </summary>
        /// <value>By default, the Actor will extract content from &#x60;iframe&#x60; elements. If you want to specifically skip &#x60;iframe&#x60; processing, disable this option. Works only for the &#x60;playwright:firefox&#x60; crawler type.</value>
        [DataMember(Name = "expandIframes", EmitDefaultValue = true)]
        public bool ExpandIframes { get; set; }

        /// <summary>
        /// A CSS selector matching DOM elements that will be clicked. This is useful for expanding collapsed sections, in order to capture their text content. The value must be a valid CSS selector as accepted by the &#x60;document.querySelectorAll()&#x60; function. 
        /// </summary>
        /// <value>A CSS selector matching DOM elements that will be clicked. This is useful for expanding collapsed sections, in order to capture their text content. The value must be a valid CSS selector as accepted by the &#x60;document.querySelectorAll()&#x60; function. </value>
        [DataMember(Name = "clickElementsCssSelector", EmitDefaultValue = false)]
        public string ClickElementsCssSelector { get; set; }

        /// <summary>
        /// A configuration options for the \&quot;Readable text\&quot; HTML transformer. It contains the minimum number of characters an article must have in order to be considered relevant.
        /// </summary>
        /// <value>A configuration options for the \&quot;Readable text\&quot; HTML transformer. It contains the minimum number of characters an article must have in order to be considered relevant.</value>
        [DataMember(Name = "readableTextCharThreshold", EmitDefaultValue = false)]
        public int ReadableTextCharThreshold { get; set; }

        /// <summary>
        /// This is an **experimental feature**. If enabled, the crawler will prune content lines that are very similar to the ones already crawled on other pages, using the Count-Min Sketch algorithm. This is useful to strip repeating content in the scraped data like menus, headers, footers, etc. In some (not very likely) cases, it might remove relevant content from some pages.
        /// </summary>
        /// <value>This is an **experimental feature**. If enabled, the crawler will prune content lines that are very similar to the ones already crawled on other pages, using the Count-Min Sketch algorithm. This is useful to strip repeating content in the scraped data like menus, headers, footers, etc. In some (not very likely) cases, it might remove relevant content from some pages.</value>
        [DataMember(Name = "aggressivePrune", EmitDefaultValue = true)]
        public bool AggressivePrune { get; set; }

        /// <summary>
        /// If enabled, the Actor will store the output of all types of HTML transformers, including the ones that are not used by default, and it will also store the HTML to Key-value Store with a link. All this data is stored under the &#x60;debug&#x60; field in the resulting Dataset.
        /// </summary>
        /// <value>If enabled, the Actor will store the output of all types of HTML transformers, including the ones that are not used by default, and it will also store the HTML to Key-value Store with a link. All this data is stored under the &#x60;debug&#x60; field in the resulting Dataset.</value>
        [DataMember(Name = "debugMode", EmitDefaultValue = true)]
        public bool DebugMode { get; set; }

        /// <summary>
        /// If enabled, the actor log will include debug messages. Beware that this can be quite verbose.
        /// </summary>
        /// <value>If enabled, the actor log will include debug messages. Beware that this can be quite verbose.</value>
        [DataMember(Name = "debugLog", EmitDefaultValue = true)]
        public bool DebugLog { get; set; }

        /// <summary>
        /// If enabled, the crawler stores full transformed HTML of all pages found to the output dataset under the &#x60;html&#x60; field. **This option has been deprecated** in favor of the &#x60;saveHtmlAsFile&#x60; option, because the dataset records have a size of approximately 10MB and it&#39;s harder to review the HTML for debugging.
        /// </summary>
        /// <value>If enabled, the crawler stores full transformed HTML of all pages found to the output dataset under the &#x60;html&#x60; field. **This option has been deprecated** in favor of the &#x60;saveHtmlAsFile&#x60; option, because the dataset records have a size of approximately 10MB and it&#39;s harder to review the HTML for debugging.</value>
        [DataMember(Name = "saveHtml", EmitDefaultValue = true)]
        public bool SaveHtml { get; set; }

        /// <summary>
        /// If enabled, the crawler stores full transformed HTML of all pages found to the default key-value store and saves links to the files as &#x60;htmlUrl&#x60; field in the output dataset. Storing HTML in key-value store is preferred to storing it into the dataset with the &#x60;saveHtml&#x60; option, because there&#39;s no size limit and it&#39;s easier for debugging as you can easily view the HTML.
        /// </summary>
        /// <value>If enabled, the crawler stores full transformed HTML of all pages found to the default key-value store and saves links to the files as &#x60;htmlUrl&#x60; field in the output dataset. Storing HTML in key-value store is preferred to storing it into the dataset with the &#x60;saveHtml&#x60; option, because there&#39;s no size limit and it&#39;s easier for debugging as you can easily view the HTML.</value>
        [DataMember(Name = "saveHtmlAsFile", EmitDefaultValue = true)]
        public bool SaveHtmlAsFile { get; set; }

        /// <summary>
        /// If enabled, the crawler converts the transformed HTML of all pages found to Markdown, and stores it under the &#x60;markdown&#x60; field in the output dataset.
        /// </summary>
        /// <value>If enabled, the crawler converts the transformed HTML of all pages found to Markdown, and stores it under the &#x60;markdown&#x60; field in the output dataset.</value>
        [DataMember(Name = "saveMarkdown", EmitDefaultValue = true)]
        public bool SaveMarkdown { get; set; }

        /// <summary>
        /// If enabled, the crawler downloads files linked from the web pages, as long as their URL has one of the following file extensions: PDF, DOC, DOCX, XLS, XLSX, and CSV. Note that unlike web pages, the files are downloaded regardless if they are under **Start URLs** or not. The files are stored to the default key-value store, and metadata about them to the output dataset, similarly as for web pages.
        /// </summary>
        /// <value>If enabled, the crawler downloads files linked from the web pages, as long as their URL has one of the following file extensions: PDF, DOC, DOCX, XLS, XLSX, and CSV. Note that unlike web pages, the files are downloaded regardless if they are under **Start URLs** or not. The files are stored to the default key-value store, and metadata about them to the output dataset, similarly as for web pages.</value>
        [DataMember(Name = "saveFiles", EmitDefaultValue = true)]
        public bool SaveFiles { get; set; }

        /// <summary>
        /// If enabled, the crawler stores a screenshot for each article page to the default key-value store. The link to the screenshot is stored under the &#x60;screenshotUrl&#x60; field in the output dataset. It is useful for debugging, but reduces performance and increases storage costs.  Note that this feature only works with the &#x60;playwright:firefox&#x60; crawler type.
        /// </summary>
        /// <value>If enabled, the crawler stores a screenshot for each article page to the default key-value store. The link to the screenshot is stored under the &#x60;screenshotUrl&#x60; field in the output dataset. It is useful for debugging, but reduces performance and increases storage costs.  Note that this feature only works with the &#x60;playwright:firefox&#x60; crawler type.</value>
        [DataMember(Name = "saveScreenshots", EmitDefaultValue = true)]
        public bool SaveScreenshots { get; set; }

        /// <summary>
        /// The maximum number of resulting web pages to store. The crawler will automatically finish after reaching this number. This setting is useful to prevent accidental crawler runaway. If both **Max pages** and **Max results** are defined, then the crawler will finish when the first limit is reached. Note that the crawler skips pages with the canonical URL of a page that has already been crawled, hence it might crawl more pages than there are results.
        /// </summary>
        /// <value>The maximum number of resulting web pages to store. The crawler will automatically finish after reaching this number. This setting is useful to prevent accidental crawler runaway. If both **Max pages** and **Max results** are defined, then the crawler will finish when the first limit is reached. Note that the crawler skips pages with the canonical URL of a page that has already been crawled, hence it might crawl more pages than there are results.</value>
        [DataMember(Name = "maxResults", EmitDefaultValue = false)]
        public int MaxResults { get; set; }

        /// <summary>
        /// Removed in favor of the &#x60;htmlTransformer&#x60; option. Will be removed soon.
        /// </summary>
        /// <value>Removed in favor of the &#x60;htmlTransformer&#x60; option. Will be removed soon.</value>
        [DataMember(Name = "textExtractor", EmitDefaultValue = false)]
        public string TextExtractor { get; set; }

        /// <summary>
        /// The least amount of content (as a percentage) change after the initial load required to consider the pages client-side rendered
        /// </summary>
        /// <value>The least amount of content (as a percentage) change after the initial load required to consider the pages client-side rendered</value>
        [DataMember(Name = "clientSideMinChangePercentage", EmitDefaultValue = false)]
        public int ClientSideMinChangePercentage { get; set; }

        /// <summary>
        /// How often should the adaptive attempt to detect page rendering type
        /// </summary>
        /// <value>How often should the adaptive attempt to detect page rendering type</value>
        [DataMember(Name = "renderingTypeDetectionPercentage", EmitDefaultValue = false)]
        public int RenderingTypeDetectionPercentage { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class InputSchema {\n");
            sb.Append("  StartUrls: ").Append(StartUrls).Append("\n");
            sb.Append("  UseSitemaps: ").Append(UseSitemaps).Append("\n");
            sb.Append("  CrawlerType: ").Append(CrawlerType).Append("\n");
            sb.Append("  IncludeUrlGlobs: ").Append(IncludeUrlGlobs).Append("\n");
            sb.Append("  ExcludeUrlGlobs: ").Append(ExcludeUrlGlobs).Append("\n");
            sb.Append("  KeepUrlFragments: ").Append(KeepUrlFragments).Append("\n");
            sb.Append("  IgnoreCanonicalUrl: ").Append(IgnoreCanonicalUrl).Append("\n");
            sb.Append("  MaxCrawlDepth: ").Append(MaxCrawlDepth).Append("\n");
            sb.Append("  MaxCrawlPages: ").Append(MaxCrawlPages).Append("\n");
            sb.Append("  InitialConcurrency: ").Append(InitialConcurrency).Append("\n");
            sb.Append("  MaxConcurrency: ").Append(MaxConcurrency).Append("\n");
            sb.Append("  InitialCookies: ").Append(InitialCookies).Append("\n");
            sb.Append("  ProxyConfiguration: ").Append(ProxyConfiguration).Append("\n");
            sb.Append("  MaxSessionRotations: ").Append(MaxSessionRotations).Append("\n");
            sb.Append("  MaxRequestRetries: ").Append(MaxRequestRetries).Append("\n");
            sb.Append("  RequestTimeoutSecs: ").Append(RequestTimeoutSecs).Append("\n");
            sb.Append("  MinFileDownloadSpeedKBps: ").Append(MinFileDownloadSpeedKBps).Append("\n");
            sb.Append("  DynamicContentWaitSecs: ").Append(DynamicContentWaitSecs).Append("\n");
            sb.Append("  WaitForSelector: ").Append(WaitForSelector).Append("\n");
            sb.Append("  SoftWaitForSelector: ").Append(SoftWaitForSelector).Append("\n");
            sb.Append("  MaxScrollHeightPixels: ").Append(MaxScrollHeightPixels).Append("\n");
            sb.Append("  KeepElementsCssSelector: ").Append(KeepElementsCssSelector).Append("\n");
            sb.Append("  RemoveElementsCssSelector: ").Append(RemoveElementsCssSelector).Append("\n");
            sb.Append("  RemoveCookieWarnings: ").Append(RemoveCookieWarnings).Append("\n");
            sb.Append("  ExpandIframes: ").Append(ExpandIframes).Append("\n");
            sb.Append("  ClickElementsCssSelector: ").Append(ClickElementsCssSelector).Append("\n");
            sb.Append("  HtmlTransformer: ").Append(HtmlTransformer).Append("\n");
            sb.Append("  ReadableTextCharThreshold: ").Append(ReadableTextCharThreshold).Append("\n");
            sb.Append("  AggressivePrune: ").Append(AggressivePrune).Append("\n");
            sb.Append("  DebugMode: ").Append(DebugMode).Append("\n");
            sb.Append("  DebugLog: ").Append(DebugLog).Append("\n");
            sb.Append("  SaveHtml: ").Append(SaveHtml).Append("\n");
            sb.Append("  SaveHtmlAsFile: ").Append(SaveHtmlAsFile).Append("\n");
            sb.Append("  SaveMarkdown: ").Append(SaveMarkdown).Append("\n");
            sb.Append("  SaveFiles: ").Append(SaveFiles).Append("\n");
            sb.Append("  SaveScreenshots: ").Append(SaveScreenshots).Append("\n");
            sb.Append("  MaxResults: ").Append(MaxResults).Append("\n");
            sb.Append("  TextExtractor: ").Append(TextExtractor).Append("\n");
            sb.Append("  ClientSideMinChangePercentage: ").Append(ClientSideMinChangePercentage).Append("\n");
            sb.Append("  RenderingTypeDetectionPercentage: ").Append(RenderingTypeDetectionPercentage).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            // MaxCrawlDepth (int) minimum
            if (this.MaxCrawlDepth < (int)0)
            {
                yield return new ValidationResult("Invalid value for MaxCrawlDepth, must be a value greater than or equal to 0.", new[] { "MaxCrawlDepth" });
            }

            // MaxCrawlPages (int) minimum
            if (this.MaxCrawlPages < (int)0)
            {
                yield return new ValidationResult("Invalid value for MaxCrawlPages, must be a value greater than or equal to 0.", new[] { "MaxCrawlPages" });
            }

            // InitialConcurrency (int) maximum
            if (this.InitialConcurrency > (int)999)
            {
                yield return new ValidationResult("Invalid value for InitialConcurrency, must be a value less than or equal to 999.", new[] { "InitialConcurrency" });
            }

            // InitialConcurrency (int) minimum
            if (this.InitialConcurrency < (int)0)
            {
                yield return new ValidationResult("Invalid value for InitialConcurrency, must be a value greater than or equal to 0.", new[] { "InitialConcurrency" });
            }

            // MaxConcurrency (int) maximum
            if (this.MaxConcurrency > (int)999)
            {
                yield return new ValidationResult("Invalid value for MaxConcurrency, must be a value less than or equal to 999.", new[] { "MaxConcurrency" });
            }

            // MaxConcurrency (int) minimum
            if (this.MaxConcurrency < (int)1)
            {
                yield return new ValidationResult("Invalid value for MaxConcurrency, must be a value greater than or equal to 1.", new[] { "MaxConcurrency" });
            }

            // MaxSessionRotations (int) maximum
            if (this.MaxSessionRotations > (int)20)
            {
                yield return new ValidationResult("Invalid value for MaxSessionRotations, must be a value less than or equal to 20.", new[] { "MaxSessionRotations" });
            }

            // MaxSessionRotations (int) minimum
            if (this.MaxSessionRotations < (int)0)
            {
                yield return new ValidationResult("Invalid value for MaxSessionRotations, must be a value greater than or equal to 0.", new[] { "MaxSessionRotations" });
            }

            // MaxRequestRetries (int) maximum
            if (this.MaxRequestRetries > (int)20)
            {
                yield return new ValidationResult("Invalid value for MaxRequestRetries, must be a value less than or equal to 20.", new[] { "MaxRequestRetries" });
            }

            // MaxRequestRetries (int) minimum
            if (this.MaxRequestRetries < (int)0)
            {
                yield return new ValidationResult("Invalid value for MaxRequestRetries, must be a value greater than or equal to 0.", new[] { "MaxRequestRetries" });
            }

            // RequestTimeoutSecs (int) maximum
            if (this.RequestTimeoutSecs > (int)600)
            {
                yield return new ValidationResult("Invalid value for RequestTimeoutSecs, must be a value less than or equal to 600.", new[] { "RequestTimeoutSecs" });
            }

            // RequestTimeoutSecs (int) minimum
            if (this.RequestTimeoutSecs < (int)1)
            {
                yield return new ValidationResult("Invalid value for RequestTimeoutSecs, must be a value greater than or equal to 1.", new[] { "RequestTimeoutSecs" });
            }

            // MaxScrollHeightPixels (int) minimum
            if (this.MaxScrollHeightPixels < (int)0)
            {
                yield return new ValidationResult("Invalid value for MaxScrollHeightPixels, must be a value greater than or equal to 0.", new[] { "MaxScrollHeightPixels" });
            }

            // MaxResults (int) minimum
            if (this.MaxResults < (int)0)
            {
                yield return new ValidationResult("Invalid value for MaxResults, must be a value greater than or equal to 0.", new[] { "MaxResults" });
            }

            // ClientSideMinChangePercentage (int) minimum
            if (this.ClientSideMinChangePercentage < (int)1)
            {
                yield return new ValidationResult("Invalid value for ClientSideMinChangePercentage, must be a value greater than or equal to 1.", new[] { "ClientSideMinChangePercentage" });
            }

            // RenderingTypeDetectionPercentage (int) maximum
            if (this.RenderingTypeDetectionPercentage > (int)100)
            {
                yield return new ValidationResult("Invalid value for RenderingTypeDetectionPercentage, must be a value less than or equal to 100.", new[] { "RenderingTypeDetectionPercentage" });
            }

            // RenderingTypeDetectionPercentage (int) minimum
            if (this.RenderingTypeDetectionPercentage < (int)1)
            {
                yield return new ValidationResult("Invalid value for RenderingTypeDetectionPercentage, must be a value greater than or equal to 1.", new[] { "RenderingTypeDetectionPercentage" });
            }

            yield break;
        }
    }

}

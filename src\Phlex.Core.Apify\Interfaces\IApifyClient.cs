﻿using Apify.SDK.Model;
using Phlex.Core.Apify.Models;
using Task = System.Threading.Tasks.Task;

namespace Phlex.Core.Apify.Interfaces;

public interface IApifyClient
{
    
    /// <summary>
    /// Get a list of tasks owned by the user (identified by api key)
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<ActorTasksGet200Response> GetTasksAsync(CancellationToken cancellationToken = default);


    /// <summary>
    /// Creates a task and returns the TaskId. Throws exception if taskName already exists.
    /// </summary>
    /// <param name="taskName"></param>
    /// <param name="urls"></param>
    /// <param name="crawlDepth"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<string> CreateTaskAsync(string taskName, List<string> urls, int crawlDepth, CancellationToken cancellationToken = default);


    /// <summary>
    /// Creates a task. All options of the default webScraper actor can be specified within the WebScraperInput object.
    /// Throws exception if taskName already exists.
    /// </summary>
    /// <param name="taskName"></param>
    /// <param name="input"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<string> CreateTaskAsync(string taskName, WebScraperInput input, CancellationToken cancellationToken = default);

    
    /// <summary>
    /// Runs a task and returns the runId
    /// </summary>
    /// <param name="taskId"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<string> RunTaskAsync(string taskId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Update a task with additional URLs
    /// </summary>
    /// <param name="taskId">The Task Id</param>
    /// <param name="urls">List of URLs to add to the task</param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<bool> UpdateTaskInputUrlsAsync(string taskId, List<string> urls, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Delete the task identified by taskID
    /// </summary>
    /// <param name="taskId"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<string> DeleteTaskAsync(string taskId, CancellationToken cancellationToken = default);


    /// <summary>
    /// Delete a run and all associated information (e.g. datasets, key-value stores)
    /// </summary>
    /// <param name="runId">Run Id</param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task DeleteActorRunAsync(string runId, CancellationToken cancellationToken = default);


    /// <summary>
    /// Returns information on the store specified by storeID 
    /// </summary>
    /// <param name="storeId"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<GetStoreResponse> GetKeyValueStoreAsync(string storeId, CancellationToken cancellationToken = default);


    /// <summary>
    /// Returns information on the dataset specified by datasetId
    /// </summary>
    /// <param name="datasetId"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<DatasetResponse> GetDatasetAsync(string datasetId, CancellationToken cancellationToken = default);


    /// <summary>
    /// Transfer files from a given Dataset and Key-Value Store
    /// </summary>
    /// <param name="dataSetId">Dataset Id</param>
    /// <param name="keyValueStoreId">Key-Value Store Id</param>
    /// <param name="storage">Download storage (e.g. Azure Blob Implementation)</param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task TransferFilesAsync(string dataSetId, string keyValueStoreId, IDownloadStorage storage, CancellationToken cancellationToken = default);


    /// <summary>
    /// Get stores owned by user (identified by api key)
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<GetListOfKeyValueStoresResponse> GetKeyValueStoresAsync(CancellationToken cancellationToken = default);


    /// <summary>
    /// Get a list of keys in a specific store
    /// </summary>
    /// <param name="storeId"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<GetListOfKeysResponse> GetKeyValueStoreKeysAsync(string storeId, CancellationToken cancellationToken = default);


    /// <summary>
    /// Schedule a task
    /// </summary>
    /// <param name="taskId"></param>
    /// <param name="scheduleName"></param>
    /// <param name="cronExpression"></param>
    /// <param name="timeZone"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<ScheduleResponse> CreateSchedulesAsync(string taskId, string scheduleName, string cronExpression, string timeZone, CancellationToken cancellationToken = default);


    /// <summary>
    /// Get a list (JSON) of schedules
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<GetListOfSchedulesResponse> GetSchedulesAsync(CancellationToken cancellationToken = default);


    /// <summary>
    /// Deletes a schedule
    /// </summary>
    /// <param name="scheduleId"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<string> DeleteScheduleAsync(string scheduleId, CancellationToken cancellationToken = default);


    /// <summary>
    /// Get information about a specific run
    /// </summary>
    /// <param name="runId"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<RunResponse> GetRunAsync(string runId, CancellationToken cancellationToken = default);


    /// <summary>
    /// Registers a webhook receiver endpoint
    /// </summary>
    /// <param name="url"></param>
    /// <param name="taskId"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<CreateWebhookResponse> CreateWebhookAsync(string url, string taskId, CancellationToken cancellationToken = default);


    /// <summary>
    /// Deletes the webhook registration identified by webhookId
    /// </summary>
    /// <param name="webhookId"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<string> DeleteWebhookAsync(string webhookId, CancellationToken cancellationToken = default);


    /// <summary>
    /// Get a list of all registered webhooks endpoints
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<GetListOfWebhooksResponse> GetWebhooksAsync(CancellationToken cancellationToken = default);


    /// <summary>
    /// Gets the Apify log for a specific run
    /// </summary>
    /// <param name="runId"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<string> GetLogAsync(string runId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Runs a scrape task, grouping URLs by domain, configuration name, and maximum crawl depth
    /// </summary>
    /// <param name="taskName">The name of the task to be executed.</param>
    /// <param name="crawlerData">An object containing the list of URLs for scraping and the configuration for the crawler.</param>
    /// <param name="webhookUrl">The URL of the webhook to be notified when the task is completed.</param>
    /// <param name="callbackFunction">A callback function that will be executed for each batch of URLs, providing a BatchInfo object as a parameter.</param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task RunScrapeAsync(
      string taskName,
      ICrawlerData crawlerData,
      string webhookUrl,
      Func<BatchInfo, Task> callbackFunction,
      CancellationToken cancellationToken = default);


    /// <summary>
    /// Retrieves the results of a completed actor run including status, status message, 
    /// started at, finished at information, and the actual run results data
    /// </summary>
    /// <param name="runId">The ID of the actor run to retrieve results for</param>
    /// <param name="cancellationToken"></param>
    /// <returns>ActorRunResult containing run details and results in a strongly-typed format</returns>
    Task<ActorRunResult> GetActorRunResultsAsync(string runId, CancellationToken cancellationToken = default);


    /// <summary>
    /// Retrieves a list of dataset items for the specified dataset ID.
    /// </summary>
    /// <param name="datasetId">The ID of the dataset to retrieve items from.</param>
    /// <param name="cancellationToken"></param>
    /// <returns>A list of dataset items.</returns>
    Task<List<DatasetItem?>> GetDatasetItemsAsync(string datasetId, CancellationToken cancellationToken = default);


    /// <summary>
    /// Clean up resources used by actor run
    /// </summary>
    /// <param name="actorId">The Id of the actor</param>
    /// <param name="age">A timespan corresponding to the age of items to delete</param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task CleanupActorRunsAsync(string actorId, TimeSpan age, CancellationToken cancellationToken = default);
}

/*
 * Apify API
 *
 *  > **UPDATE 2025-01-14:** > We have rolled out this new Apify API Documentation. In case of any issues, please [report here](https://github.com/apify/apify-docs/issues). > The old API Documentation is still [available here](https://docs.apify.com/api/v2-old).  The Apify API (version 2) provides programmatic access to the [Apify platform](https://docs.apify.com). The API is organized around [RESTful](https://en.wikipedia.org/wiki/Representational_state_transfer) HTTP endpoints.  You can download the complete OpenAPI schema of Apify API in the [YAML](http://docs.apify.com/api/openapi.yaml) or [JSON](http://docs.apify.com/api/openapi.json) formats. The source code is also available on [GitHub](https://github.com/apify/apify-docs/tree/master/apify-api/openapi).  All requests and responses (including errors) are encoded in [JSON](http://www.json.org/) format with UTF-8 encoding, with a few exceptions that are explicitly described in the reference.  To access the API using [Node.js](https://nodejs.org/en/), we recommend the [`apify-client`](https://docs.apify.com/api/client/js) [NPM package](https://www.npmjs.com/package/apify-client).  To access the API using [Python](https://www.python.org/), we recommend the [`apify-client`](https://docs.apify.com/api/client/python) [PyPI package](https://pypi.org/project/apify-client/). The clients' functions correspond to the API endpoints and have the same parameters. This simplifies development of apps that depend on the Apify platform.  **Note:** All requests with JSON payloads need to specify the `Content-Type: application/json` HTTP header! All API endpoints support the `method` query parameter that can override the HTTP method. For example, if you want to call a POST endpoint using a GET request, simply add the query parameter `method=POST` to the URL and send the GET request. This feature is especially useful if you want to call Apify API endpoints from services that can only send GET requests.  ## Authentication <span id=\"/introduction/authentication\"></span>  You can find your API token on the [Integrations](https://console.apify.com/account#/integrations) page in the Apify Console.  To use your token in a request, either:  - Add the token to your request's `Authorization` header as `Bearer <token>`. E.g., `Authorization: Bearer xxxxxxx`. [More info](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Authorization). (Recommended). - Add it as the `token` parameter to your request URL. (Less secure).  Using your token in the request header is more secure than using it as a URL parameter because URLs are often stored in browser history and server logs. This creates a chance for someone unauthorized to access your API token.  **Do not share your API token or password with untrusted parties.**  For more information, see our [integrations](https://docs.apify.com/platform/integrations) documentation.  ## Basic usage <span id=\"/introduction/basic-usage\"></span>  To run an Actor, send a POST request to the [Run Actor](#/reference/actors/run-collection/run-actor) endpoint using either the Actor ID code (e.g. `vKg4IjxZbEYTYeW8T`) or its name (e.g. `janedoe~my-actor`):  `https://api.apify.com/v2/acts/[actor_id]/runs`  If the Actor is not runnable anonymously, you will receive a 401 or 403 [response code](https://developer.mozilla.org/en-US/docs/Web/HTTP/Status). This means you need to add your [secret API token](https://console.apify.com/account#/integrations) to the request's `Authorization` header ([recommended](#/introduction/authentication)) or as a URL query parameter `?token=[your_token]` (less secure).  Optionally, you can include the query parameters described in the [Run Actor](#/reference/actors/run-collection/run-actor) section to customize your run.  If you're using Node.js, the best way to run an Actor is using the `Apify.call()` method from the [Apify SDK](https://sdk.apify.com/docs/api/apify#apifycallactid-input-options). It runs the Actor using the account you are currently logged into (determined by the [secret API token](https://console.apify.com/account#/integrations)). The result is an [Actor run object](https://sdk.apify.com/docs/typedefs/actor-run) and its output (if any).  A typical workflow is as follows:  1. Run an Actor or task using the [Run Actor](#/reference/actors/run-collection/run-actor) or [Run task](#/reference/actor-tasks/run-collection/run-task) API endpoints. 2. Monitor the Actor run by periodically polling its progress using the [Get run](#/reference/actor-runs/run-object-and-its-storages/get-run) API endpoint. 3. Fetch the results from the [Get items](#/reference/datasets/item-collection/get-items) API endpoint using the `defaultDatasetId`, which you receive in the Run request response. Additional data may be stored in a key-value store. You can fetch them from the [Get record](#/reference/key-value-stores/record/get-record) API endpoint using the `defaultKeyValueStoreId` and the store's `key`.  **Note**: Instead of periodic polling, you can also run your [Actor](#/reference/actors/run-actor-synchronously) or [task](#/reference/actor-tasks/runs-collection/run-task-synchronously) synchronously. This will ensure that the request waits for 300 seconds (5 minutes) for the run to finish and returns its output. If the run takes longer, the request will time out and throw an error.  ## Response structure <span id=\"/introduction/response-structure\"></span>  Most API endpoints return a JSON object with the `data` property:  ``` {     \"data\": {         ...     } } ```  However, there are a few explicitly described exceptions, such as Dataset [Get items](#/reference/datasets/item-collection/get-items) or Key-value store [Get record](#/reference/key-value-stores/record/get-record) API endpoints, which return data in other formats. In case of an error, the response has the HTTP status code in the range of 4xx or 5xx and the `data` property is replaced with `error`. For example:  ``` {     \"error\": {         \"type\": \"record-not-found\",         \"message\": \"Store was not found.\"     } } ```  See [Errors](#/introduction/errors) for more details.  ## Pagination <span id=\"/introduction/pagination\"></span>  All API endpoints that return a list of records (e.g. [Get list of Actors](#/reference/actors/actor-collection/get-list-of-actors)) enforce pagination in order to limit the size of their responses.  Most of these API endpoints are paginated using the `offset` and `limit` query parameters. The only exception is [Get list of keys](#/reference/key-value-stores/key-collection/get-list-of-keys), which is paginated using the `exclusiveStartKey` query parameter.  **IMPORTANT**: Each API endpoint that supports pagination enforces a certain maximum value for the `limit` parameter, in order to reduce the load on Apify servers. The maximum limit could change in future so you should never rely on a specific value and check the responses of these API endpoints.  ### Using offset <span id=\"/introduction/pagination/using-offset\"></span>  Most API endpoints that return a list of records enable pagination using the following query parameters:  <table>   <tr>     <td><code>limit</code></td>     <td>Limits the response to contain a specific maximum number of items, e.g. <code>limit=20</code>.</td>   </tr>   <tr>     <td><code>offset</code></td>     <td>Skips a number of items from the beginning of the list, e.g. <code>offset=100</code>.</td>   </tr>   <tr>     <td><code>desc</code></td>     <td>     By default, items are sorted in the order in which they were created or added to the list.     This feature is useful when fetching all the items, because it ensures that items     created after the client started the pagination will not be skipped.     If you specify the <code>desc=1</code> parameter, the items will be returned in the reverse order,     i.e. from the newest to the oldest items.     </td>   </tr> </table>  The response of these API endpoints is always a JSON object with the following structure:  ``` {     \"data\": {         \"total\": 2560,         \"offset\": 250,         \"limit\": 1000,         \"count\": 1000,         \"desc\": false,         \"items\": [             { 1st object },             { 2nd object },             ...             { 1000th object }         ]     } } ```  The following table describes the meaning of the response properties:  <table>   <tr>     <th>Property</th>     <th>Description</th>   </tr>   <tr>     <td><code>total</code></td>     <td>The total number of items available in the list.</td>   </tr>   <tr>     <td><code>offset</code></td>     <td>The number of items that were skipped at the start.     This is equal to the <code>offset</code> query parameter if it was provided, otherwise it is <code>0</code>.</td>   </tr>   <tr>     <td><code>limit</code></td>     <td>The maximum number of items that can be returned in the HTTP response.     It equals to the <code>limit</code> query parameter if it was provided or     the maximum limit enforced for the particular API endpoint, whichever is smaller.</td>   </tr>   <tr>     <td><code>count</code></td>     <td>The actual number of items returned in the HTTP response.</td>   </tr>   <tr>     <td><code>desc</code></td>     <td><code>true</code> if data were requested in descending order and <code>false</code> otherwise.</td>   </tr>   <tr>     <td><code>items</code></td>     <td>An array of requested items.</td>   </tr> </table>  ### Using key <span id=\"/introduction/pagination/using-key\"></span>  The records in the [key-value store](https://docs.apify.com/platform/storage/key-value-store) are not ordered based on numerical indexes, but rather by their keys in the UTF-8 binary order. Therefore the [Get list of keys](#/reference/key-value-stores/key-collection/get-list-of-keys) API endpoint only supports pagination using the following query parameters:  <table>   <tr>     <td><code>limit</code></td>     <td>Limits the response to contain a specific maximum number items, e.g. <code>limit=20</code>.</td>   </tr>   <tr>     <td><code>exclusiveStartKey</code></td>     <td>Skips all records with keys up to the given key including the given key,     in the UTF-8 binary order.</td>   </tr> </table>  The response of the API endpoint is always a JSON object with following structure:  ``` {     \"data\": {         \"limit\": 1000,         \"isTruncated\": true,         \"exclusiveStartKey\": \"my-key\",         \"nextExclusiveStartKey\": \"some-other-key\",         \"items\": [             { 1st object },             { 2nd object },             ...             { 1000th object }         ]     } } ```  The following table describes the meaning of the response properties:  <table>   <tr>     <th>Property</th>     <th>Description</th>   </tr>   <tr>     <td><code>limit</code></td>     <td>The maximum number of items that can be returned in the HTTP response.     It equals to the <code>limit</code> query parameter if it was provided or     the maximum limit enforced for the particular endpoint, whichever is smaller.</td>   </tr>   <tr>     <td><code>isTruncated</code></td>     <td><code>true</code> if there are more items left to be queried. Otherwise <code>false</code>.</td>   </tr>   <tr>     <td><code>exclusiveStartKey</code></td>     <td>The last key that was skipped at the start. Is `null` for the first page.</td>   </tr>   <tr>     <td><code>nextExclusiveStartKey</code></td>     <td>The value for the <code>exclusiveStartKey</code> parameter to query the next page of items.</td>   </tr> </table>  ## Errors <span id=\"/introduction/errors\"></span>  The Apify API uses common HTTP status codes: `2xx` range for success, `4xx` range for errors caused by the caller (invalid requests) and `5xx` range for server errors (these are rare). Each error response contains a JSON object defining the `error` property, which is an object with the `type` and `message` properties that contain the error code and a human-readable error description, respectively.  For example:  ``` {     \"error\": {         \"type\": \"record-not-found\",         \"message\": \"Store was not found.\"     } } ```  Here is the table of the most common errors that can occur for many API endpoints:  <table>   <tr>     <th>status</th>     <th>type</th>     <th>message</th>   </tr>   <tr>     <td><code>400</code></td>     <td><code>invalid-request</code></td>     <td>POST data must be a JSON object</td>   </tr>   <tr>     <td><code>400</code></td>     <td><code>invalid-value</code></td>     <td>Invalid value provided: Comments required</td>   </tr>   <tr>     <td><code>400</code></td>     <td><code>invalid-record-key</code></td>     <td>Record key contains invalid character</td>   </tr>   <tr>     <td><code>401</code></td>     <td><code>token-not-provided</code></td>     <td>Authentication token was not provided</td>   </tr>   <tr>     <td><code>404</code></td>     <td><code>record-not-found</code></td>     <td>Store was not found</td>   </tr>   <tr>     <td><code>429</code></td>     <td><code>rate-limit-exceeded</code></td>     <td>You have exceeded the rate limit of 30 requests per second</td>   </tr>   <tr>     <td><code>405</code></td>     <td><code>method-not-allowed</code></td>     <td>This API endpoint can only be accessed using the following HTTP methods: OPTIONS, POST</td>   </tr> </table>  ## Rate limiting <span id=\"/introduction/rate-limiting\"></span>  All API endpoints limit the rate of requests in order to prevent overloading of Apify servers by misbehaving clients.  There are two kinds of rate limits - a global rate limit and a per-resource rate limit.  ### Global rate limit <span id=\"/introduction/rate-limiting/global-rate-limit\"></span>  The global rate limit is set to _250 000 requests per minute_. For [authenticated](#/introduction/authentication) requests, it is counted per user, and for unauthenticated requests, it is counted per IP address.  ### Per-resource rate limit <span id=\"/introduction/rate-limiting/per-resource-rate-limit\"></span>  The default per-resource rate limit is _30 requests per second per resource_, which in this context means a single Actor, a single Actor run, a single dataset, single key-value store etc. The default rate limit is applied to every API endpoint except a few select ones, which have higher rate limits. Each API endpoint returns its rate limit in `X-RateLimit-Limit` header.  These endpoints have a rate limit of _100 requests per second per resource_:  * CRUD ([get](#/reference/key-value-stores/record/get-record),   [put](#/reference/key-value-stores/record/put-record),   [delete](#/reference/key-value-stores/record/delete-record))   operations on key-value store records  These endpoints have a rate limit of _200 requests per second per resource_: * [Run Actor](#/reference/actors/run-collection/run-actor) * [Run Actor task asynchronously](#/reference/actor-tasks/runs-collection/run-task-asynchronously) * [Run Actor task synchronously](#/reference/actor-tasks/runs-collection/run-task-synchronously) * [Metamorph Actor run](#/reference/actors/metamorph-run/metamorph-run) * [Push items](#/reference/datasets/item-collection/put-items) to dataset * CRUD   ([add](#/reference/request-queues/request-collection/add-request),   [get](#/reference/request-queues/request-collection/get-request),   [update](#/reference/request-queues/request-collection/update-request),   [delete](#/reference/request-queues/request-collection/delete-request))   operations on requests in request queues  ### Rate limit exceeded errors <span id=\"/introduction/rate-limiting/rate-limit-exceeded-errors\"></span>  If the client is sending too many requests, the API endpoints respond with the HTTP status code `429 Too Many Requests` and the following body:  ``` {     \"error\": {         \"type\": \"rate-limit-exceeded\",         \"message\": \"You have exceeded the rate limit of ... requests per second\"     } } ```  ### Retrying rate-limited requests with exponential backoff <span id=\"/introduction/rate-limiting/retrying-rate-limited-requests-with-exponential-backoff\"></span>  If the client receives the rate limit error, it should wait a certain period of time and then retry the request. If the error happens again, the client should double the wait period and retry the request, and so on. This algorithm is known as _exponential backoff_ and it can be described using the following pseudo-code:  1. Define a variable `DELAY=500` 2. Send the HTTP request to the API endpoint 3. If the response has status code not equal to `429` then you are done. Otherwise:    * Wait for a period of time chosen randomly from the interval `DELAY` to `2*DELAY` milliseconds    * Double the future wait period by setting `DELAY = 2*DELAY`    * Continue with step 2  If all requests sent by the client implement the above steps, the client will automatically use the maximum available bandwidth for its requests.  Note that the Apify API clients [for JavaScript](https://docs.apify.com/api/client/js) and [for Python](https://docs.apify.com/api/client/python) use the exponential backoff algorithm transparently, so that you do not need to worry about it.  ## Referring to resources <span id=\"/introduction/referring-to-resources\"></span>  There are three main ways to refer to a resource you're accessing via API.  - the resource ID (e.g. `iKkPcIgVvwmztduf8`) - `username~resourcename` - when using this access method, you will need to use your API token, and access will only work if you have the correct permissions. - `~resourcename` - for this, you need to use an API token, and the `resourcename` refers to a resource in the API token owner's account. 
 *
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Mime;
using Apify.SDK.Client;
using Apify.SDK.Model;

namespace Apify.SDK.Api
{

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IStorageDatasetsApiSync : IApiAccessor
    {
        #region Synchronous Operations
        /// <summary>
        /// Delete dataset
        /// </summary>
        /// <remarks>
        /// Deletes a specific dataset.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <returns></returns>
        void DatasetDelete(string datasetId);

        /// <summary>
        /// Delete dataset
        /// </summary>
        /// <remarks>
        /// Deletes a specific dataset.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <returns>ApiResponse of Object(void)</returns>
        ApiResponse<Object> DatasetDeleteWithHttpInfo(string datasetId);
        /// <summary>
        /// Get dataset
        /// </summary>
        /// <remarks>
        /// Returns dataset object for given dataset ID.  :::note  Keep in mind that attributes &#x60;itemCount&#x60; and &#x60;cleanItemCount&#x60; are not propagated right away after data are pushed into a dataset.  :::  There is a short period (up to 5 seconds) during which these counters may not match with exact counts in dataset items. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="token">API authentication token. It is required only when using the &#x60;username~dataset-name&#x60; format for &#x60;datasetId&#x60;.  (optional)</param>
        /// <returns>DatasetResponse</returns>
        DatasetResponse DatasetGet(string datasetId, string? token = default(string?));

        /// <summary>
        /// Get dataset
        /// </summary>
        /// <remarks>
        /// Returns dataset object for given dataset ID.  :::note  Keep in mind that attributes &#x60;itemCount&#x60; and &#x60;cleanItemCount&#x60; are not propagated right away after data are pushed into a dataset.  :::  There is a short period (up to 5 seconds) during which these counters may not match with exact counts in dataset items. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="token">API authentication token. It is required only when using the &#x60;username~dataset-name&#x60; format for &#x60;datasetId&#x60;.  (optional)</param>
        /// <returns>ApiResponse of DatasetResponse</returns>
        ApiResponse<DatasetResponse> DatasetGetWithHttpInfo(string datasetId, string? token = default(string?));
        /// <summary>
        /// Get items
        /// </summary>
        /// <remarks>
        /// Returns data stored in the dataset in a desired format.  ### Response format  The format of the response depends on &lt;code&gt;format&lt;/code&gt; query parameter.  The &lt;code&gt;format&lt;/code&gt; parameter can have one of the following values: &lt;code&gt;json&lt;/code&gt;, &lt;code&gt;jsonl&lt;/code&gt;, &lt;code&gt;xml&lt;/code&gt;, &lt;code&gt;html&lt;/code&gt;, &lt;code&gt;csv&lt;/code&gt;, &lt;code&gt;xlsx&lt;/code&gt; and &lt;code&gt;rss&lt;/code&gt;.  The following table describes how each format is treated.  &lt;table&gt;   &lt;tr&gt;     &lt;th&gt;Format&lt;/th&gt;     &lt;th&gt;Items&lt;/th&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;json&lt;/code&gt;&lt;/td&gt;     &lt;td rowspan&#x3D;\&quot;3\&quot;&gt;The response is a JSON, JSONL or XML array of raw item objects.&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;jsonl&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;xml&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;html&lt;/code&gt;&lt;/td&gt;     &lt;td rowspan&#x3D;\&quot;3\&quot;&gt;The response is a HTML, CSV or XLSX table, where columns correspond to the     properties of the item and rows correspond to each dataset item.&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;csv&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;xlsx&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;rss&lt;/code&gt;&lt;/td&gt;     &lt;td colspan&#x3D;\&quot;2\&quot;&gt;The response is a RSS file. Each item is displayed as child elements of one     &lt;code&gt;&amp;lt;item&amp;gt;&lt;/code&gt;.&lt;/td&gt;   &lt;/tr&gt; &lt;/table&gt;  Note that CSV, XLSX and HTML tables are limited to 2000 columns and the column names cannot be longer than 200 characters. JSON, XML and RSS formats do not have such restrictions.  ### Hidden fields  The top-level fields starting with the &#x60;#&#x60; character are considered hidden. These are useful to store debugging information and can be omitted from the output by providing the &#x60;skipHidden&#x3D;1&#x60; or &#x60;clean&#x3D;1&#x60; query parameters. For example, if you store the following object to the dataset:  &#x60;&#x60;&#x60; {     productName: \&quot;iPhone Xs\&quot;,     description: \&quot;Welcome to the big screens.\&quot;     #debug: {         url: \&quot;https://www.apple.com/lae/iphone-xs/\&quot;,         crawledAt: \&quot;2019-01-21T16:06:03.683Z\&quot;     } } &#x60;&#x60;&#x60;  The &#x60;#debug&#x60; field will be considered as hidden and can be omitted from the results. This is useful to provide nice cleaned data to end users, while keeping debugging info available if needed. The Dataset object returned by the API contains the number of such clean items in the&#x60;dataset.cleanItemCount&#x60; property.  ### XML format extension  When exporting results to XML or RSS formats, the names of object properties become XML tags and the corresponding values become tag&#39;s children. For example, the following JavaScript object:  &#x60;&#x60;&#x60; {     name: \&quot;Paul Newman\&quot;,     address: [         { type: \&quot;home\&quot;, street: \&quot;21st\&quot;, city: \&quot;Chicago\&quot; },         { type: \&quot;office\&quot;, street: null, city: null }     ] } &#x60;&#x60;&#x60;  will be transformed to the following XML snippet:  &#x60;&#x60;&#x60; &lt;name&gt;Paul Newman&lt;/name&gt; &lt;address&gt;   &lt;type&gt;home&lt;/type&gt;   &lt;street&gt;21st&lt;/street&gt;   &lt;city&gt;Chicago&lt;/city&gt; &lt;/address&gt; &lt;address&gt;   &lt;type&gt;office&lt;/type&gt;   &lt;street/&gt;   &lt;city/&gt; &lt;/address&gt; &#x60;&#x60;&#x60;  If the JavaScript object contains a property named &#x60;@&#x60; then its sub-properties are exported as attributes of the parent XML element. If the parent XML element does not have any child elements then its value is taken from a JavaScript object property named &#x60;#&#x60;.  For example, the following JavaScript object:  &#x60;&#x60;&#x60; {   \&quot;address\&quot;: [{     \&quot;@\&quot;: {       \&quot;type\&quot;: \&quot;home\&quot;     },     \&quot;street\&quot;: \&quot;21st\&quot;,     \&quot;city\&quot;: \&quot;Chicago\&quot;   },   {     \&quot;@\&quot;: {       \&quot;type\&quot;: \&quot;office\&quot;     },     \&quot;#\&quot;: &#39;unknown&#39;   }] } &#x60;&#x60;&#x60;  will be transformed to the following XML snippet:  &#x60;&#x60;&#x60; &lt;address type&#x3D;\&quot;home\&quot;&gt;   &lt;street&gt;21st&lt;/street&gt;   &lt;city&gt;Chicago&lt;/city&gt; &lt;/address&gt; &lt;address type&#x3D;\&quot;office\&quot;&gt;unknown&lt;/address&gt; &#x60;&#x60;&#x60;  This feature is also useful to customize your RSS feeds generated for various websites.  By default the whole result is wrapped in a &#x60;&lt;items&gt;&#x60; element and each page object is wrapped in a &#x60;&lt;item&gt;&#x60; element. You can change this using &lt;code&gt;xmlRoot&lt;/code&gt; and &lt;code&gt;xmlRow&lt;/code&gt; url parameters.  ### Pagination  The generated response supports [pagination](#/introduction/pagination). The pagination is always performed with the granularity of a single item, regardless whether &lt;code&gt;unwind&lt;/code&gt; parameter was provided. By default, the **Items** in the response are sorted by the time they were stored to the database, therefore you can use pagination to incrementally fetch the items as they are being added. No limit exists to how many items can be returned in one response.  If you specify &#x60;desc&#x3D;1&#x60; query parameter, the results are returned in the reverse order than they were stored (i.e. from newest to oldest items). Note that only the order of **Items** is reversed, but not the order of the &#x60;unwind&#x60; array elements. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object. If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures.  For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;.  The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not.  If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output.  This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <returns>List&lt;Object&gt;</returns>
        List<Object> DatasetItemsGet(string datasetId, string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?));

        /// <summary>
        /// Get items
        /// </summary>
        /// <remarks>
        /// Returns data stored in the dataset in a desired format.  ### Response format  The format of the response depends on &lt;code&gt;format&lt;/code&gt; query parameter.  The &lt;code&gt;format&lt;/code&gt; parameter can have one of the following values: &lt;code&gt;json&lt;/code&gt;, &lt;code&gt;jsonl&lt;/code&gt;, &lt;code&gt;xml&lt;/code&gt;, &lt;code&gt;html&lt;/code&gt;, &lt;code&gt;csv&lt;/code&gt;, &lt;code&gt;xlsx&lt;/code&gt; and &lt;code&gt;rss&lt;/code&gt;.  The following table describes how each format is treated.  &lt;table&gt;   &lt;tr&gt;     &lt;th&gt;Format&lt;/th&gt;     &lt;th&gt;Items&lt;/th&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;json&lt;/code&gt;&lt;/td&gt;     &lt;td rowspan&#x3D;\&quot;3\&quot;&gt;The response is a JSON, JSONL or XML array of raw item objects.&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;jsonl&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;xml&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;html&lt;/code&gt;&lt;/td&gt;     &lt;td rowspan&#x3D;\&quot;3\&quot;&gt;The response is a HTML, CSV or XLSX table, where columns correspond to the     properties of the item and rows correspond to each dataset item.&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;csv&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;xlsx&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;rss&lt;/code&gt;&lt;/td&gt;     &lt;td colspan&#x3D;\&quot;2\&quot;&gt;The response is a RSS file. Each item is displayed as child elements of one     &lt;code&gt;&amp;lt;item&amp;gt;&lt;/code&gt;.&lt;/td&gt;   &lt;/tr&gt; &lt;/table&gt;  Note that CSV, XLSX and HTML tables are limited to 2000 columns and the column names cannot be longer than 200 characters. JSON, XML and RSS formats do not have such restrictions.  ### Hidden fields  The top-level fields starting with the &#x60;#&#x60; character are considered hidden. These are useful to store debugging information and can be omitted from the output by providing the &#x60;skipHidden&#x3D;1&#x60; or &#x60;clean&#x3D;1&#x60; query parameters. For example, if you store the following object to the dataset:  &#x60;&#x60;&#x60; {     productName: \&quot;iPhone Xs\&quot;,     description: \&quot;Welcome to the big screens.\&quot;     #debug: {         url: \&quot;https://www.apple.com/lae/iphone-xs/\&quot;,         crawledAt: \&quot;2019-01-21T16:06:03.683Z\&quot;     } } &#x60;&#x60;&#x60;  The &#x60;#debug&#x60; field will be considered as hidden and can be omitted from the results. This is useful to provide nice cleaned data to end users, while keeping debugging info available if needed. The Dataset object returned by the API contains the number of such clean items in the&#x60;dataset.cleanItemCount&#x60; property.  ### XML format extension  When exporting results to XML or RSS formats, the names of object properties become XML tags and the corresponding values become tag&#39;s children. For example, the following JavaScript object:  &#x60;&#x60;&#x60; {     name: \&quot;Paul Newman\&quot;,     address: [         { type: \&quot;home\&quot;, street: \&quot;21st\&quot;, city: \&quot;Chicago\&quot; },         { type: \&quot;office\&quot;, street: null, city: null }     ] } &#x60;&#x60;&#x60;  will be transformed to the following XML snippet:  &#x60;&#x60;&#x60; &lt;name&gt;Paul Newman&lt;/name&gt; &lt;address&gt;   &lt;type&gt;home&lt;/type&gt;   &lt;street&gt;21st&lt;/street&gt;   &lt;city&gt;Chicago&lt;/city&gt; &lt;/address&gt; &lt;address&gt;   &lt;type&gt;office&lt;/type&gt;   &lt;street/&gt;   &lt;city/&gt; &lt;/address&gt; &#x60;&#x60;&#x60;  If the JavaScript object contains a property named &#x60;@&#x60; then its sub-properties are exported as attributes of the parent XML element. If the parent XML element does not have any child elements then its value is taken from a JavaScript object property named &#x60;#&#x60;.  For example, the following JavaScript object:  &#x60;&#x60;&#x60; {   \&quot;address\&quot;: [{     \&quot;@\&quot;: {       \&quot;type\&quot;: \&quot;home\&quot;     },     \&quot;street\&quot;: \&quot;21st\&quot;,     \&quot;city\&quot;: \&quot;Chicago\&quot;   },   {     \&quot;@\&quot;: {       \&quot;type\&quot;: \&quot;office\&quot;     },     \&quot;#\&quot;: &#39;unknown&#39;   }] } &#x60;&#x60;&#x60;  will be transformed to the following XML snippet:  &#x60;&#x60;&#x60; &lt;address type&#x3D;\&quot;home\&quot;&gt;   &lt;street&gt;21st&lt;/street&gt;   &lt;city&gt;Chicago&lt;/city&gt; &lt;/address&gt; &lt;address type&#x3D;\&quot;office\&quot;&gt;unknown&lt;/address&gt; &#x60;&#x60;&#x60;  This feature is also useful to customize your RSS feeds generated for various websites.  By default the whole result is wrapped in a &#x60;&lt;items&gt;&#x60; element and each page object is wrapped in a &#x60;&lt;item&gt;&#x60; element. You can change this using &lt;code&gt;xmlRoot&lt;/code&gt; and &lt;code&gt;xmlRow&lt;/code&gt; url parameters.  ### Pagination  The generated response supports [pagination](#/introduction/pagination). The pagination is always performed with the granularity of a single item, regardless whether &lt;code&gt;unwind&lt;/code&gt; parameter was provided. By default, the **Items** in the response are sorted by the time they were stored to the database, therefore you can use pagination to incrementally fetch the items as they are being added. No limit exists to how many items can be returned in one response.  If you specify &#x60;desc&#x3D;1&#x60; query parameter, the results are returned in the reverse order than they were stored (i.e. from newest to oldest items). Note that only the order of **Items** is reversed, but not the order of the &#x60;unwind&#x60; array elements. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object. If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures.  For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;.  The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not.  If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output.  This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <returns>ApiResponse of List&lt;Object&gt;</returns>
        ApiResponse<List<Object>> DatasetItemsGetWithHttpInfo(string datasetId, string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?));
        /// <summary>
        /// Store items
        /// </summary>
        /// <remarks>
        /// Appends an item or an array of items to the end of the dataset. The POST payload is a JSON object or a JSON array of objects to save into the dataset.  If the data you attempt to store in the dataset is invalid (meaning any of the items received by the API fails the validation), the whole request is discarded and the API will return a response with status code 400. For more information about dataset schema validation, see [Dataset schema](https://docs.apify.com/platform/actors/development/actor-definition/dataset-schema/validation).  **IMPORTANT:** The limit of request payload size for the dataset is 5 MB. If the array exceeds the size, you&#39;ll need to split it into a number of smaller arrays. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="putItemsRequest"></param>
        /// <returns>Object</returns>
        Object DatasetItemsPost(string datasetId, List<PutItemsRequest> putItemsRequest);

        /// <summary>
        /// Store items
        /// </summary>
        /// <remarks>
        /// Appends an item or an array of items to the end of the dataset. The POST payload is a JSON object or a JSON array of objects to save into the dataset.  If the data you attempt to store in the dataset is invalid (meaning any of the items received by the API fails the validation), the whole request is discarded and the API will return a response with status code 400. For more information about dataset schema validation, see [Dataset schema](https://docs.apify.com/platform/actors/development/actor-definition/dataset-schema/validation).  **IMPORTANT:** The limit of request payload size for the dataset is 5 MB. If the array exceeds the size, you&#39;ll need to split it into a number of smaller arrays. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="putItemsRequest"></param>
        /// <returns>ApiResponse of Object</returns>
        ApiResponse<Object> DatasetItemsPostWithHttpInfo(string datasetId, List<PutItemsRequest> putItemsRequest);
        /// <summary>
        /// Update dataset
        /// </summary>
        /// <remarks>
        /// Updates a dataset&#39;s name using a value specified by a JSON object passed in the PUT payload. The response is the updated dataset object, as returned by the [Get dataset](#/reference/datasets/dataset-collection/get-dataset) API endpoint. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="updateDatasetRequest"></param>
        /// <returns>DatasetResponse</returns>
        DatasetResponse DatasetPut(string datasetId, UpdateDatasetRequest updateDatasetRequest);

        /// <summary>
        /// Update dataset
        /// </summary>
        /// <remarks>
        /// Updates a dataset&#39;s name using a value specified by a JSON object passed in the PUT payload. The response is the updated dataset object, as returned by the [Get dataset](#/reference/datasets/dataset-collection/get-dataset) API endpoint. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="updateDatasetRequest"></param>
        /// <returns>ApiResponse of DatasetResponse</returns>
        ApiResponse<DatasetResponse> DatasetPutWithHttpInfo(string datasetId, UpdateDatasetRequest updateDatasetRequest);
        /// <summary>
        /// Get dataset statistics
        /// </summary>
        /// <remarks>
        /// Returns statistics for given dataset.  Provides only [field statistics](https://docs.apify.com/platform/actors/development/actor-definition/dataset-schema/validation#dataset-field-statistics). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <returns>GetDatasetStatisticsResponse</returns>
        GetDatasetStatisticsResponse DatasetStatisticsGet(string datasetId);

        /// <summary>
        /// Get dataset statistics
        /// </summary>
        /// <remarks>
        /// Returns statistics for given dataset.  Provides only [field statistics](https://docs.apify.com/platform/actors/development/actor-definition/dataset-schema/validation#dataset-field-statistics). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <returns>ApiResponse of GetDatasetStatisticsResponse</returns>
        ApiResponse<GetDatasetStatisticsResponse> DatasetStatisticsGetWithHttpInfo(string datasetId);
        /// <summary>
        /// Get list of datasets
        /// </summary>
        /// <remarks>
        /// Lists all of a user&#39;s datasets.  The response is a JSON array of objects, where each object contains basic information about one dataset.  By default, the objects are sorted by the &#x60;createdAt&#x60; field in ascending order, therefore you can use pagination to incrementally fetch all datasets while new ones are still being created. To sort them in descending order, use &#x60;desc&#x3D;1&#x60; parameter. The endpoint supports pagination using &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 array elements. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="unnamed">If &#x60;true&#x60; or &#x60;1&#x60; then all the datasets are returned. By default only named datasets are returned.  (optional)</param>
        /// <returns>GetListOfDatasetsResponse</returns>
        GetListOfDatasetsResponse DatasetsGet(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), bool? unnamed = default(bool?));

        /// <summary>
        /// Get list of datasets
        /// </summary>
        /// <remarks>
        /// Lists all of a user&#39;s datasets.  The response is a JSON array of objects, where each object contains basic information about one dataset.  By default, the objects are sorted by the &#x60;createdAt&#x60; field in ascending order, therefore you can use pagination to incrementally fetch all datasets while new ones are still being created. To sort them in descending order, use &#x60;desc&#x3D;1&#x60; parameter. The endpoint supports pagination using &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 array elements. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="unnamed">If &#x60;true&#x60; or &#x60;1&#x60; then all the datasets are returned. By default only named datasets are returned.  (optional)</param>
        /// <returns>ApiResponse of GetListOfDatasetsResponse</returns>
        ApiResponse<GetListOfDatasetsResponse> DatasetsGetWithHttpInfo(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), bool? unnamed = default(bool?));
        /// <summary>
        /// Create dataset
        /// </summary>
        /// <remarks>
        /// Creates a dataset and returns its object. Keep in mind that data stored under unnamed dataset follows [data retention period](https://docs.apify.com/platform/storage#data-retention). It creates a dataset with the given name if the parameter name is used. If a dataset with the given name already exists then returns its object. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="name">Custom unique name to easily identify the dataset in the future. (optional)</param>
        /// <returns>DatasetResponse</returns>
        DatasetResponse DatasetsPost(string? name = default(string?));

        /// <summary>
        /// Create dataset
        /// </summary>
        /// <remarks>
        /// Creates a dataset and returns its object. Keep in mind that data stored under unnamed dataset follows [data retention period](https://docs.apify.com/platform/storage#data-retention). It creates a dataset with the given name if the parameter name is used. If a dataset with the given name already exists then returns its object. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="name">Custom unique name to easily identify the dataset in the future. (optional)</param>
        /// <returns>ApiResponse of DatasetResponse</returns>
        ApiResponse<DatasetResponse> DatasetsPostWithHttpInfo(string? name = default(string?));
        #endregion Synchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IStorageDatasetsApiAsync : IApiAccessor
    {
        #region Asynchronous Operations
        /// <summary>
        /// Delete dataset
        /// </summary>
        /// <remarks>
        /// Deletes a specific dataset.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of void</returns>
        System.Threading.Tasks.Task DatasetDeleteAsync(string datasetId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Delete dataset
        /// </summary>
        /// <remarks>
        /// Deletes a specific dataset.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> DatasetDeleteWithHttpInfoAsync(string datasetId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Get dataset
        /// </summary>
        /// <remarks>
        /// Returns dataset object for given dataset ID.  :::note  Keep in mind that attributes &#x60;itemCount&#x60; and &#x60;cleanItemCount&#x60; are not propagated right away after data are pushed into a dataset.  :::  There is a short period (up to 5 seconds) during which these counters may not match with exact counts in dataset items. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="token">API authentication token. It is required only when using the &#x60;username~dataset-name&#x60; format for &#x60;datasetId&#x60;.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of DatasetResponse</returns>
        System.Threading.Tasks.Task<DatasetResponse> DatasetGetAsync(string datasetId, string? token = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Get dataset
        /// </summary>
        /// <remarks>
        /// Returns dataset object for given dataset ID.  :::note  Keep in mind that attributes &#x60;itemCount&#x60; and &#x60;cleanItemCount&#x60; are not propagated right away after data are pushed into a dataset.  :::  There is a short period (up to 5 seconds) during which these counters may not match with exact counts in dataset items. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="token">API authentication token. It is required only when using the &#x60;username~dataset-name&#x60; format for &#x60;datasetId&#x60;.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (DatasetResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<DatasetResponse>> DatasetGetWithHttpInfoAsync(string datasetId, string? token = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Get items
        /// </summary>
        /// <remarks>
        /// Returns data stored in the dataset in a desired format.  ### Response format  The format of the response depends on &lt;code&gt;format&lt;/code&gt; query parameter.  The &lt;code&gt;format&lt;/code&gt; parameter can have one of the following values: &lt;code&gt;json&lt;/code&gt;, &lt;code&gt;jsonl&lt;/code&gt;, &lt;code&gt;xml&lt;/code&gt;, &lt;code&gt;html&lt;/code&gt;, &lt;code&gt;csv&lt;/code&gt;, &lt;code&gt;xlsx&lt;/code&gt; and &lt;code&gt;rss&lt;/code&gt;.  The following table describes how each format is treated.  &lt;table&gt;   &lt;tr&gt;     &lt;th&gt;Format&lt;/th&gt;     &lt;th&gt;Items&lt;/th&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;json&lt;/code&gt;&lt;/td&gt;     &lt;td rowspan&#x3D;\&quot;3\&quot;&gt;The response is a JSON, JSONL or XML array of raw item objects.&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;jsonl&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;xml&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;html&lt;/code&gt;&lt;/td&gt;     &lt;td rowspan&#x3D;\&quot;3\&quot;&gt;The response is a HTML, CSV or XLSX table, where columns correspond to the     properties of the item and rows correspond to each dataset item.&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;csv&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;xlsx&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;rss&lt;/code&gt;&lt;/td&gt;     &lt;td colspan&#x3D;\&quot;2\&quot;&gt;The response is a RSS file. Each item is displayed as child elements of one     &lt;code&gt;&amp;lt;item&amp;gt;&lt;/code&gt;.&lt;/td&gt;   &lt;/tr&gt; &lt;/table&gt;  Note that CSV, XLSX and HTML tables are limited to 2000 columns and the column names cannot be longer than 200 characters. JSON, XML and RSS formats do not have such restrictions.  ### Hidden fields  The top-level fields starting with the &#x60;#&#x60; character are considered hidden. These are useful to store debugging information and can be omitted from the output by providing the &#x60;skipHidden&#x3D;1&#x60; or &#x60;clean&#x3D;1&#x60; query parameters. For example, if you store the following object to the dataset:  &#x60;&#x60;&#x60; {     productName: \&quot;iPhone Xs\&quot;,     description: \&quot;Welcome to the big screens.\&quot;     #debug: {         url: \&quot;https://www.apple.com/lae/iphone-xs/\&quot;,         crawledAt: \&quot;2019-01-21T16:06:03.683Z\&quot;     } } &#x60;&#x60;&#x60;  The &#x60;#debug&#x60; field will be considered as hidden and can be omitted from the results. This is useful to provide nice cleaned data to end users, while keeping debugging info available if needed. The Dataset object returned by the API contains the number of such clean items in the&#x60;dataset.cleanItemCount&#x60; property.  ### XML format extension  When exporting results to XML or RSS formats, the names of object properties become XML tags and the corresponding values become tag&#39;s children. For example, the following JavaScript object:  &#x60;&#x60;&#x60; {     name: \&quot;Paul Newman\&quot;,     address: [         { type: \&quot;home\&quot;, street: \&quot;21st\&quot;, city: \&quot;Chicago\&quot; },         { type: \&quot;office\&quot;, street: null, city: null }     ] } &#x60;&#x60;&#x60;  will be transformed to the following XML snippet:  &#x60;&#x60;&#x60; &lt;name&gt;Paul Newman&lt;/name&gt; &lt;address&gt;   &lt;type&gt;home&lt;/type&gt;   &lt;street&gt;21st&lt;/street&gt;   &lt;city&gt;Chicago&lt;/city&gt; &lt;/address&gt; &lt;address&gt;   &lt;type&gt;office&lt;/type&gt;   &lt;street/&gt;   &lt;city/&gt; &lt;/address&gt; &#x60;&#x60;&#x60;  If the JavaScript object contains a property named &#x60;@&#x60; then its sub-properties are exported as attributes of the parent XML element. If the parent XML element does not have any child elements then its value is taken from a JavaScript object property named &#x60;#&#x60;.  For example, the following JavaScript object:  &#x60;&#x60;&#x60; {   \&quot;address\&quot;: [{     \&quot;@\&quot;: {       \&quot;type\&quot;: \&quot;home\&quot;     },     \&quot;street\&quot;: \&quot;21st\&quot;,     \&quot;city\&quot;: \&quot;Chicago\&quot;   },   {     \&quot;@\&quot;: {       \&quot;type\&quot;: \&quot;office\&quot;     },     \&quot;#\&quot;: &#39;unknown&#39;   }] } &#x60;&#x60;&#x60;  will be transformed to the following XML snippet:  &#x60;&#x60;&#x60; &lt;address type&#x3D;\&quot;home\&quot;&gt;   &lt;street&gt;21st&lt;/street&gt;   &lt;city&gt;Chicago&lt;/city&gt; &lt;/address&gt; &lt;address type&#x3D;\&quot;office\&quot;&gt;unknown&lt;/address&gt; &#x60;&#x60;&#x60;  This feature is also useful to customize your RSS feeds generated for various websites.  By default the whole result is wrapped in a &#x60;&lt;items&gt;&#x60; element and each page object is wrapped in a &#x60;&lt;item&gt;&#x60; element. You can change this using &lt;code&gt;xmlRoot&lt;/code&gt; and &lt;code&gt;xmlRow&lt;/code&gt; url parameters.  ### Pagination  The generated response supports [pagination](#/introduction/pagination). The pagination is always performed with the granularity of a single item, regardless whether &lt;code&gt;unwind&lt;/code&gt; parameter was provided. By default, the **Items** in the response are sorted by the time they were stored to the database, therefore you can use pagination to incrementally fetch the items as they are being added. No limit exists to how many items can be returned in one response.  If you specify &#x60;desc&#x3D;1&#x60; query parameter, the results are returned in the reverse order than they were stored (i.e. from newest to oldest items). Note that only the order of **Items** is reversed, but not the order of the &#x60;unwind&#x60; array elements. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object. If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures.  For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;.  The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not.  If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output.  This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of List&lt;Object&gt;</returns>
        System.Threading.Tasks.Task<List<Object>> DatasetItemsGetAsync(string datasetId, string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Get items
        /// </summary>
        /// <remarks>
        /// Returns data stored in the dataset in a desired format.  ### Response format  The format of the response depends on &lt;code&gt;format&lt;/code&gt; query parameter.  The &lt;code&gt;format&lt;/code&gt; parameter can have one of the following values: &lt;code&gt;json&lt;/code&gt;, &lt;code&gt;jsonl&lt;/code&gt;, &lt;code&gt;xml&lt;/code&gt;, &lt;code&gt;html&lt;/code&gt;, &lt;code&gt;csv&lt;/code&gt;, &lt;code&gt;xlsx&lt;/code&gt; and &lt;code&gt;rss&lt;/code&gt;.  The following table describes how each format is treated.  &lt;table&gt;   &lt;tr&gt;     &lt;th&gt;Format&lt;/th&gt;     &lt;th&gt;Items&lt;/th&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;json&lt;/code&gt;&lt;/td&gt;     &lt;td rowspan&#x3D;\&quot;3\&quot;&gt;The response is a JSON, JSONL or XML array of raw item objects.&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;jsonl&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;xml&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;html&lt;/code&gt;&lt;/td&gt;     &lt;td rowspan&#x3D;\&quot;3\&quot;&gt;The response is a HTML, CSV or XLSX table, where columns correspond to the     properties of the item and rows correspond to each dataset item.&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;csv&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;xlsx&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;rss&lt;/code&gt;&lt;/td&gt;     &lt;td colspan&#x3D;\&quot;2\&quot;&gt;The response is a RSS file. Each item is displayed as child elements of one     &lt;code&gt;&amp;lt;item&amp;gt;&lt;/code&gt;.&lt;/td&gt;   &lt;/tr&gt; &lt;/table&gt;  Note that CSV, XLSX and HTML tables are limited to 2000 columns and the column names cannot be longer than 200 characters. JSON, XML and RSS formats do not have such restrictions.  ### Hidden fields  The top-level fields starting with the &#x60;#&#x60; character are considered hidden. These are useful to store debugging information and can be omitted from the output by providing the &#x60;skipHidden&#x3D;1&#x60; or &#x60;clean&#x3D;1&#x60; query parameters. For example, if you store the following object to the dataset:  &#x60;&#x60;&#x60; {     productName: \&quot;iPhone Xs\&quot;,     description: \&quot;Welcome to the big screens.\&quot;     #debug: {         url: \&quot;https://www.apple.com/lae/iphone-xs/\&quot;,         crawledAt: \&quot;2019-01-21T16:06:03.683Z\&quot;     } } &#x60;&#x60;&#x60;  The &#x60;#debug&#x60; field will be considered as hidden and can be omitted from the results. This is useful to provide nice cleaned data to end users, while keeping debugging info available if needed. The Dataset object returned by the API contains the number of such clean items in the&#x60;dataset.cleanItemCount&#x60; property.  ### XML format extension  When exporting results to XML or RSS formats, the names of object properties become XML tags and the corresponding values become tag&#39;s children. For example, the following JavaScript object:  &#x60;&#x60;&#x60; {     name: \&quot;Paul Newman\&quot;,     address: [         { type: \&quot;home\&quot;, street: \&quot;21st\&quot;, city: \&quot;Chicago\&quot; },         { type: \&quot;office\&quot;, street: null, city: null }     ] } &#x60;&#x60;&#x60;  will be transformed to the following XML snippet:  &#x60;&#x60;&#x60; &lt;name&gt;Paul Newman&lt;/name&gt; &lt;address&gt;   &lt;type&gt;home&lt;/type&gt;   &lt;street&gt;21st&lt;/street&gt;   &lt;city&gt;Chicago&lt;/city&gt; &lt;/address&gt; &lt;address&gt;   &lt;type&gt;office&lt;/type&gt;   &lt;street/&gt;   &lt;city/&gt; &lt;/address&gt; &#x60;&#x60;&#x60;  If the JavaScript object contains a property named &#x60;@&#x60; then its sub-properties are exported as attributes of the parent XML element. If the parent XML element does not have any child elements then its value is taken from a JavaScript object property named &#x60;#&#x60;.  For example, the following JavaScript object:  &#x60;&#x60;&#x60; {   \&quot;address\&quot;: [{     \&quot;@\&quot;: {       \&quot;type\&quot;: \&quot;home\&quot;     },     \&quot;street\&quot;: \&quot;21st\&quot;,     \&quot;city\&quot;: \&quot;Chicago\&quot;   },   {     \&quot;@\&quot;: {       \&quot;type\&quot;: \&quot;office\&quot;     },     \&quot;#\&quot;: &#39;unknown&#39;   }] } &#x60;&#x60;&#x60;  will be transformed to the following XML snippet:  &#x60;&#x60;&#x60; &lt;address type&#x3D;\&quot;home\&quot;&gt;   &lt;street&gt;21st&lt;/street&gt;   &lt;city&gt;Chicago&lt;/city&gt; &lt;/address&gt; &lt;address type&#x3D;\&quot;office\&quot;&gt;unknown&lt;/address&gt; &#x60;&#x60;&#x60;  This feature is also useful to customize your RSS feeds generated for various websites.  By default the whole result is wrapped in a &#x60;&lt;items&gt;&#x60; element and each page object is wrapped in a &#x60;&lt;item&gt;&#x60; element. You can change this using &lt;code&gt;xmlRoot&lt;/code&gt; and &lt;code&gt;xmlRow&lt;/code&gt; url parameters.  ### Pagination  The generated response supports [pagination](#/introduction/pagination). The pagination is always performed with the granularity of a single item, regardless whether &lt;code&gt;unwind&lt;/code&gt; parameter was provided. By default, the **Items** in the response are sorted by the time they were stored to the database, therefore you can use pagination to incrementally fetch the items as they are being added. No limit exists to how many items can be returned in one response.  If you specify &#x60;desc&#x3D;1&#x60; query parameter, the results are returned in the reverse order than they were stored (i.e. from newest to oldest items). Note that only the order of **Items** is reversed, but not the order of the &#x60;unwind&#x60; array elements. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object. If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures.  For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;.  The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not.  If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output.  This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (List&lt;Object&gt;)</returns>
        System.Threading.Tasks.Task<ApiResponse<List<Object>>> DatasetItemsGetWithHttpInfoAsync(string datasetId, string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Store items
        /// </summary>
        /// <remarks>
        /// Appends an item or an array of items to the end of the dataset. The POST payload is a JSON object or a JSON array of objects to save into the dataset.  If the data you attempt to store in the dataset is invalid (meaning any of the items received by the API fails the validation), the whole request is discarded and the API will return a response with status code 400. For more information about dataset schema validation, see [Dataset schema](https://docs.apify.com/platform/actors/development/actor-definition/dataset-schema/validation).  **IMPORTANT:** The limit of request payload size for the dataset is 5 MB. If the array exceeds the size, you&#39;ll need to split it into a number of smaller arrays. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="putItemsRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        System.Threading.Tasks.Task<Object> DatasetItemsPostAsync(string datasetId, List<PutItemsRequest> putItemsRequest, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Store items
        /// </summary>
        /// <remarks>
        /// Appends an item or an array of items to the end of the dataset. The POST payload is a JSON object or a JSON array of objects to save into the dataset.  If the data you attempt to store in the dataset is invalid (meaning any of the items received by the API fails the validation), the whole request is discarded and the API will return a response with status code 400. For more information about dataset schema validation, see [Dataset schema](https://docs.apify.com/platform/actors/development/actor-definition/dataset-schema/validation).  **IMPORTANT:** The limit of request payload size for the dataset is 5 MB. If the array exceeds the size, you&#39;ll need to split it into a number of smaller arrays. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="putItemsRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> DatasetItemsPostWithHttpInfoAsync(string datasetId, List<PutItemsRequest> putItemsRequest, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Update dataset
        /// </summary>
        /// <remarks>
        /// Updates a dataset&#39;s name using a value specified by a JSON object passed in the PUT payload. The response is the updated dataset object, as returned by the [Get dataset](#/reference/datasets/dataset-collection/get-dataset) API endpoint. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="updateDatasetRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of DatasetResponse</returns>
        System.Threading.Tasks.Task<DatasetResponse> DatasetPutAsync(string datasetId, UpdateDatasetRequest updateDatasetRequest, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Update dataset
        /// </summary>
        /// <remarks>
        /// Updates a dataset&#39;s name using a value specified by a JSON object passed in the PUT payload. The response is the updated dataset object, as returned by the [Get dataset](#/reference/datasets/dataset-collection/get-dataset) API endpoint. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="updateDatasetRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (DatasetResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<DatasetResponse>> DatasetPutWithHttpInfoAsync(string datasetId, UpdateDatasetRequest updateDatasetRequest, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Get dataset statistics
        /// </summary>
        /// <remarks>
        /// Returns statistics for given dataset.  Provides only [field statistics](https://docs.apify.com/platform/actors/development/actor-definition/dataset-schema/validation#dataset-field-statistics). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of GetDatasetStatisticsResponse</returns>
        System.Threading.Tasks.Task<GetDatasetStatisticsResponse> DatasetStatisticsGetAsync(string datasetId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Get dataset statistics
        /// </summary>
        /// <remarks>
        /// Returns statistics for given dataset.  Provides only [field statistics](https://docs.apify.com/platform/actors/development/actor-definition/dataset-schema/validation#dataset-field-statistics). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (GetDatasetStatisticsResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<GetDatasetStatisticsResponse>> DatasetStatisticsGetWithHttpInfoAsync(string datasetId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Get list of datasets
        /// </summary>
        /// <remarks>
        /// Lists all of a user&#39;s datasets.  The response is a JSON array of objects, where each object contains basic information about one dataset.  By default, the objects are sorted by the &#x60;createdAt&#x60; field in ascending order, therefore you can use pagination to incrementally fetch all datasets while new ones are still being created. To sort them in descending order, use &#x60;desc&#x3D;1&#x60; parameter. The endpoint supports pagination using &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 array elements. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="unnamed">If &#x60;true&#x60; or &#x60;1&#x60; then all the datasets are returned. By default only named datasets are returned.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of GetListOfDatasetsResponse</returns>
        System.Threading.Tasks.Task<GetListOfDatasetsResponse> DatasetsGetAsync(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), bool? unnamed = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Get list of datasets
        /// </summary>
        /// <remarks>
        /// Lists all of a user&#39;s datasets.  The response is a JSON array of objects, where each object contains basic information about one dataset.  By default, the objects are sorted by the &#x60;createdAt&#x60; field in ascending order, therefore you can use pagination to incrementally fetch all datasets while new ones are still being created. To sort them in descending order, use &#x60;desc&#x3D;1&#x60; parameter. The endpoint supports pagination using &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 array elements. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="unnamed">If &#x60;true&#x60; or &#x60;1&#x60; then all the datasets are returned. By default only named datasets are returned.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (GetListOfDatasetsResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<GetListOfDatasetsResponse>> DatasetsGetWithHttpInfoAsync(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), bool? unnamed = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Create dataset
        /// </summary>
        /// <remarks>
        /// Creates a dataset and returns its object. Keep in mind that data stored under unnamed dataset follows [data retention period](https://docs.apify.com/platform/storage#data-retention). It creates a dataset with the given name if the parameter name is used. If a dataset with the given name already exists then returns its object. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="name">Custom unique name to easily identify the dataset in the future. (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of DatasetResponse</returns>
        System.Threading.Tasks.Task<DatasetResponse> DatasetsPostAsync(string? name = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Create dataset
        /// </summary>
        /// <remarks>
        /// Creates a dataset and returns its object. Keep in mind that data stored under unnamed dataset follows [data retention period](https://docs.apify.com/platform/storage#data-retention). It creates a dataset with the given name if the parameter name is used. If a dataset with the given name already exists then returns its object. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="name">Custom unique name to easily identify the dataset in the future. (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (DatasetResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<DatasetResponse>> DatasetsPostWithHttpInfoAsync(string? name = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        #endregion Asynchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IStorageDatasetsApi : IStorageDatasetsApiSync, IStorageDatasetsApiAsync
    {

    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public partial class StorageDatasetsApi : IDisposable, IStorageDatasetsApi
    {
        private Apify.SDK.Client.ExceptionFactory _exceptionFactory = (name, response) => null;

        /// <summary>
        /// Initializes a new instance of the <see cref="StorageDatasetsApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <returns></returns>
        public StorageDatasetsApi() : this((string)null)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="StorageDatasetsApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        public StorageDatasetsApi(string basePath)
        {
            this.Configuration = Apify.SDK.Client.Configuration.MergeConfigurations(
                Apify.SDK.Client.GlobalConfiguration.Instance,
                new Apify.SDK.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Apify.SDK.Client.ApiClient(this.Configuration.BasePath);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Apify.SDK.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="StorageDatasetsApi"/> class using Configuration object.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        public StorageDatasetsApi(Apify.SDK.Client.Configuration configuration)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Configuration = Apify.SDK.Client.Configuration.MergeConfigurations(
                Apify.SDK.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Apify.SDK.Client.ApiClient(this.Configuration.BasePath);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Apify.SDK.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="StorageDatasetsApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public StorageDatasetsApi(HttpClient client, HttpClientHandler handler = null) : this(client, (string)null, handler)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="StorageDatasetsApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public StorageDatasetsApi(HttpClient client, string basePath, HttpClientHandler handler = null)
        {
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Apify.SDK.Client.Configuration.MergeConfigurations(
                Apify.SDK.Client.GlobalConfiguration.Instance,
                new Apify.SDK.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Apify.SDK.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Apify.SDK.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="StorageDatasetsApi"/> class using Configuration object.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public StorageDatasetsApi(HttpClient client, Apify.SDK.Client.Configuration configuration, HttpClientHandler handler = null)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Apify.SDK.Client.Configuration.MergeConfigurations(
                Apify.SDK.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Apify.SDK.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Apify.SDK.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="StorageDatasetsApi"/> class
        /// using a Configuration object and client instance.
        /// </summary>
        /// <param name="client">The client interface for synchronous API access.</param>
        /// <param name="asyncClient">The client interface for asynchronous API access.</param>
        /// <param name="configuration">The configuration object.</param>
        /// <exception cref="ArgumentNullException"></exception>
        public StorageDatasetsApi(Apify.SDK.Client.ISynchronousClient client, Apify.SDK.Client.IAsynchronousClient asyncClient, Apify.SDK.Client.IReadableConfiguration configuration)
        {
            if (client == null) throw new ArgumentNullException("client");
            if (asyncClient == null) throw new ArgumentNullException("asyncClient");
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Client = client;
            this.AsynchronousClient = asyncClient;
            this.Configuration = configuration;
            this.ExceptionFactory = Apify.SDK.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Disposes resources if they were created by us
        /// </summary>
        public void Dispose()
        {
            this.ApiClient?.Dispose();
        }

        /// <summary>
        /// Holds the ApiClient if created
        /// </summary>
        public Apify.SDK.Client.ApiClient ApiClient { get; set; } = null;

        /// <summary>
        /// The client for accessing this underlying API asynchronously.
        /// </summary>
        public Apify.SDK.Client.IAsynchronousClient AsynchronousClient { get; set; }

        /// <summary>
        /// The client for accessing this underlying API synchronously.
        /// </summary>
        public Apify.SDK.Client.ISynchronousClient Client { get; set; }

        /// <summary>
        /// Gets the base path of the API client.
        /// </summary>
        /// <value>The base path</value>
        public string GetBasePath()
        {
            return this.Configuration.BasePath;
        }

        /// <summary>
        /// Gets or sets the configuration object
        /// </summary>
        /// <value>An instance of the Configuration</value>
        public Apify.SDK.Client.IReadableConfiguration Configuration { get; set; }

        /// <summary>
        /// Provides a factory method hook for the creation of exceptions.
        /// </summary>
        public Apify.SDK.Client.ExceptionFactory ExceptionFactory
        {
            get
            {
                if (_exceptionFactory != null && _exceptionFactory.GetInvocationList().Length > 1)
                {
                    throw new InvalidOperationException("Multicast delegate for ExceptionFactory is unsupported.");
                }
                return _exceptionFactory;
            }
            set { _exceptionFactory = value; }
        }

        /// <summary>
        /// Delete dataset Deletes a specific dataset.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <returns></returns>
        public void DatasetDelete(string datasetId)
        {
            DatasetDeleteWithHttpInfo(datasetId);
        }

        /// <summary>
        /// Delete dataset Deletes a specific dataset.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <returns>ApiResponse of Object(void)</returns>
        public Apify.SDK.Client.ApiResponse<Object> DatasetDeleteWithHttpInfo(string datasetId)
        {
            // verify the required parameter 'datasetId' is set
            if (datasetId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'datasetId' when calling StorageDatasetsApi->DatasetDelete");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("datasetId", Apify.SDK.Client.ClientUtils.ParameterToString(datasetId)); // path parameter

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Delete<Object>("/v2/datasets/{datasetId}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DatasetDelete", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Delete dataset Deletes a specific dataset.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of void</returns>
        public async System.Threading.Tasks.Task DatasetDeleteAsync(string datasetId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            await DatasetDeleteWithHttpInfoAsync(datasetId, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Delete dataset Deletes a specific dataset.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<Object>> DatasetDeleteWithHttpInfoAsync(string datasetId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'datasetId' is set
            if (datasetId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'datasetId' when calling StorageDatasetsApi->DatasetDelete");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("datasetId", Apify.SDK.Client.ClientUtils.ParameterToString(datasetId)); // path parameter

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.DeleteAsync<Object>("/v2/datasets/{datasetId}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DatasetDelete", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get dataset Returns dataset object for given dataset ID.  :::note  Keep in mind that attributes &#x60;itemCount&#x60; and &#x60;cleanItemCount&#x60; are not propagated right away after data are pushed into a dataset.  :::  There is a short period (up to 5 seconds) during which these counters may not match with exact counts in dataset items. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="token">API authentication token. It is required only when using the &#x60;username~dataset-name&#x60; format for &#x60;datasetId&#x60;.  (optional)</param>
        /// <returns>DatasetResponse</returns>
        public DatasetResponse DatasetGet(string datasetId, string? token = default(string?))
        {
            Apify.SDK.Client.ApiResponse<DatasetResponse> localVarResponse = DatasetGetWithHttpInfo(datasetId, token);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get dataset Returns dataset object for given dataset ID.  :::note  Keep in mind that attributes &#x60;itemCount&#x60; and &#x60;cleanItemCount&#x60; are not propagated right away after data are pushed into a dataset.  :::  There is a short period (up to 5 seconds) during which these counters may not match with exact counts in dataset items. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="token">API authentication token. It is required only when using the &#x60;username~dataset-name&#x60; format for &#x60;datasetId&#x60;.  (optional)</param>
        /// <returns>ApiResponse of DatasetResponse</returns>
        public Apify.SDK.Client.ApiResponse<DatasetResponse> DatasetGetWithHttpInfo(string datasetId, string? token = default(string?))
        {
            // verify the required parameter 'datasetId' is set
            if (datasetId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'datasetId' when calling StorageDatasetsApi->DatasetGet");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("datasetId", Apify.SDK.Client.ClientUtils.ParameterToString(datasetId)); // path parameter
            if (token != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", token));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<DatasetResponse>("/v2/datasets/{datasetId}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DatasetGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get dataset Returns dataset object for given dataset ID.  :::note  Keep in mind that attributes &#x60;itemCount&#x60; and &#x60;cleanItemCount&#x60; are not propagated right away after data are pushed into a dataset.  :::  There is a short period (up to 5 seconds) during which these counters may not match with exact counts in dataset items. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="token">API authentication token. It is required only when using the &#x60;username~dataset-name&#x60; format for &#x60;datasetId&#x60;.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of DatasetResponse</returns>
        public async System.Threading.Tasks.Task<DatasetResponse> DatasetGetAsync(string datasetId, string? token = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<DatasetResponse> localVarResponse = await DatasetGetWithHttpInfoAsync(datasetId, token, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get dataset Returns dataset object for given dataset ID.  :::note  Keep in mind that attributes &#x60;itemCount&#x60; and &#x60;cleanItemCount&#x60; are not propagated right away after data are pushed into a dataset.  :::  There is a short period (up to 5 seconds) during which these counters may not match with exact counts in dataset items. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="token">API authentication token. It is required only when using the &#x60;username~dataset-name&#x60; format for &#x60;datasetId&#x60;.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (DatasetResponse)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<DatasetResponse>> DatasetGetWithHttpInfoAsync(string datasetId, string? token = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'datasetId' is set
            if (datasetId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'datasetId' when calling StorageDatasetsApi->DatasetGet");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("datasetId", Apify.SDK.Client.ClientUtils.ParameterToString(datasetId)); // path parameter
            if (token != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", token));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<DatasetResponse>("/v2/datasets/{datasetId}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DatasetGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get items Returns data stored in the dataset in a desired format.  ### Response format  The format of the response depends on &lt;code&gt;format&lt;/code&gt; query parameter.  The &lt;code&gt;format&lt;/code&gt; parameter can have one of the following values: &lt;code&gt;json&lt;/code&gt;, &lt;code&gt;jsonl&lt;/code&gt;, &lt;code&gt;xml&lt;/code&gt;, &lt;code&gt;html&lt;/code&gt;, &lt;code&gt;csv&lt;/code&gt;, &lt;code&gt;xlsx&lt;/code&gt; and &lt;code&gt;rss&lt;/code&gt;.  The following table describes how each format is treated.  &lt;table&gt;   &lt;tr&gt;     &lt;th&gt;Format&lt;/th&gt;     &lt;th&gt;Items&lt;/th&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;json&lt;/code&gt;&lt;/td&gt;     &lt;td rowspan&#x3D;\&quot;3\&quot;&gt;The response is a JSON, JSONL or XML array of raw item objects.&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;jsonl&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;xml&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;html&lt;/code&gt;&lt;/td&gt;     &lt;td rowspan&#x3D;\&quot;3\&quot;&gt;The response is a HTML, CSV or XLSX table, where columns correspond to the     properties of the item and rows correspond to each dataset item.&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;csv&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;xlsx&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;rss&lt;/code&gt;&lt;/td&gt;     &lt;td colspan&#x3D;\&quot;2\&quot;&gt;The response is a RSS file. Each item is displayed as child elements of one     &lt;code&gt;&amp;lt;item&amp;gt;&lt;/code&gt;.&lt;/td&gt;   &lt;/tr&gt; &lt;/table&gt;  Note that CSV, XLSX and HTML tables are limited to 2000 columns and the column names cannot be longer than 200 characters. JSON, XML and RSS formats do not have such restrictions.  ### Hidden fields  The top-level fields starting with the &#x60;#&#x60; character are considered hidden. These are useful to store debugging information and can be omitted from the output by providing the &#x60;skipHidden&#x3D;1&#x60; or &#x60;clean&#x3D;1&#x60; query parameters. For example, if you store the following object to the dataset:  &#x60;&#x60;&#x60; {     productName: \&quot;iPhone Xs\&quot;,     description: \&quot;Welcome to the big screens.\&quot;     #debug: {         url: \&quot;https://www.apple.com/lae/iphone-xs/\&quot;,         crawledAt: \&quot;2019-01-21T16:06:03.683Z\&quot;     } } &#x60;&#x60;&#x60;  The &#x60;#debug&#x60; field will be considered as hidden and can be omitted from the results. This is useful to provide nice cleaned data to end users, while keeping debugging info available if needed. The Dataset object returned by the API contains the number of such clean items in the&#x60;dataset.cleanItemCount&#x60; property.  ### XML format extension  When exporting results to XML or RSS formats, the names of object properties become XML tags and the corresponding values become tag&#39;s children. For example, the following JavaScript object:  &#x60;&#x60;&#x60; {     name: \&quot;Paul Newman\&quot;,     address: [         { type: \&quot;home\&quot;, street: \&quot;21st\&quot;, city: \&quot;Chicago\&quot; },         { type: \&quot;office\&quot;, street: null, city: null }     ] } &#x60;&#x60;&#x60;  will be transformed to the following XML snippet:  &#x60;&#x60;&#x60; &lt;name&gt;Paul Newman&lt;/name&gt; &lt;address&gt;   &lt;type&gt;home&lt;/type&gt;   &lt;street&gt;21st&lt;/street&gt;   &lt;city&gt;Chicago&lt;/city&gt; &lt;/address&gt; &lt;address&gt;   &lt;type&gt;office&lt;/type&gt;   &lt;street/&gt;   &lt;city/&gt; &lt;/address&gt; &#x60;&#x60;&#x60;  If the JavaScript object contains a property named &#x60;@&#x60; then its sub-properties are exported as attributes of the parent XML element. If the parent XML element does not have any child elements then its value is taken from a JavaScript object property named &#x60;#&#x60;.  For example, the following JavaScript object:  &#x60;&#x60;&#x60; {   \&quot;address\&quot;: [{     \&quot;@\&quot;: {       \&quot;type\&quot;: \&quot;home\&quot;     },     \&quot;street\&quot;: \&quot;21st\&quot;,     \&quot;city\&quot;: \&quot;Chicago\&quot;   },   {     \&quot;@\&quot;: {       \&quot;type\&quot;: \&quot;office\&quot;     },     \&quot;#\&quot;: &#39;unknown&#39;   }] } &#x60;&#x60;&#x60;  will be transformed to the following XML snippet:  &#x60;&#x60;&#x60; &lt;address type&#x3D;\&quot;home\&quot;&gt;   &lt;street&gt;21st&lt;/street&gt;   &lt;city&gt;Chicago&lt;/city&gt; &lt;/address&gt; &lt;address type&#x3D;\&quot;office\&quot;&gt;unknown&lt;/address&gt; &#x60;&#x60;&#x60;  This feature is also useful to customize your RSS feeds generated for various websites.  By default the whole result is wrapped in a &#x60;&lt;items&gt;&#x60; element and each page object is wrapped in a &#x60;&lt;item&gt;&#x60; element. You can change this using &lt;code&gt;xmlRoot&lt;/code&gt; and &lt;code&gt;xmlRow&lt;/code&gt; url parameters.  ### Pagination  The generated response supports [pagination](#/introduction/pagination). The pagination is always performed with the granularity of a single item, regardless whether &lt;code&gt;unwind&lt;/code&gt; parameter was provided. By default, the **Items** in the response are sorted by the time they were stored to the database, therefore you can use pagination to incrementally fetch the items as they are being added. No limit exists to how many items can be returned in one response.  If you specify &#x60;desc&#x3D;1&#x60; query parameter, the results are returned in the reverse order than they were stored (i.e. from newest to oldest items). Note that only the order of **Items** is reversed, but not the order of the &#x60;unwind&#x60; array elements. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object. If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures.  For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;.  The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not.  If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output.  This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <returns>List&lt;Object&gt;</returns>
        public List<Object> DatasetItemsGet(string datasetId, string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?))
        {
            Apify.SDK.Client.ApiResponse<List<Object>> localVarResponse = DatasetItemsGetWithHttpInfo(datasetId, format, clean, offset, limit, fields, omit, unwind, flatten, desc, attachment, delimiter, bom, xmlRoot, xmlRow, skipHeaderRow, skipHidden, skipEmpty, simplified, skipFailedPages);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get items Returns data stored in the dataset in a desired format.  ### Response format  The format of the response depends on &lt;code&gt;format&lt;/code&gt; query parameter.  The &lt;code&gt;format&lt;/code&gt; parameter can have one of the following values: &lt;code&gt;json&lt;/code&gt;, &lt;code&gt;jsonl&lt;/code&gt;, &lt;code&gt;xml&lt;/code&gt;, &lt;code&gt;html&lt;/code&gt;, &lt;code&gt;csv&lt;/code&gt;, &lt;code&gt;xlsx&lt;/code&gt; and &lt;code&gt;rss&lt;/code&gt;.  The following table describes how each format is treated.  &lt;table&gt;   &lt;tr&gt;     &lt;th&gt;Format&lt;/th&gt;     &lt;th&gt;Items&lt;/th&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;json&lt;/code&gt;&lt;/td&gt;     &lt;td rowspan&#x3D;\&quot;3\&quot;&gt;The response is a JSON, JSONL or XML array of raw item objects.&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;jsonl&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;xml&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;html&lt;/code&gt;&lt;/td&gt;     &lt;td rowspan&#x3D;\&quot;3\&quot;&gt;The response is a HTML, CSV or XLSX table, where columns correspond to the     properties of the item and rows correspond to each dataset item.&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;csv&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;xlsx&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;rss&lt;/code&gt;&lt;/td&gt;     &lt;td colspan&#x3D;\&quot;2\&quot;&gt;The response is a RSS file. Each item is displayed as child elements of one     &lt;code&gt;&amp;lt;item&amp;gt;&lt;/code&gt;.&lt;/td&gt;   &lt;/tr&gt; &lt;/table&gt;  Note that CSV, XLSX and HTML tables are limited to 2000 columns and the column names cannot be longer than 200 characters. JSON, XML and RSS formats do not have such restrictions.  ### Hidden fields  The top-level fields starting with the &#x60;#&#x60; character are considered hidden. These are useful to store debugging information and can be omitted from the output by providing the &#x60;skipHidden&#x3D;1&#x60; or &#x60;clean&#x3D;1&#x60; query parameters. For example, if you store the following object to the dataset:  &#x60;&#x60;&#x60; {     productName: \&quot;iPhone Xs\&quot;,     description: \&quot;Welcome to the big screens.\&quot;     #debug: {         url: \&quot;https://www.apple.com/lae/iphone-xs/\&quot;,         crawledAt: \&quot;2019-01-21T16:06:03.683Z\&quot;     } } &#x60;&#x60;&#x60;  The &#x60;#debug&#x60; field will be considered as hidden and can be omitted from the results. This is useful to provide nice cleaned data to end users, while keeping debugging info available if needed. The Dataset object returned by the API contains the number of such clean items in the&#x60;dataset.cleanItemCount&#x60; property.  ### XML format extension  When exporting results to XML or RSS formats, the names of object properties become XML tags and the corresponding values become tag&#39;s children. For example, the following JavaScript object:  &#x60;&#x60;&#x60; {     name: \&quot;Paul Newman\&quot;,     address: [         { type: \&quot;home\&quot;, street: \&quot;21st\&quot;, city: \&quot;Chicago\&quot; },         { type: \&quot;office\&quot;, street: null, city: null }     ] } &#x60;&#x60;&#x60;  will be transformed to the following XML snippet:  &#x60;&#x60;&#x60; &lt;name&gt;Paul Newman&lt;/name&gt; &lt;address&gt;   &lt;type&gt;home&lt;/type&gt;   &lt;street&gt;21st&lt;/street&gt;   &lt;city&gt;Chicago&lt;/city&gt; &lt;/address&gt; &lt;address&gt;   &lt;type&gt;office&lt;/type&gt;   &lt;street/&gt;   &lt;city/&gt; &lt;/address&gt; &#x60;&#x60;&#x60;  If the JavaScript object contains a property named &#x60;@&#x60; then its sub-properties are exported as attributes of the parent XML element. If the parent XML element does not have any child elements then its value is taken from a JavaScript object property named &#x60;#&#x60;.  For example, the following JavaScript object:  &#x60;&#x60;&#x60; {   \&quot;address\&quot;: [{     \&quot;@\&quot;: {       \&quot;type\&quot;: \&quot;home\&quot;     },     \&quot;street\&quot;: \&quot;21st\&quot;,     \&quot;city\&quot;: \&quot;Chicago\&quot;   },   {     \&quot;@\&quot;: {       \&quot;type\&quot;: \&quot;office\&quot;     },     \&quot;#\&quot;: &#39;unknown&#39;   }] } &#x60;&#x60;&#x60;  will be transformed to the following XML snippet:  &#x60;&#x60;&#x60; &lt;address type&#x3D;\&quot;home\&quot;&gt;   &lt;street&gt;21st&lt;/street&gt;   &lt;city&gt;Chicago&lt;/city&gt; &lt;/address&gt; &lt;address type&#x3D;\&quot;office\&quot;&gt;unknown&lt;/address&gt; &#x60;&#x60;&#x60;  This feature is also useful to customize your RSS feeds generated for various websites.  By default the whole result is wrapped in a &#x60;&lt;items&gt;&#x60; element and each page object is wrapped in a &#x60;&lt;item&gt;&#x60; element. You can change this using &lt;code&gt;xmlRoot&lt;/code&gt; and &lt;code&gt;xmlRow&lt;/code&gt; url parameters.  ### Pagination  The generated response supports [pagination](#/introduction/pagination). The pagination is always performed with the granularity of a single item, regardless whether &lt;code&gt;unwind&lt;/code&gt; parameter was provided. By default, the **Items** in the response are sorted by the time they were stored to the database, therefore you can use pagination to incrementally fetch the items as they are being added. No limit exists to how many items can be returned in one response.  If you specify &#x60;desc&#x3D;1&#x60; query parameter, the results are returned in the reverse order than they were stored (i.e. from newest to oldest items). Note that only the order of **Items** is reversed, but not the order of the &#x60;unwind&#x60; array elements. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object. If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures.  For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;.  The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not.  If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output.  This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <returns>ApiResponse of List&lt;Object&gt;</returns>
        public Apify.SDK.Client.ApiResponse<List<Object>> DatasetItemsGetWithHttpInfo(string datasetId, string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?))
        {
            // verify the required parameter 'datasetId' is set
            if (datasetId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'datasetId' when calling StorageDatasetsApi->DatasetItemsGet");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/jsonl",
                "text/csv",
                "text/html",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "application/rss+xml",
                "application/xml"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("datasetId", Apify.SDK.Client.ClientUtils.ParameterToString(datasetId)); // path parameter
            if (format != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "format", format));
            }
            if (clean != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "clean", clean));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (fields != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "fields", fields));
            }
            if (omit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "omit", omit));
            }
            if (unwind != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "unwind", unwind));
            }
            if (flatten != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "flatten", flatten));
            }
            if (desc != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "desc", desc));
            }
            if (attachment != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "attachment", attachment));
            }
            if (delimiter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "delimiter", delimiter));
            }
            if (bom != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "bom", bom));
            }
            if (xmlRoot != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "xmlRoot", xmlRoot));
            }
            if (xmlRow != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "xmlRow", xmlRow));
            }
            if (skipHeaderRow != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipHeaderRow", skipHeaderRow));
            }
            if (skipHidden != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipHidden", skipHidden));
            }
            if (skipEmpty != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipEmpty", skipEmpty));
            }
            if (simplified != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "simplified", simplified));
            }
            if (skipFailedPages != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipFailedPages", skipFailedPages));
            }

            // authentication (apiKeyStoreId) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearerStoreId) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<List<Object>>("/v2/datasets/{datasetId}/items", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DatasetItemsGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get items Returns data stored in the dataset in a desired format.  ### Response format  The format of the response depends on &lt;code&gt;format&lt;/code&gt; query parameter.  The &lt;code&gt;format&lt;/code&gt; parameter can have one of the following values: &lt;code&gt;json&lt;/code&gt;, &lt;code&gt;jsonl&lt;/code&gt;, &lt;code&gt;xml&lt;/code&gt;, &lt;code&gt;html&lt;/code&gt;, &lt;code&gt;csv&lt;/code&gt;, &lt;code&gt;xlsx&lt;/code&gt; and &lt;code&gt;rss&lt;/code&gt;.  The following table describes how each format is treated.  &lt;table&gt;   &lt;tr&gt;     &lt;th&gt;Format&lt;/th&gt;     &lt;th&gt;Items&lt;/th&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;json&lt;/code&gt;&lt;/td&gt;     &lt;td rowspan&#x3D;\&quot;3\&quot;&gt;The response is a JSON, JSONL or XML array of raw item objects.&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;jsonl&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;xml&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;html&lt;/code&gt;&lt;/td&gt;     &lt;td rowspan&#x3D;\&quot;3\&quot;&gt;The response is a HTML, CSV or XLSX table, where columns correspond to the     properties of the item and rows correspond to each dataset item.&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;csv&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;xlsx&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;rss&lt;/code&gt;&lt;/td&gt;     &lt;td colspan&#x3D;\&quot;2\&quot;&gt;The response is a RSS file. Each item is displayed as child elements of one     &lt;code&gt;&amp;lt;item&amp;gt;&lt;/code&gt;.&lt;/td&gt;   &lt;/tr&gt; &lt;/table&gt;  Note that CSV, XLSX and HTML tables are limited to 2000 columns and the column names cannot be longer than 200 characters. JSON, XML and RSS formats do not have such restrictions.  ### Hidden fields  The top-level fields starting with the &#x60;#&#x60; character are considered hidden. These are useful to store debugging information and can be omitted from the output by providing the &#x60;skipHidden&#x3D;1&#x60; or &#x60;clean&#x3D;1&#x60; query parameters. For example, if you store the following object to the dataset:  &#x60;&#x60;&#x60; {     productName: \&quot;iPhone Xs\&quot;,     description: \&quot;Welcome to the big screens.\&quot;     #debug: {         url: \&quot;https://www.apple.com/lae/iphone-xs/\&quot;,         crawledAt: \&quot;2019-01-21T16:06:03.683Z\&quot;     } } &#x60;&#x60;&#x60;  The &#x60;#debug&#x60; field will be considered as hidden and can be omitted from the results. This is useful to provide nice cleaned data to end users, while keeping debugging info available if needed. The Dataset object returned by the API contains the number of such clean items in the&#x60;dataset.cleanItemCount&#x60; property.  ### XML format extension  When exporting results to XML or RSS formats, the names of object properties become XML tags and the corresponding values become tag&#39;s children. For example, the following JavaScript object:  &#x60;&#x60;&#x60; {     name: \&quot;Paul Newman\&quot;,     address: [         { type: \&quot;home\&quot;, street: \&quot;21st\&quot;, city: \&quot;Chicago\&quot; },         { type: \&quot;office\&quot;, street: null, city: null }     ] } &#x60;&#x60;&#x60;  will be transformed to the following XML snippet:  &#x60;&#x60;&#x60; &lt;name&gt;Paul Newman&lt;/name&gt; &lt;address&gt;   &lt;type&gt;home&lt;/type&gt;   &lt;street&gt;21st&lt;/street&gt;   &lt;city&gt;Chicago&lt;/city&gt; &lt;/address&gt; &lt;address&gt;   &lt;type&gt;office&lt;/type&gt;   &lt;street/&gt;   &lt;city/&gt; &lt;/address&gt; &#x60;&#x60;&#x60;  If the JavaScript object contains a property named &#x60;@&#x60; then its sub-properties are exported as attributes of the parent XML element. If the parent XML element does not have any child elements then its value is taken from a JavaScript object property named &#x60;#&#x60;.  For example, the following JavaScript object:  &#x60;&#x60;&#x60; {   \&quot;address\&quot;: [{     \&quot;@\&quot;: {       \&quot;type\&quot;: \&quot;home\&quot;     },     \&quot;street\&quot;: \&quot;21st\&quot;,     \&quot;city\&quot;: \&quot;Chicago\&quot;   },   {     \&quot;@\&quot;: {       \&quot;type\&quot;: \&quot;office\&quot;     },     \&quot;#\&quot;: &#39;unknown&#39;   }] } &#x60;&#x60;&#x60;  will be transformed to the following XML snippet:  &#x60;&#x60;&#x60; &lt;address type&#x3D;\&quot;home\&quot;&gt;   &lt;street&gt;21st&lt;/street&gt;   &lt;city&gt;Chicago&lt;/city&gt; &lt;/address&gt; &lt;address type&#x3D;\&quot;office\&quot;&gt;unknown&lt;/address&gt; &#x60;&#x60;&#x60;  This feature is also useful to customize your RSS feeds generated for various websites.  By default the whole result is wrapped in a &#x60;&lt;items&gt;&#x60; element and each page object is wrapped in a &#x60;&lt;item&gt;&#x60; element. You can change this using &lt;code&gt;xmlRoot&lt;/code&gt; and &lt;code&gt;xmlRow&lt;/code&gt; url parameters.  ### Pagination  The generated response supports [pagination](#/introduction/pagination). The pagination is always performed with the granularity of a single item, regardless whether &lt;code&gt;unwind&lt;/code&gt; parameter was provided. By default, the **Items** in the response are sorted by the time they were stored to the database, therefore you can use pagination to incrementally fetch the items as they are being added. No limit exists to how many items can be returned in one response.  If you specify &#x60;desc&#x3D;1&#x60; query parameter, the results are returned in the reverse order than they were stored (i.e. from newest to oldest items). Note that only the order of **Items** is reversed, but not the order of the &#x60;unwind&#x60; array elements. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object. If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures.  For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;.  The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not.  If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output.  This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of List&lt;Object&gt;</returns>
        public async System.Threading.Tasks.Task<List<Object>> DatasetItemsGetAsync(string datasetId, string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<List<Object>> localVarResponse = await DatasetItemsGetWithHttpInfoAsync(datasetId, format, clean, offset, limit, fields, omit, unwind, flatten, desc, attachment, delimiter, bom, xmlRoot, xmlRow, skipHeaderRow, skipHidden, skipEmpty, simplified, skipFailedPages, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get items Returns data stored in the dataset in a desired format.  ### Response format  The format of the response depends on &lt;code&gt;format&lt;/code&gt; query parameter.  The &lt;code&gt;format&lt;/code&gt; parameter can have one of the following values: &lt;code&gt;json&lt;/code&gt;, &lt;code&gt;jsonl&lt;/code&gt;, &lt;code&gt;xml&lt;/code&gt;, &lt;code&gt;html&lt;/code&gt;, &lt;code&gt;csv&lt;/code&gt;, &lt;code&gt;xlsx&lt;/code&gt; and &lt;code&gt;rss&lt;/code&gt;.  The following table describes how each format is treated.  &lt;table&gt;   &lt;tr&gt;     &lt;th&gt;Format&lt;/th&gt;     &lt;th&gt;Items&lt;/th&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;json&lt;/code&gt;&lt;/td&gt;     &lt;td rowspan&#x3D;\&quot;3\&quot;&gt;The response is a JSON, JSONL or XML array of raw item objects.&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;jsonl&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;xml&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;html&lt;/code&gt;&lt;/td&gt;     &lt;td rowspan&#x3D;\&quot;3\&quot;&gt;The response is a HTML, CSV or XLSX table, where columns correspond to the     properties of the item and rows correspond to each dataset item.&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;csv&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;xlsx&lt;/code&gt;&lt;/td&gt;   &lt;/tr&gt;   &lt;tr&gt;     &lt;td&gt;&lt;code&gt;rss&lt;/code&gt;&lt;/td&gt;     &lt;td colspan&#x3D;\&quot;2\&quot;&gt;The response is a RSS file. Each item is displayed as child elements of one     &lt;code&gt;&amp;lt;item&amp;gt;&lt;/code&gt;.&lt;/td&gt;   &lt;/tr&gt; &lt;/table&gt;  Note that CSV, XLSX and HTML tables are limited to 2000 columns and the column names cannot be longer than 200 characters. JSON, XML and RSS formats do not have such restrictions.  ### Hidden fields  The top-level fields starting with the &#x60;#&#x60; character are considered hidden. These are useful to store debugging information and can be omitted from the output by providing the &#x60;skipHidden&#x3D;1&#x60; or &#x60;clean&#x3D;1&#x60; query parameters. For example, if you store the following object to the dataset:  &#x60;&#x60;&#x60; {     productName: \&quot;iPhone Xs\&quot;,     description: \&quot;Welcome to the big screens.\&quot;     #debug: {         url: \&quot;https://www.apple.com/lae/iphone-xs/\&quot;,         crawledAt: \&quot;2019-01-21T16:06:03.683Z\&quot;     } } &#x60;&#x60;&#x60;  The &#x60;#debug&#x60; field will be considered as hidden and can be omitted from the results. This is useful to provide nice cleaned data to end users, while keeping debugging info available if needed. The Dataset object returned by the API contains the number of such clean items in the&#x60;dataset.cleanItemCount&#x60; property.  ### XML format extension  When exporting results to XML or RSS formats, the names of object properties become XML tags and the corresponding values become tag&#39;s children. For example, the following JavaScript object:  &#x60;&#x60;&#x60; {     name: \&quot;Paul Newman\&quot;,     address: [         { type: \&quot;home\&quot;, street: \&quot;21st\&quot;, city: \&quot;Chicago\&quot; },         { type: \&quot;office\&quot;, street: null, city: null }     ] } &#x60;&#x60;&#x60;  will be transformed to the following XML snippet:  &#x60;&#x60;&#x60; &lt;name&gt;Paul Newman&lt;/name&gt; &lt;address&gt;   &lt;type&gt;home&lt;/type&gt;   &lt;street&gt;21st&lt;/street&gt;   &lt;city&gt;Chicago&lt;/city&gt; &lt;/address&gt; &lt;address&gt;   &lt;type&gt;office&lt;/type&gt;   &lt;street/&gt;   &lt;city/&gt; &lt;/address&gt; &#x60;&#x60;&#x60;  If the JavaScript object contains a property named &#x60;@&#x60; then its sub-properties are exported as attributes of the parent XML element. If the parent XML element does not have any child elements then its value is taken from a JavaScript object property named &#x60;#&#x60;.  For example, the following JavaScript object:  &#x60;&#x60;&#x60; {   \&quot;address\&quot;: [{     \&quot;@\&quot;: {       \&quot;type\&quot;: \&quot;home\&quot;     },     \&quot;street\&quot;: \&quot;21st\&quot;,     \&quot;city\&quot;: \&quot;Chicago\&quot;   },   {     \&quot;@\&quot;: {       \&quot;type\&quot;: \&quot;office\&quot;     },     \&quot;#\&quot;: &#39;unknown&#39;   }] } &#x60;&#x60;&#x60;  will be transformed to the following XML snippet:  &#x60;&#x60;&#x60; &lt;address type&#x3D;\&quot;home\&quot;&gt;   &lt;street&gt;21st&lt;/street&gt;   &lt;city&gt;Chicago&lt;/city&gt; &lt;/address&gt; &lt;address type&#x3D;\&quot;office\&quot;&gt;unknown&lt;/address&gt; &#x60;&#x60;&#x60;  This feature is also useful to customize your RSS feeds generated for various websites.  By default the whole result is wrapped in a &#x60;&lt;items&gt;&#x60; element and each page object is wrapped in a &#x60;&lt;item&gt;&#x60; element. You can change this using &lt;code&gt;xmlRoot&lt;/code&gt; and &lt;code&gt;xmlRow&lt;/code&gt; url parameters.  ### Pagination  The generated response supports [pagination](#/introduction/pagination). The pagination is always performed with the granularity of a single item, regardless whether &lt;code&gt;unwind&lt;/code&gt; parameter was provided. By default, the **Items** in the response are sorted by the time they were stored to the database, therefore you can use pagination to incrementally fetch the items as they are being added. No limit exists to how many items can be returned in one response.  If you specify &#x60;desc&#x3D;1&#x60; query parameter, the results are returned in the reverse order than they were stored (i.e. from newest to oldest items). Note that only the order of **Items** is reversed, but not the order of the &#x60;unwind&#x60; array elements. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object. If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures.  For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;.  The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not.  If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output.  This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (List&lt;Object&gt;)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<List<Object>>> DatasetItemsGetWithHttpInfoAsync(string datasetId, string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'datasetId' is set
            if (datasetId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'datasetId' when calling StorageDatasetsApi->DatasetItemsGet");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json",
                "application/jsonl",
                "text/csv",
                "text/html",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "application/rss+xml",
                "application/xml"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("datasetId", Apify.SDK.Client.ClientUtils.ParameterToString(datasetId)); // path parameter
            if (format != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "format", format));
            }
            if (clean != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "clean", clean));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (fields != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "fields", fields));
            }
            if (omit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "omit", omit));
            }
            if (unwind != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "unwind", unwind));
            }
            if (flatten != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "flatten", flatten));
            }
            if (desc != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "desc", desc));
            }
            if (attachment != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "attachment", attachment));
            }
            if (delimiter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "delimiter", delimiter));
            }
            if (bom != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "bom", bom));
            }
            if (xmlRoot != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "xmlRoot", xmlRoot));
            }
            if (xmlRow != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "xmlRow", xmlRow));
            }
            if (skipHeaderRow != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipHeaderRow", skipHeaderRow));
            }
            if (skipHidden != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipHidden", skipHidden));
            }
            if (skipEmpty != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipEmpty", skipEmpty));
            }
            if (simplified != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "simplified", simplified));
            }
            if (skipFailedPages != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipFailedPages", skipFailedPages));
            }

            // authentication (apiKeyStoreId) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearerStoreId) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<List<Object>>("/v2/datasets/{datasetId}/items", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DatasetItemsGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Store items Appends an item or an array of items to the end of the dataset. The POST payload is a JSON object or a JSON array of objects to save into the dataset.  If the data you attempt to store in the dataset is invalid (meaning any of the items received by the API fails the validation), the whole request is discarded and the API will return a response with status code 400. For more information about dataset schema validation, see [Dataset schema](https://docs.apify.com/platform/actors/development/actor-definition/dataset-schema/validation).  **IMPORTANT:** The limit of request payload size for the dataset is 5 MB. If the array exceeds the size, you&#39;ll need to split it into a number of smaller arrays. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="putItemsRequest"></param>
        /// <returns>Object</returns>
        public Object DatasetItemsPost(string datasetId, List<PutItemsRequest> putItemsRequest)
        {
            Apify.SDK.Client.ApiResponse<Object> localVarResponse = DatasetItemsPostWithHttpInfo(datasetId, putItemsRequest);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Store items Appends an item or an array of items to the end of the dataset. The POST payload is a JSON object or a JSON array of objects to save into the dataset.  If the data you attempt to store in the dataset is invalid (meaning any of the items received by the API fails the validation), the whole request is discarded and the API will return a response with status code 400. For more information about dataset schema validation, see [Dataset schema](https://docs.apify.com/platform/actors/development/actor-definition/dataset-schema/validation).  **IMPORTANT:** The limit of request payload size for the dataset is 5 MB. If the array exceeds the size, you&#39;ll need to split it into a number of smaller arrays. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="putItemsRequest"></param>
        /// <returns>ApiResponse of Object</returns>
        public Apify.SDK.Client.ApiResponse<Object> DatasetItemsPostWithHttpInfo(string datasetId, List<PutItemsRequest> putItemsRequest)
        {
            // verify the required parameter 'datasetId' is set
            if (datasetId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'datasetId' when calling StorageDatasetsApi->DatasetItemsPost");

            // verify the required parameter 'putItemsRequest' is set
            if (putItemsRequest == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'putItemsRequest' when calling StorageDatasetsApi->DatasetItemsPost");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("datasetId", Apify.SDK.Client.ClientUtils.ParameterToString(datasetId)); // path parameter
            localVarRequestOptions.Data = putItemsRequest;

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<Object>("/v2/datasets/{datasetId}/items", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DatasetItemsPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Store items Appends an item or an array of items to the end of the dataset. The POST payload is a JSON object or a JSON array of objects to save into the dataset.  If the data you attempt to store in the dataset is invalid (meaning any of the items received by the API fails the validation), the whole request is discarded and the API will return a response with status code 400. For more information about dataset schema validation, see [Dataset schema](https://docs.apify.com/platform/actors/development/actor-definition/dataset-schema/validation).  **IMPORTANT:** The limit of request payload size for the dataset is 5 MB. If the array exceeds the size, you&#39;ll need to split it into a number of smaller arrays. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="putItemsRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        public async System.Threading.Tasks.Task<Object> DatasetItemsPostAsync(string datasetId, List<PutItemsRequest> putItemsRequest, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<Object> localVarResponse = await DatasetItemsPostWithHttpInfoAsync(datasetId, putItemsRequest, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Store items Appends an item or an array of items to the end of the dataset. The POST payload is a JSON object or a JSON array of objects to save into the dataset.  If the data you attempt to store in the dataset is invalid (meaning any of the items received by the API fails the validation), the whole request is discarded and the API will return a response with status code 400. For more information about dataset schema validation, see [Dataset schema](https://docs.apify.com/platform/actors/development/actor-definition/dataset-schema/validation).  **IMPORTANT:** The limit of request payload size for the dataset is 5 MB. If the array exceeds the size, you&#39;ll need to split it into a number of smaller arrays. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="putItemsRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<Object>> DatasetItemsPostWithHttpInfoAsync(string datasetId, List<PutItemsRequest> putItemsRequest, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'datasetId' is set
            if (datasetId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'datasetId' when calling StorageDatasetsApi->DatasetItemsPost");

            // verify the required parameter 'putItemsRequest' is set
            if (putItemsRequest == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'putItemsRequest' when calling StorageDatasetsApi->DatasetItemsPost");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("datasetId", Apify.SDK.Client.ClientUtils.ParameterToString(datasetId)); // path parameter
            localVarRequestOptions.Data = putItemsRequest;

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<Object>("/v2/datasets/{datasetId}/items", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DatasetItemsPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Update dataset Updates a dataset&#39;s name using a value specified by a JSON object passed in the PUT payload. The response is the updated dataset object, as returned by the [Get dataset](#/reference/datasets/dataset-collection/get-dataset) API endpoint. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="updateDatasetRequest"></param>
        /// <returns>DatasetResponse</returns>
        public DatasetResponse DatasetPut(string datasetId, UpdateDatasetRequest updateDatasetRequest)
        {
            Apify.SDK.Client.ApiResponse<DatasetResponse> localVarResponse = DatasetPutWithHttpInfo(datasetId, updateDatasetRequest);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Update dataset Updates a dataset&#39;s name using a value specified by a JSON object passed in the PUT payload. The response is the updated dataset object, as returned by the [Get dataset](#/reference/datasets/dataset-collection/get-dataset) API endpoint. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="updateDatasetRequest"></param>
        /// <returns>ApiResponse of DatasetResponse</returns>
        public Apify.SDK.Client.ApiResponse<DatasetResponse> DatasetPutWithHttpInfo(string datasetId, UpdateDatasetRequest updateDatasetRequest)
        {
            // verify the required parameter 'datasetId' is set
            if (datasetId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'datasetId' when calling StorageDatasetsApi->DatasetPut");

            // verify the required parameter 'updateDatasetRequest' is set
            if (updateDatasetRequest == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'updateDatasetRequest' when calling StorageDatasetsApi->DatasetPut");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("datasetId", Apify.SDK.Client.ClientUtils.ParameterToString(datasetId)); // path parameter
            localVarRequestOptions.Data = updateDatasetRequest;

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Put<DatasetResponse>("/v2/datasets/{datasetId}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DatasetPut", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Update dataset Updates a dataset&#39;s name using a value specified by a JSON object passed in the PUT payload. The response is the updated dataset object, as returned by the [Get dataset](#/reference/datasets/dataset-collection/get-dataset) API endpoint. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="updateDatasetRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of DatasetResponse</returns>
        public async System.Threading.Tasks.Task<DatasetResponse> DatasetPutAsync(string datasetId, UpdateDatasetRequest updateDatasetRequest, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<DatasetResponse> localVarResponse = await DatasetPutWithHttpInfoAsync(datasetId, updateDatasetRequest, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Update dataset Updates a dataset&#39;s name using a value specified by a JSON object passed in the PUT payload. The response is the updated dataset object, as returned by the [Get dataset](#/reference/datasets/dataset-collection/get-dataset) API endpoint. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="updateDatasetRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (DatasetResponse)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<DatasetResponse>> DatasetPutWithHttpInfoAsync(string datasetId, UpdateDatasetRequest updateDatasetRequest, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'datasetId' is set
            if (datasetId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'datasetId' when calling StorageDatasetsApi->DatasetPut");

            // verify the required parameter 'updateDatasetRequest' is set
            if (updateDatasetRequest == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'updateDatasetRequest' when calling StorageDatasetsApi->DatasetPut");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("datasetId", Apify.SDK.Client.ClientUtils.ParameterToString(datasetId)); // path parameter
            localVarRequestOptions.Data = updateDatasetRequest;

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PutAsync<DatasetResponse>("/v2/datasets/{datasetId}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DatasetPut", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get dataset statistics Returns statistics for given dataset.  Provides only [field statistics](https://docs.apify.com/platform/actors/development/actor-definition/dataset-schema/validation#dataset-field-statistics). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <returns>GetDatasetStatisticsResponse</returns>
        public GetDatasetStatisticsResponse DatasetStatisticsGet(string datasetId)
        {
            Apify.SDK.Client.ApiResponse<GetDatasetStatisticsResponse> localVarResponse = DatasetStatisticsGetWithHttpInfo(datasetId);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get dataset statistics Returns statistics for given dataset.  Provides only [field statistics](https://docs.apify.com/platform/actors/development/actor-definition/dataset-schema/validation#dataset-field-statistics). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <returns>ApiResponse of GetDatasetStatisticsResponse</returns>
        public Apify.SDK.Client.ApiResponse<GetDatasetStatisticsResponse> DatasetStatisticsGetWithHttpInfo(string datasetId)
        {
            // verify the required parameter 'datasetId' is set
            if (datasetId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'datasetId' when calling StorageDatasetsApi->DatasetStatisticsGet");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("datasetId", Apify.SDK.Client.ClientUtils.ParameterToString(datasetId)); // path parameter

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<GetDatasetStatisticsResponse>("/v2/datasets/{datasetId}/statistics", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DatasetStatisticsGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get dataset statistics Returns statistics for given dataset.  Provides only [field statistics](https://docs.apify.com/platform/actors/development/actor-definition/dataset-schema/validation#dataset-field-statistics). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of GetDatasetStatisticsResponse</returns>
        public async System.Threading.Tasks.Task<GetDatasetStatisticsResponse> DatasetStatisticsGetAsync(string datasetId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<GetDatasetStatisticsResponse> localVarResponse = await DatasetStatisticsGetWithHttpInfoAsync(datasetId, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get dataset statistics Returns statistics for given dataset.  Provides only [field statistics](https://docs.apify.com/platform/actors/development/actor-definition/dataset-schema/validation#dataset-field-statistics). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="datasetId">Dataset ID or &#x60;username~dataset-name&#x60;.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (GetDatasetStatisticsResponse)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<GetDatasetStatisticsResponse>> DatasetStatisticsGetWithHttpInfoAsync(string datasetId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'datasetId' is set
            if (datasetId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'datasetId' when calling StorageDatasetsApi->DatasetStatisticsGet");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("datasetId", Apify.SDK.Client.ClientUtils.ParameterToString(datasetId)); // path parameter

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<GetDatasetStatisticsResponse>("/v2/datasets/{datasetId}/statistics", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DatasetStatisticsGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get list of datasets Lists all of a user&#39;s datasets.  The response is a JSON array of objects, where each object contains basic information about one dataset.  By default, the objects are sorted by the &#x60;createdAt&#x60; field in ascending order, therefore you can use pagination to incrementally fetch all datasets while new ones are still being created. To sort them in descending order, use &#x60;desc&#x3D;1&#x60; parameter. The endpoint supports pagination using &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 array elements. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="unnamed">If &#x60;true&#x60; or &#x60;1&#x60; then all the datasets are returned. By default only named datasets are returned.  (optional)</param>
        /// <returns>GetListOfDatasetsResponse</returns>
        public GetListOfDatasetsResponse DatasetsGet(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), bool? unnamed = default(bool?))
        {
            Apify.SDK.Client.ApiResponse<GetListOfDatasetsResponse> localVarResponse = DatasetsGetWithHttpInfo(offset, limit, desc, unnamed);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get list of datasets Lists all of a user&#39;s datasets.  The response is a JSON array of objects, where each object contains basic information about one dataset.  By default, the objects are sorted by the &#x60;createdAt&#x60; field in ascending order, therefore you can use pagination to incrementally fetch all datasets while new ones are still being created. To sort them in descending order, use &#x60;desc&#x3D;1&#x60; parameter. The endpoint supports pagination using &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 array elements. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="unnamed">If &#x60;true&#x60; or &#x60;1&#x60; then all the datasets are returned. By default only named datasets are returned.  (optional)</param>
        /// <returns>ApiResponse of GetListOfDatasetsResponse</returns>
        public Apify.SDK.Client.ApiResponse<GetListOfDatasetsResponse> DatasetsGetWithHttpInfo(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), bool? unnamed = default(bool?))
        {
            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (desc != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "desc", desc));
            }
            if (unnamed != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "unnamed", unnamed));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<GetListOfDatasetsResponse>("/v2/datasets", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DatasetsGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get list of datasets Lists all of a user&#39;s datasets.  The response is a JSON array of objects, where each object contains basic information about one dataset.  By default, the objects are sorted by the &#x60;createdAt&#x60; field in ascending order, therefore you can use pagination to incrementally fetch all datasets while new ones are still being created. To sort them in descending order, use &#x60;desc&#x3D;1&#x60; parameter. The endpoint supports pagination using &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 array elements. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="unnamed">If &#x60;true&#x60; or &#x60;1&#x60; then all the datasets are returned. By default only named datasets are returned.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of GetListOfDatasetsResponse</returns>
        public async System.Threading.Tasks.Task<GetListOfDatasetsResponse> DatasetsGetAsync(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), bool? unnamed = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<GetListOfDatasetsResponse> localVarResponse = await DatasetsGetWithHttpInfoAsync(offset, limit, desc, unnamed, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get list of datasets Lists all of a user&#39;s datasets.  The response is a JSON array of objects, where each object contains basic information about one dataset.  By default, the objects are sorted by the &#x60;createdAt&#x60; field in ascending order, therefore you can use pagination to incrementally fetch all datasets while new ones are still being created. To sort them in descending order, use &#x60;desc&#x3D;1&#x60; parameter. The endpoint supports pagination using &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 array elements. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="unnamed">If &#x60;true&#x60; or &#x60;1&#x60; then all the datasets are returned. By default only named datasets are returned.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (GetListOfDatasetsResponse)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<GetListOfDatasetsResponse>> DatasetsGetWithHttpInfoAsync(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), bool? unnamed = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (desc != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "desc", desc));
            }
            if (unnamed != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "unnamed", unnamed));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<GetListOfDatasetsResponse>("/v2/datasets", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DatasetsGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Create dataset Creates a dataset and returns its object. Keep in mind that data stored under unnamed dataset follows [data retention period](https://docs.apify.com/platform/storage#data-retention). It creates a dataset with the given name if the parameter name is used. If a dataset with the given name already exists then returns its object. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="name">Custom unique name to easily identify the dataset in the future. (optional)</param>
        /// <returns>DatasetResponse</returns>
        public DatasetResponse DatasetsPost(string? name = default(string?))
        {
            Apify.SDK.Client.ApiResponse<DatasetResponse> localVarResponse = DatasetsPostWithHttpInfo(name);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Create dataset Creates a dataset and returns its object. Keep in mind that data stored under unnamed dataset follows [data retention period](https://docs.apify.com/platform/storage#data-retention). It creates a dataset with the given name if the parameter name is used. If a dataset with the given name already exists then returns its object. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="name">Custom unique name to easily identify the dataset in the future. (optional)</param>
        /// <returns>ApiResponse of DatasetResponse</returns>
        public Apify.SDK.Client.ApiResponse<DatasetResponse> DatasetsPostWithHttpInfo(string? name = default(string?))
        {
            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (name != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "name", name));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<DatasetResponse>("/v2/datasets", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DatasetsPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Create dataset Creates a dataset and returns its object. Keep in mind that data stored under unnamed dataset follows [data retention period](https://docs.apify.com/platform/storage#data-retention). It creates a dataset with the given name if the parameter name is used. If a dataset with the given name already exists then returns its object. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="name">Custom unique name to easily identify the dataset in the future. (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of DatasetResponse</returns>
        public async System.Threading.Tasks.Task<DatasetResponse> DatasetsPostAsync(string? name = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<DatasetResponse> localVarResponse = await DatasetsPostWithHttpInfoAsync(name, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Create dataset Creates a dataset and returns its object. Keep in mind that data stored under unnamed dataset follows [data retention period](https://docs.apify.com/platform/storage#data-retention). It creates a dataset with the given name if the parameter name is used. If a dataset with the given name already exists then returns its object. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="name">Custom unique name to easily identify the dataset in the future. (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (DatasetResponse)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<DatasetResponse>> DatasetsPostWithHttpInfoAsync(string? name = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (name != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "name", name));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<DatasetResponse>("/v2/datasets", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("DatasetsPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

    }
}

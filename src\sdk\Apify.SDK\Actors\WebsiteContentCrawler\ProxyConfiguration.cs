﻿using System.Runtime.Serialization;

namespace Apify.SDK.Actors.WebsiteContentCrawler
{
    [DataContract(Name = "proxyConfiguration")]
    public class ProxyConfiguration
    {
        [DataMember(Name = "useApifyProxy", EmitDefaultValue = true)]
        public bool UseApifyProxy { get; set; } = true;
        [DataMember(Name = "apifyProxyGroups", EmitDefaultValue = false)]
        public string[] ApifyProxyGroups { get; set; }
        [DataMember(Name = "proxyUrls", EmitDefaultValue = false)]
        public string[] ProxyUrls { get; set; }
    }
}

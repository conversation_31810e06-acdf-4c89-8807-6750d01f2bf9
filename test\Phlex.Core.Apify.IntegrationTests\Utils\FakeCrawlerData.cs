﻿using Phlex.Core.Apify.IntegrationTests.Builders;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Models;

namespace Phlex.Core.Apify.IntegrationTests.Utils;

public class FakeCrawlerData : ICrawlerData
{
    public Task<CrawlerConfiguration> GetConfigurationAsync(string configName)
    {
        var configuration = CrawlerConfigurationBuilder.Default().WithName(configName)
            .WithJsonConfiguration(
                "{\"startUrls\": [], \"maxCrawlDepth\": 1, \"proxyConfiguration\": {\"mode\": \"global\"}}").Build();

        return Task.FromResult(configuration);
    }

    public Task<IQueryable<UrlItem>> GetStartUrlsAsync()
    {
        var urls = new List<UrlItem>()
        {
            UrlItemBuilder.Default()
                .WithId(Guid.NewGuid())
                .WithDomain(new Uri(Constants.ScrapeUrl).Host)
                .WithUrl(Constants.ScrapeUrl)
                .WithConfigurationName("config01")
                .Build()
        };

        return Task.FromResult(urls.AsQueryable());
    }
}

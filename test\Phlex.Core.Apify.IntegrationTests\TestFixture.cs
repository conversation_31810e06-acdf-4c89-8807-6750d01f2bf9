﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Phlex.Core.Apify.Extensions;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Services;
using System.Net.Http.Headers;

namespace Phlex.Core.Apify.IntegrationTests;

public class TestFixture : IDisposable
{
    private bool disposed;
    private readonly IHost? host;
    public HttpClient HttpClient;
    public IConfiguration Configuration;
    public ApifyConfiguration ApifyCfg;
    private readonly IApifyClient? _apifyClient;

    public TestFixture()
    {
        Configuration = new ConfigurationBuilder().SetBasePath(Directory.GetCurrentDirectory())
             .AddJsonFile("appsettings.Development.json", false)
             .AddUserSecrets<TestFixture>()
             .AddEnvironmentVariables()
             .Build();
        ApifyCfg = ApifyConfiguration.Get(Configuration);

        HttpClient = CreateClient(ApifyCfg.BaseUri, ApifyCfg.AccessToken);

        host = Host.CreateDefaultBuilder().ConfigureServices((_, services) =>
        {
            services.AddScoped<IDownloadService, DownloadService>(sp =>
            {
                var logger = sp.GetRequiredService<ILogger<DownloadService>>();
                return new DownloadService(logger);
            });

            services.AddScoped<IDatasetService, DatasetService>(sp =>
            {
                var logger = sp.GetRequiredService<ILogger<DatasetService>>();
                return new DatasetService(Configuration, HttpClient, logger);
            });

            services.AddScoped<ICrawlerConfigurationService, CrawlerConfigurationService>(sp =>
            {
                var logger = sp.GetRequiredService<ILogger<CrawlerConfigurationService>>();
                return new CrawlerConfigurationService(logger);
            });

            services.AddScoped(_ => ApifyCfg);

            services.AddScoped<IApifyClient, ApifyClient>(sp =>
            {
                var logger = sp.GetRequiredService<ILogger<ApifyClient>>();
                var downloadService = sp.GetRequiredService<IDownloadService>();
                var datasetService = sp.GetRequiredService<IDatasetService>();
                var crawlerConfigurationService = sp.GetRequiredService<ICrawlerConfigurationService>();
                return new ApifyClient(Configuration, HttpClient, downloadService, datasetService,
                    crawlerConfigurationService, logger);
            });
        })
        .Build();

        var serviceScope = host.Services.CreateScope();
        var serviceProvider = serviceScope.ServiceProvider;

        _apifyClient = serviceProvider.GetRequiredService<IApifyClient>();
    }

    public IApifyClient GetApifyClient() => _apifyClient!;

    public HttpClient GetHttpClient() => HttpClient;

    private static HttpClient CreateClient(string baseUrl, string token)
    {
        var httpClient = new HttpClient { BaseAddress = new Uri(baseUrl) };
        httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
        return httpClient;
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (disposed)
        {
            return;
        }
        if (disposing)
        {
            host?.Dispose();
        }

        disposed = true;
    }
}

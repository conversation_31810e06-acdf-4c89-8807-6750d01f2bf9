﻿using Newtonsoft.Json.Linq;

namespace Phlex.Core.Apify.Models;

public class WebScraperInput
{
    public bool aggressivePrune { get; set; }
    public string clickElementsCssSelector { get; set; }
    public int clientSideMinChangePercentage { get; set; }
    public string crawlerType { get; set; }
    public bool debugLog { get; set; }
    public bool debugMode { get; set; }
    public int dynamicContentWaitSecs { get; set; }
    public List<ExcludeUrlGlob> excludeUrlGlobs { get; set; }
    public bool expandIframes { get; set; }
    public bool ignoreCanonicalUrl { get; set; }
    public List<IncludeUrlGlob> includeUrlGlobs { get; set; }
    public int initialConcurrency { get; set; }
    public bool keepUrlFragments { get; set; }
    public int maxCrawlDepth { get; set; }
    public ProxyConfiguration proxyConfiguration { get; set; }
    public int readableTextCharThreshold { get; set; }
    public bool removeCookieWarnings { get; set; }
    public string removeElementsCssSelector { get; set; }
    public int renderingTypeDetectionPercentage { get; set; }
    public bool respectRobotsTxtFile { get; set; }
    public bool saveFiles { get; set; }
    public bool saveHtml { get; set; }
    public bool saveHtmlAsFile { get; set; }
    public bool saveMarkdown { get; set; }
    public bool saveScreenshots { get; set; }
    public List<StartUrl>? startUrls { get; set; }
    public bool useSitemaps { get; set; }

    public WebScraperInput()
    {
        this.aggressivePrune = false;
        this.clickElementsCssSelector = "[aria-expanded=\"false\"]";
        this.clientSideMinChangePercentage = 15;
        this.crawlerType = "playwright:firefox";
        this.debugLog = false;
        IncludeUrlGlob incglob= new IncludeUrlGlob();
        incglob.glob = "";
        this.includeUrlGlobs = new List<IncludeUrlGlob>();
        this.includeUrlGlobs.Add(incglob);
        ExcludeUrlGlob excludeUrlGlob = new ExcludeUrlGlob();
        excludeUrlGlob.glob = "";
        this.excludeUrlGlobs = new List<ExcludeUrlGlob>();
        this.excludeUrlGlobs.Add(excludeUrlGlob);
        this.debugMode = false;
        this.expandIframes = true;
        this.ignoreCanonicalUrl = false;
        this.initialConcurrency = 8;
        this.keepUrlFragments = false;
        this.maxCrawlDepth = 1;
        ProxyConfiguration proxyConfiguration = new ProxyConfiguration(); ;
        proxyConfiguration.useApifyProxy = true;
        this.proxyConfiguration = proxyConfiguration;
        this.readableTextCharThreshold = 100;
        this.removeCookieWarnings = true;
        this.removeElementsCssSelector = "nav, footer, script, style, noscript, svg, img[src^='data:'],\n[role=\"alert\"],\n[role=\"banner\"],\n[role=\"dialog\"],\n[role=\"alertdialog\"],\n[role=\"region\"][aria-label*=\"skip\" i],\n[aria-modal=\"true\"]";
        this.renderingTypeDetectionPercentage = 10;
        this.respectRobotsTxtFile = false;
        this.saveFiles = true;
        this.saveHtml = false;
        this.saveHtmlAsFile = false;
        this.saveMarkdown = true;
        this.saveScreenshots = false;
        this.useSitemaps = false;
    }
}

public class ExcludeUrlGlob
{
    public string? glob { get; set; }
}

public class IncludeUrlGlob
{
    public string? glob { get; set; }
}

public static class WebScraperInputExtensions
{
    public static WebScraperInput ToWebScraperInput(this object obj)
    {
        return ((JObject)obj).ToObject<WebScraperInput>() ??
               throw new ArgumentException($"Cannot convert \"{obj}\" to EventType", nameof(obj));
    }
}
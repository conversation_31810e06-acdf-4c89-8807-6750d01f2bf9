﻿using Apify.SDK.Model;
using Microsoft.Extensions.Configuration;
using Phlex.Core.Apify;
using Phlex.Core.Apify.Extensions;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Models;
using Microsoft.Extensions.DependencyInjection;
using Task = System.Threading.Tasks.Task;
using Newtonsoft.Json;

namespace ConsoleTest
{
    internal class Program
    {
        static async Task Main(string[] args)
        {
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json")
                .Build();

            var serviceProvider = new ServiceCollection()
                .AddSingleton<IConfiguration>(configuration)
                .AddApify(configuration).BuildServiceProvider();

            var apify = serviceProvider.GetRequiredService<IApifyClient>();

            #region JustTesting
            //DeleteAllWebhooks(apify);
            
            //Storage storage = new Storage();
            //await apify.TransferFilesAsync("cktJH9A99h8FZCXdT", "eiteBKw4zmgIBrrXh", storage); 
            
            // ReadDatasetItem();
            //if (response != null) { Console.WriteLine(response); }
            #endregion

            await Workflow01Async(apify, "DevTest009");

            Console.WriteLine("... Done! Press Enter");
            Console.ReadLine();
        }



        private static async Task Workflow01Async(IApifyClient apify, string taskName)
        {
            try
            {
                // CREATE TASK
                string taskId = await CreateTaskAsync(apify, taskName);
                await apify.CreateWebhookAsync("https://webhook.site/3480f0fa-757b-40d3-925e-31065623abd0", taskId);
                string runId = await RunTaskAsync(apify, taskId);

                // RUN TASK
                bool finished = false;
                RunResponse runResponse = null!;
                while (!finished)
                {
                    runResponse = await apify.GetRunAsync(runId);
                    Console.WriteLine(runResponse.ToString());
                    if (runResponse.Data.Status.Equals("SUCCEEDED")) { finished = true; }
                    else Thread.Sleep(5000);
                }

                // DOWNLOAD
                string dataSetID = runResponse.Data.DefaultDatasetId;
                string keyValueStoreId = runResponse.Data.DefaultKeyValueStoreId;
                Storage storage = new Storage();
                await apify.TransferFilesAsync(dataSetID, keyValueStoreId, storage);

                //CLEANUP
                //await apify.DeleteActorRunAsync(runId);
                //await apify.DeleteTaskAsync(taskId);
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
            }

        }

        // Batch process Urls
        private static async Task Workflow02Async(IApifyClient apify, string taskName)
        {
            CrawlerData crawlerData = new CrawlerData();
            string webhook = "https://webhook.site/ad4fd36e-ba7e-40ef-a56e-2a464eb8a3c1";
            await apify.RunScrapeAsync(taskName, crawlerData, webhook, TaskRunCallback);
        }

        public static async Task TaskRunCallback(BatchInfo batchInfo)
        {
            Console.WriteLine("In callback. BatchInfo: " + batchInfo.BatchNumber + " - " + batchInfo.RunId);
            await Task.Delay(300);
        }

        #region Utility
        private static async Task<string> RunTaskAsync(IApifyClient apify, string taskId)
        {
            string runId = await apify.RunTaskAsync(taskId);
            Console.WriteLine(runId);
            return runId;
        }


        private static async Task GetTasksAsync(ApifyClient apify)
        {
            ActorTasksGet200Response response = await apify.GetTasksAsync();
            foreach (var item in response.Data.Items)
            {
                Console.WriteLine(item.Name);
            }
        }

        private static async Task<string> CreateTaskAsync(IApifyClient apify, string taskName)
        {
            List<string> urls = new List<string>();
            urls.Add("https://www.phlexglobal.com/about-phlexglobal");
            string taskId = await apify.CreateTaskAsync(taskName, urls, 0);
            Console.WriteLine(taskId);
            return taskId;
        }

        private static async void DeleteAllWebhooks(ApifyClient apify)
        {
            GetListOfWebhooksResponse response = await apify.GetWebhooksAsync();
            foreach (var item in response.Data.Items)
            {
                string res = await apify.DeleteWebhookAsync(item.Id);
                Console.WriteLine();
            }
        }

        private static void ReadDatasetItem()
        {
            DirectoryInfo d = new DirectoryInfo(@"c:\temp\apify\");
            foreach (var file in d.GetFiles("*.json"))
            {
                using (TextReader fi = File.OpenText(file.FullName))
                {
                    var result = JsonConvert.DeserializeObject<WebScraperDatasetItem>(fi.ReadToEnd());
                    if (result.fileUrl!=null) Console.WriteLine("\n\nFile:  "+result.fileUrl);
                    else if (result.markdown!=null) Console.WriteLine("\n\nMD:  "+result.markdown);
                }
            }
        }
        #endregion
    }

    internal class Storage : IDownloadStorage
    {
        private string basePath = @"c:\temp\apify\";

        public void WriteDataItem(string fileName, string jsonData)
        {
            File.WriteAllText(basePath + fileName, jsonData);
        }

        public void WriteDataItem(string fileName, byte[] data)
        {
            File.WriteAllBytes(basePath + fileName, data);
        }

        public async Task WriteDataItemAsync(string fileName, Stream input, CancellationToken cancellationToken = default)
        {
            using (Stream file = File.Create(basePath + fileName))
            {
                await CopyStreamAsync(input, file);
            }
        }

        // Copies the contents of input to output. Doesn't close either stream.
        private async Task CopyStreamAsync(Stream input, Stream output)
        {
            byte[] buffer = new byte[8 * 1024];
            int len;
            while ((len = await input.ReadAsync(buffer, 0, buffer.Length)) > 0)
            {
                await output.WriteAsync(buffer, 0, len);
            }
        }

      
    }

    internal class CrawlerData : ICrawlerData
    {
        public async Task<CrawlerConfiguration> GetConfigurationAsync(string configName)
        {
            CrawlerConfiguration configuration = new CrawlerConfiguration();
            configuration.Name = configName;
            configuration.JsonConfig = File.ReadAllText("../../../ScraperInputTest01.json");

            return configuration;
        }

        public async Task<IQueryable<UrlItem>> GetStartUrlsAsync()
        {
            List<UrlItem> urls = new List<UrlItem>();
            GetTestUrlsForBatch(urls);
            return urls.AsQueryable();
        }

        private static void GetTestUrlsForBatch(List<UrlItem> urls)
        {
            UrlItem item = new UrlItem
            {
                Id = Guid.NewGuid(),
                Domain = "https://fimea.fi",
                Url = "https://fimea.fi/en/about_us/whats_new/news_archive",
                MaxCrawlDepth = 0,
                ConfigurationName = "config01"
            };
            urls.Add(item);

            item = new UrlItem
            {
                Id = Guid.NewGuid(),
                Domain = "https://fimea.fi",
                Url = "https://fimea.fi/en/marketing_authorisations/other_permits/exemption",
                MaxCrawlDepth = 0,
                ConfigurationName = "config01"
            };
            urls.Add(item);

            item = new UrlItem
            {
                Id = Guid.NewGuid(),
                Domain = "https://fimea.fi",
                Url = "https://fimea.fi/en/marketing_authorisations/renewals_and_variation_applications",
                MaxCrawlDepth = 1,
                ConfigurationName = "config01"
            };
            urls.Add(item);

            item = new UrlItem
            {
                Id = Guid.NewGuid(),
                Domain = "https://www.ima.is",
                Url = "https://www.ima.is/frettir/",
                MaxCrawlDepth = 0,
                ConfigurationName = "config01"
            };
            urls.Add(item);

            item = new UrlItem
            {
                Id = Guid.NewGuid(),
                Domain = "https://www.ima.is",
                Url = "https://www.ima.is/ima/laws-and-regulations/",
                MaxCrawlDepth = 0,
                ConfigurationName = "config01"
            };
            urls.Add(item);
        }
    }
}


/*
 * Apify API
 *
 *  > **UPDATE 2025-01-14:** > We have rolled out this new Apify API Documentation. In case of any issues, please [report here](https://github.com/apify/apify-docs/issues). > The old API Documentation is still [available here](https://docs.apify.com/api/v2-old).  The Apify API (version 2) provides programmatic access to the [Apify platform](https://docs.apify.com). The API is organized around [RESTful](https://en.wikipedia.org/wiki/Representational_state_transfer) HTTP endpoints.  You can download the complete OpenAPI schema of Apify API in the [YAML](http://docs.apify.com/api/openapi.yaml) or [JSON](http://docs.apify.com/api/openapi.json) formats. The source code is also available on [GitHub](https://github.com/apify/apify-docs/tree/master/apify-api/openapi).  All requests and responses (including errors) are encoded in [JSON](http://www.json.org/) format with UTF-8 encoding, with a few exceptions that are explicitly described in the reference.  To access the API using [Node.js](https://nodejs.org/en/), we recommend the [`apify-client`](https://docs.apify.com/api/client/js) [NPM package](https://www.npmjs.com/package/apify-client).  To access the API using [Python](https://www.python.org/), we recommend the [`apify-client`](https://docs.apify.com/api/client/python) [PyPI package](https://pypi.org/project/apify-client/). The clients' functions correspond to the API endpoints and have the same parameters. This simplifies development of apps that depend on the Apify platform.  **Note:** All requests with JSON payloads need to specify the `Content-Type: application/json` HTTP header! All API endpoints support the `method` query parameter that can override the HTTP method. For example, if you want to call a POST endpoint using a GET request, simply add the query parameter `method=POST` to the URL and send the GET request. This feature is especially useful if you want to call Apify API endpoints from services that can only send GET requests.  ## Authentication <span id=\"/introduction/authentication\"></span>  You can find your API token on the [Integrations](https://console.apify.com/account#/integrations) page in the Apify Console.  To use your token in a request, either:  - Add the token to your request's `Authorization` header as `Bearer <token>`. E.g., `Authorization: Bearer xxxxxxx`. [More info](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Authorization). (Recommended). - Add it as the `token` parameter to your request URL. (Less secure).  Using your token in the request header is more secure than using it as a URL parameter because URLs are often stored in browser history and server logs. This creates a chance for someone unauthorized to access your API token.  **Do not share your API token or password with untrusted parties.**  For more information, see our [integrations](https://docs.apify.com/platform/integrations) documentation.  ## Basic usage <span id=\"/introduction/basic-usage\"></span>  To run an Actor, send a POST request to the [Run Actor](#/reference/actors/run-collection/run-actor) endpoint using either the Actor ID code (e.g. `vKg4IjxZbEYTYeW8T`) or its name (e.g. `janedoe~my-actor`):  `https://api.apify.com/v2/acts/[actor_id]/runs`  If the Actor is not runnable anonymously, you will receive a 401 or 403 [response code](https://developer.mozilla.org/en-US/docs/Web/HTTP/Status). This means you need to add your [secret API token](https://console.apify.com/account#/integrations) to the request's `Authorization` header ([recommended](#/introduction/authentication)) or as a URL query parameter `?token=[your_token]` (less secure).  Optionally, you can include the query parameters described in the [Run Actor](#/reference/actors/run-collection/run-actor) section to customize your run.  If you're using Node.js, the best way to run an Actor is using the `Apify.call()` method from the [Apify SDK](https://sdk.apify.com/docs/api/apify#apifycallactid-input-options). It runs the Actor using the account you are currently logged into (determined by the [secret API token](https://console.apify.com/account#/integrations)). The result is an [Actor run object](https://sdk.apify.com/docs/typedefs/actor-run) and its output (if any).  A typical workflow is as follows:  1. Run an Actor or task using the [Run Actor](#/reference/actors/run-collection/run-actor) or [Run task](#/reference/actor-tasks/run-collection/run-task) API endpoints. 2. Monitor the Actor run by periodically polling its progress using the [Get run](#/reference/actor-runs/run-object-and-its-storages/get-run) API endpoint. 3. Fetch the results from the [Get items](#/reference/datasets/item-collection/get-items) API endpoint using the `defaultDatasetId`, which you receive in the Run request response. Additional data may be stored in a key-value store. You can fetch them from the [Get record](#/reference/key-value-stores/record/get-record) API endpoint using the `defaultKeyValueStoreId` and the store's `key`.  **Note**: Instead of periodic polling, you can also run your [Actor](#/reference/actors/run-actor-synchronously) or [task](#/reference/actor-tasks/runs-collection/run-task-synchronously) synchronously. This will ensure that the request waits for 300 seconds (5 minutes) for the run to finish and returns its output. If the run takes longer, the request will time out and throw an error.  ## Response structure <span id=\"/introduction/response-structure\"></span>  Most API endpoints return a JSON object with the `data` property:  ``` {     \"data\": {         ...     } } ```  However, there are a few explicitly described exceptions, such as Dataset [Get items](#/reference/datasets/item-collection/get-items) or Key-value store [Get record](#/reference/key-value-stores/record/get-record) API endpoints, which return data in other formats. In case of an error, the response has the HTTP status code in the range of 4xx or 5xx and the `data` property is replaced with `error`. For example:  ``` {     \"error\": {         \"type\": \"record-not-found\",         \"message\": \"Store was not found.\"     } } ```  See [Errors](#/introduction/errors) for more details.  ## Pagination <span id=\"/introduction/pagination\"></span>  All API endpoints that return a list of records (e.g. [Get list of Actors](#/reference/actors/actor-collection/get-list-of-actors)) enforce pagination in order to limit the size of their responses.  Most of these API endpoints are paginated using the `offset` and `limit` query parameters. The only exception is [Get list of keys](#/reference/key-value-stores/key-collection/get-list-of-keys), which is paginated using the `exclusiveStartKey` query parameter.  **IMPORTANT**: Each API endpoint that supports pagination enforces a certain maximum value for the `limit` parameter, in order to reduce the load on Apify servers. The maximum limit could change in future so you should never rely on a specific value and check the responses of these API endpoints.  ### Using offset <span id=\"/introduction/pagination/using-offset\"></span>  Most API endpoints that return a list of records enable pagination using the following query parameters:  <table>   <tr>     <td><code>limit</code></td>     <td>Limits the response to contain a specific maximum number of items, e.g. <code>limit=20</code>.</td>   </tr>   <tr>     <td><code>offset</code></td>     <td>Skips a number of items from the beginning of the list, e.g. <code>offset=100</code>.</td>   </tr>   <tr>     <td><code>desc</code></td>     <td>     By default, items are sorted in the order in which they were created or added to the list.     This feature is useful when fetching all the items, because it ensures that items     created after the client started the pagination will not be skipped.     If you specify the <code>desc=1</code> parameter, the items will be returned in the reverse order,     i.e. from the newest to the oldest items.     </td>   </tr> </table>  The response of these API endpoints is always a JSON object with the following structure:  ``` {     \"data\": {         \"total\": 2560,         \"offset\": 250,         \"limit\": 1000,         \"count\": 1000,         \"desc\": false,         \"items\": [             { 1st object },             { 2nd object },             ...             { 1000th object }         ]     } } ```  The following table describes the meaning of the response properties:  <table>   <tr>     <th>Property</th>     <th>Description</th>   </tr>   <tr>     <td><code>total</code></td>     <td>The total number of items available in the list.</td>   </tr>   <tr>     <td><code>offset</code></td>     <td>The number of items that were skipped at the start.     This is equal to the <code>offset</code> query parameter if it was provided, otherwise it is <code>0</code>.</td>   </tr>   <tr>     <td><code>limit</code></td>     <td>The maximum number of items that can be returned in the HTTP response.     It equals to the <code>limit</code> query parameter if it was provided or     the maximum limit enforced for the particular API endpoint, whichever is smaller.</td>   </tr>   <tr>     <td><code>count</code></td>     <td>The actual number of items returned in the HTTP response.</td>   </tr>   <tr>     <td><code>desc</code></td>     <td><code>true</code> if data were requested in descending order and <code>false</code> otherwise.</td>   </tr>   <tr>     <td><code>items</code></td>     <td>An array of requested items.</td>   </tr> </table>  ### Using key <span id=\"/introduction/pagination/using-key\"></span>  The records in the [key-value store](https://docs.apify.com/platform/storage/key-value-store) are not ordered based on numerical indexes, but rather by their keys in the UTF-8 binary order. Therefore the [Get list of keys](#/reference/key-value-stores/key-collection/get-list-of-keys) API endpoint only supports pagination using the following query parameters:  <table>   <tr>     <td><code>limit</code></td>     <td>Limits the response to contain a specific maximum number items, e.g. <code>limit=20</code>.</td>   </tr>   <tr>     <td><code>exclusiveStartKey</code></td>     <td>Skips all records with keys up to the given key including the given key,     in the UTF-8 binary order.</td>   </tr> </table>  The response of the API endpoint is always a JSON object with following structure:  ``` {     \"data\": {         \"limit\": 1000,         \"isTruncated\": true,         \"exclusiveStartKey\": \"my-key\",         \"nextExclusiveStartKey\": \"some-other-key\",         \"items\": [             { 1st object },             { 2nd object },             ...             { 1000th object }         ]     } } ```  The following table describes the meaning of the response properties:  <table>   <tr>     <th>Property</th>     <th>Description</th>   </tr>   <tr>     <td><code>limit</code></td>     <td>The maximum number of items that can be returned in the HTTP response.     It equals to the <code>limit</code> query parameter if it was provided or     the maximum limit enforced for the particular endpoint, whichever is smaller.</td>   </tr>   <tr>     <td><code>isTruncated</code></td>     <td><code>true</code> if there are more items left to be queried. Otherwise <code>false</code>.</td>   </tr>   <tr>     <td><code>exclusiveStartKey</code></td>     <td>The last key that was skipped at the start. Is `null` for the first page.</td>   </tr>   <tr>     <td><code>nextExclusiveStartKey</code></td>     <td>The value for the <code>exclusiveStartKey</code> parameter to query the next page of items.</td>   </tr> </table>  ## Errors <span id=\"/introduction/errors\"></span>  The Apify API uses common HTTP status codes: `2xx` range for success, `4xx` range for errors caused by the caller (invalid requests) and `5xx` range for server errors (these are rare). Each error response contains a JSON object defining the `error` property, which is an object with the `type` and `message` properties that contain the error code and a human-readable error description, respectively.  For example:  ``` {     \"error\": {         \"type\": \"record-not-found\",         \"message\": \"Store was not found.\"     } } ```  Here is the table of the most common errors that can occur for many API endpoints:  <table>   <tr>     <th>status</th>     <th>type</th>     <th>message</th>   </tr>   <tr>     <td><code>400</code></td>     <td><code>invalid-request</code></td>     <td>POST data must be a JSON object</td>   </tr>   <tr>     <td><code>400</code></td>     <td><code>invalid-value</code></td>     <td>Invalid value provided: Comments required</td>   </tr>   <tr>     <td><code>400</code></td>     <td><code>invalid-record-key</code></td>     <td>Record key contains invalid character</td>   </tr>   <tr>     <td><code>401</code></td>     <td><code>token-not-provided</code></td>     <td>Authentication token was not provided</td>   </tr>   <tr>     <td><code>404</code></td>     <td><code>record-not-found</code></td>     <td>Store was not found</td>   </tr>   <tr>     <td><code>429</code></td>     <td><code>rate-limit-exceeded</code></td>     <td>You have exceeded the rate limit of 30 requests per second</td>   </tr>   <tr>     <td><code>405</code></td>     <td><code>method-not-allowed</code></td>     <td>This API endpoint can only be accessed using the following HTTP methods: OPTIONS, POST</td>   </tr> </table>  ## Rate limiting <span id=\"/introduction/rate-limiting\"></span>  All API endpoints limit the rate of requests in order to prevent overloading of Apify servers by misbehaving clients.  There are two kinds of rate limits - a global rate limit and a per-resource rate limit.  ### Global rate limit <span id=\"/introduction/rate-limiting/global-rate-limit\"></span>  The global rate limit is set to _250 000 requests per minute_. For [authenticated](#/introduction/authentication) requests, it is counted per user, and for unauthenticated requests, it is counted per IP address.  ### Per-resource rate limit <span id=\"/introduction/rate-limiting/per-resource-rate-limit\"></span>  The default per-resource rate limit is _30 requests per second per resource_, which in this context means a single Actor, a single Actor run, a single dataset, single key-value store etc. The default rate limit is applied to every API endpoint except a few select ones, which have higher rate limits. Each API endpoint returns its rate limit in `X-RateLimit-Limit` header.  These endpoints have a rate limit of _100 requests per second per resource_:  * CRUD ([get](#/reference/key-value-stores/record/get-record),   [put](#/reference/key-value-stores/record/put-record),   [delete](#/reference/key-value-stores/record/delete-record))   operations on key-value store records  These endpoints have a rate limit of _200 requests per second per resource_: * [Run Actor](#/reference/actors/run-collection/run-actor) * [Run Actor task asynchronously](#/reference/actor-tasks/runs-collection/run-task-asynchronously) * [Run Actor task synchronously](#/reference/actor-tasks/runs-collection/run-task-synchronously) * [Metamorph Actor run](#/reference/actors/metamorph-run/metamorph-run) * [Push items](#/reference/datasets/item-collection/put-items) to dataset * CRUD   ([add](#/reference/request-queues/request-collection/add-request),   [get](#/reference/request-queues/request-collection/get-request),   [update](#/reference/request-queues/request-collection/update-request),   [delete](#/reference/request-queues/request-collection/delete-request))   operations on requests in request queues  ### Rate limit exceeded errors <span id=\"/introduction/rate-limiting/rate-limit-exceeded-errors\"></span>  If the client is sending too many requests, the API endpoints respond with the HTTP status code `429 Too Many Requests` and the following body:  ``` {     \"error\": {         \"type\": \"rate-limit-exceeded\",         \"message\": \"You have exceeded the rate limit of ... requests per second\"     } } ```  ### Retrying rate-limited requests with exponential backoff <span id=\"/introduction/rate-limiting/retrying-rate-limited-requests-with-exponential-backoff\"></span>  If the client receives the rate limit error, it should wait a certain period of time and then retry the request. If the error happens again, the client should double the wait period and retry the request, and so on. This algorithm is known as _exponential backoff_ and it can be described using the following pseudo-code:  1. Define a variable `DELAY=500` 2. Send the HTTP request to the API endpoint 3. If the response has status code not equal to `429` then you are done. Otherwise:    * Wait for a period of time chosen randomly from the interval `DELAY` to `2*DELAY` milliseconds    * Double the future wait period by setting `DELAY = 2*DELAY`    * Continue with step 2  If all requests sent by the client implement the above steps, the client will automatically use the maximum available bandwidth for its requests.  Note that the Apify API clients [for JavaScript](https://docs.apify.com/api/client/js) and [for Python](https://docs.apify.com/api/client/python) use the exponential backoff algorithm transparently, so that you do not need to worry about it.  ## Referring to resources <span id=\"/introduction/referring-to-resources\"></span>  There are three main ways to refer to a resource you're accessing via API.  - the resource ID (e.g. `iKkPcIgVvwmztduf8`) - `username~resourcename` - when using this access method, you will need to use your API token, and access will only work if you have the correct permissions. - `~resourcename` - for this, you need to use an API token, and the `resourcename` refers to a resource in the API token owner's account. 
 *
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Mime;
using Apify.SDK.Client;
using Apify.SDK.Model;

namespace Apify.SDK.Api
{

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IActorTasksApiSync : IApiAccessor
    {
        #region Synchronous Operations
        /// <summary>
        /// Delete task
        /// </summary>
        /// <remarks>
        /// Delete the task specified through the &#x60;actorTaskId&#x60; parameter.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <returns>Object</returns>
        Object ActorTaskDelete(string actorTaskId);

        /// <summary>
        /// Delete task
        /// </summary>
        /// <remarks>
        /// Delete the task specified through the &#x60;actorTaskId&#x60; parameter.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <returns>ApiResponse of Object</returns>
        ApiResponse<Object> ActorTaskDeleteWithHttpInfo(string actorTaskId);
        /// <summary>
        /// Get task
        /// </summary>
        /// <remarks>
        /// Get an object that contains all the details about a task.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <returns>ActorTasksPost201Response</returns>
        ActorTasksPost201Response ActorTaskGet(string actorTaskId);

        /// <summary>
        /// Get task
        /// </summary>
        /// <remarks>
        /// Get an object that contains all the details about a task.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <returns>ApiResponse of ActorTasksPost201Response</returns>
        ApiResponse<ActorTasksPost201Response> ActorTaskGetWithHttpInfo(string actorTaskId);
        /// <summary>
        /// Get task input
        /// </summary>
        /// <remarks>
        /// Returns the input of a given task.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <returns>Object</returns>
        Object ActorTaskInputGet(string actorTaskId);

        /// <summary>
        /// Get task input
        /// </summary>
        /// <remarks>
        /// Returns the input of a given task.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <returns>ApiResponse of Object</returns>
        ApiResponse<Object> ActorTaskInputGetWithHttpInfo(string actorTaskId);
        /// <summary>
        /// Update task input
        /// </summary>
        /// <remarks>
        /// Updates the input of a task using values specified by an object passed as JSON in the PUT payload.  If the object does not define a specific property, its value is not updated.  The response is the full task input as returned by the [Get task input](#/reference/tasks/task-input-object/get-task-input) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <returns>Object</returns>
        Object ActorTaskInputPut(string actorTaskId, Object body);

        /// <summary>
        /// Update task input
        /// </summary>
        /// <remarks>
        /// Updates the input of a task using values specified by an object passed as JSON in the PUT payload.  If the object does not define a specific property, its value is not updated.  The response is the full task input as returned by the [Get task input](#/reference/tasks/task-input-object/get-task-input) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <returns>ApiResponse of Object</returns>
        ApiResponse<Object> ActorTaskInputPutWithHttpInfo(string actorTaskId, Object body);
        /// <summary>
        /// Update task
        /// </summary>
        /// <remarks>
        /// Update settings of a task using values specified by an object passed as JSON in the POST payload.  If the object does not define a specific property, its value is not updated.  The response is the full task object as returned by the [Get task](#/reference/tasks/task-object/get-task) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="updateTaskRequest"></param>
        /// <returns>ActorTasksPost201Response</returns>
        ActorTasksPost201Response ActorTaskPut(string actorTaskId, UpdateTaskRequest updateTaskRequest);

        /// <summary>
        /// Update task
        /// </summary>
        /// <remarks>
        /// Update settings of a task using values specified by an object passed as JSON in the POST payload.  If the object does not define a specific property, its value is not updated.  The response is the full task object as returned by the [Get task](#/reference/tasks/task-object/get-task) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="updateTaskRequest"></param>
        /// <returns>ApiResponse of ActorTasksPost201Response</returns>
        ApiResponse<ActorTasksPost201Response> ActorTaskPutWithHttpInfo(string actorTaskId, UpdateTaskRequest updateTaskRequest);
        /// <summary>
        /// Run task synchronously
        /// </summary>
        /// <remarks>
        /// Run a specific task and return its output.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  To run the Task asynchronously, use the [Run task asynchronously](#/reference/actor-tasks/run-collection/run-task) endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <returns>Object</returns>
        Object ActorTaskRunSyncGet(string actorTaskId, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), string? build = default(string?), string? outputRecordKey = default(string?), string? webhooks = default(string?));

        /// <summary>
        /// Run task synchronously
        /// </summary>
        /// <remarks>
        /// Run a specific task and return its output.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  To run the Task asynchronously, use the [Run task asynchronously](#/reference/actor-tasks/run-collection/run-task) endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <returns>ApiResponse of Object</returns>
        ApiResponse<Object> ActorTaskRunSyncGetWithHttpInfo(string actorTaskId, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), string? build = default(string?), string? outputRecordKey = default(string?), string? webhooks = default(string?));
        /// <summary>
        /// Run task synchronously and get dataset items
        /// </summary>
        /// <remarks>
        /// Run a specific task and return its dataset items.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  You can send all the same options in parameters as the [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  To run the Task asynchronously, use the [Run task asynchronously](#/reference/actor-tasks/run-collection/run-task) endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields  (i.e. fields starting with the # character).  The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters.  Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items,  only these fields will remain in the resulting record objects.  Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter.  You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object.  If the field is an array then every element of  the array will become a separate record and merged with parent object.  If the unwound field is an object then it is merged with the parent object  If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is.  Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures.  For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;.  The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with  the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not.  If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output. This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <returns>Object</returns>
        Object ActorTaskRunSyncGetDatasetItemsGet(string actorTaskId, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?));

        /// <summary>
        /// Run task synchronously and get dataset items
        /// </summary>
        /// <remarks>
        /// Run a specific task and return its dataset items.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  You can send all the same options in parameters as the [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  To run the Task asynchronously, use the [Run task asynchronously](#/reference/actor-tasks/run-collection/run-task) endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields  (i.e. fields starting with the # character).  The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters.  Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items,  only these fields will remain in the resulting record objects.  Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter.  You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object.  If the field is an array then every element of  the array will become a separate record and merged with parent object.  If the unwound field is an object then it is merged with the parent object  If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is.  Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures.  For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;.  The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with  the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not.  If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output. This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <returns>ApiResponse of Object</returns>
        ApiResponse<Object> ActorTaskRunSyncGetDatasetItemsGetWithHttpInfo(string actorTaskId, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?));
        /// <summary>
        /// Run task synchronously and get dataset items
        /// </summary>
        /// <remarks>
        /// Runs an Actor task and synchronously returns its dataset items.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  Optionally, you can override the Actor input configuration by passing a JSON object as the POST payload and setting the &#x60;Content-Type: application/json&#x60; HTTP header.  Note that if the object in the POST payload does not define a particular input property, the Actor run uses the default value defined by the task (or the Actor&#39;s input schema if not defined by the task).  You can send all the same options in parameters as the [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  Input fields from Actor task configuration can be overloaded with values passed as the POST payload.  Just make sure to specify the &#x60;Content-Type&#x60; header as &#x60;application/json&#x60; and that the input is an object.  To run the task asynchronously, use the [Run task](#/reference/actor-tasks/run-collection/run-task) API endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the task run. This is useful for pay-per-event tasks, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures. For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;. The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not. If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60;  and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the  legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output.  This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <returns>Object</returns>
        Object ActorTaskRunSyncGetDatasetItemsPost(string actorTaskId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?));

        /// <summary>
        /// Run task synchronously and get dataset items
        /// </summary>
        /// <remarks>
        /// Runs an Actor task and synchronously returns its dataset items.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  Optionally, you can override the Actor input configuration by passing a JSON object as the POST payload and setting the &#x60;Content-Type: application/json&#x60; HTTP header.  Note that if the object in the POST payload does not define a particular input property, the Actor run uses the default value defined by the task (or the Actor&#39;s input schema if not defined by the task).  You can send all the same options in parameters as the [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  Input fields from Actor task configuration can be overloaded with values passed as the POST payload.  Just make sure to specify the &#x60;Content-Type&#x60; header as &#x60;application/json&#x60; and that the input is an object.  To run the task asynchronously, use the [Run task](#/reference/actor-tasks/run-collection/run-task) API endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the task run. This is useful for pay-per-event tasks, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures. For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;. The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not. If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60;  and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the  legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output.  This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <returns>ApiResponse of Object</returns>
        ApiResponse<Object> ActorTaskRunSyncGetDatasetItemsPostWithHttpInfo(string actorTaskId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?));
        /// <summary>
        /// Run task synchronously
        /// </summary>
        /// <remarks>
        /// Runs an Actor task and synchronously returns its output.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  Optionally, you can override the Actor input configuration by passing a JSON object as the POST payload and setting the &#x60;Content-Type: application/json&#x60; HTTP header.  Note that if the object in the POST payload does not define a particular input property, the Actor run uses the default value defined by the task (or Actor&#39;s input schema if not defined by the task).  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  Input fields from Actor task configuration can be overloaded with values passed as the POST payload.  Just make sure to specify &#x60;Content-Type&#x60; header to be &#x60;application/json&#x60; and input to be an object.  To run the task asynchronously, use the [Run task](#/reference/actor-tasks/run-collection/run-task) API endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the task run. This parameter is useful for pay-per-event tasks, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification  e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see  [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <returns>Object</returns>
        Object ActorTaskRunSyncPost(string actorTaskId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? outputRecordKey = default(string?), string? webhooks = default(string?));

        /// <summary>
        /// Run task synchronously
        /// </summary>
        /// <remarks>
        /// Runs an Actor task and synchronously returns its output.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  Optionally, you can override the Actor input configuration by passing a JSON object as the POST payload and setting the &#x60;Content-Type: application/json&#x60; HTTP header.  Note that if the object in the POST payload does not define a particular input property, the Actor run uses the default value defined by the task (or Actor&#39;s input schema if not defined by the task).  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  Input fields from Actor task configuration can be overloaded with values passed as the POST payload.  Just make sure to specify &#x60;Content-Type&#x60; header to be &#x60;application/json&#x60; and input to be an object.  To run the task asynchronously, use the [Run task](#/reference/actor-tasks/run-collection/run-task) API endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the task run. This parameter is useful for pay-per-event tasks, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification  e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see  [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <returns>ApiResponse of Object</returns>
        ApiResponse<Object> ActorTaskRunSyncPostWithHttpInfo(string actorTaskId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? outputRecordKey = default(string?), string? webhooks = default(string?));
        /// <summary>
        /// Get list of task runs
        /// </summary>
        /// <remarks>
        /// Get a list of runs of a specific task. The response is a list of objects, where each object contains essential information about a single task run.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters, and it does not return more than a 1000 array elements.  By default, the records are sorted by the &#x60;startedAt&#x60; field in ascending order; therefore you can use pagination to incrementally fetch all records while new ones are still being created. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. You can also filter runs by status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle)). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="status">Return only runs with the provided status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle))  (optional)</param>
        /// <returns>ActorTaskRunsGet200Response</returns>
        ActorTaskRunsGet200Response ActorTaskRunsGet(string actorTaskId, double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), string? status = default(string?));

        /// <summary>
        /// Get list of task runs
        /// </summary>
        /// <remarks>
        /// Get a list of runs of a specific task. The response is a list of objects, where each object contains essential information about a single task run.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters, and it does not return more than a 1000 array elements.  By default, the records are sorted by the &#x60;startedAt&#x60; field in ascending order; therefore you can use pagination to incrementally fetch all records while new ones are still being created. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. You can also filter runs by status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle)). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="status">Return only runs with the provided status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle))  (optional)</param>
        /// <returns>ApiResponse of ActorTaskRunsGet200Response</returns>
        ApiResponse<ActorTaskRunsGet200Response> ActorTaskRunsGetWithHttpInfo(string actorTaskId, double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), string? status = default(string?));
        /// <summary>
        /// Run task
        /// </summary>
        /// <remarks>
        /// Runs an Actor task and immediately returns without waiting for the run to finish.  Optionally, you can override the Actor input configuration by passing a JSON object as the POST payload and setting the &#x60;Content-Type: application/json&#x60; HTTP header.  Note that if the object in the POST payload does not define a particular input property, the Actor run uses the default value defined by the task (or Actor&#39;s input schema if not defined by the task).  The response is the Actor Run object as returned by the [Get run](#/reference/actor-runs/run-object-and-its-storages/get-run) endpoint.  If you want to wait for the run to finish and receive the actual output of the Actor run as the response, use one of the [Run task synchronously](#/reference/actor-tasks/run-task-synchronously) API endpoints instead.  To fetch the Actor run results that are typically stored in the default dataset, you&#39;ll need to pass the ID received in the &#x60;defaultDatasetId&#x60; field received in the response JSON to the [Get items](#/reference/datasets/item-collection/get-items) API endpoint. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default, it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks.  **Note**: if you already have a webhook set up for the Actor or task, you do not have to add it again here.  For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <returns>ActorTaskRunsPost201Response</returns>
        ActorTaskRunsPost201Response ActorTaskRunsPost(string actorTaskId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), double? waitForFinish = default(double?), string? webhooks = default(string?));

        /// <summary>
        /// Run task
        /// </summary>
        /// <remarks>
        /// Runs an Actor task and immediately returns without waiting for the run to finish.  Optionally, you can override the Actor input configuration by passing a JSON object as the POST payload and setting the &#x60;Content-Type: application/json&#x60; HTTP header.  Note that if the object in the POST payload does not define a particular input property, the Actor run uses the default value defined by the task (or Actor&#39;s input schema if not defined by the task).  The response is the Actor Run object as returned by the [Get run](#/reference/actor-runs/run-object-and-its-storages/get-run) endpoint.  If you want to wait for the run to finish and receive the actual output of the Actor run as the response, use one of the [Run task synchronously](#/reference/actor-tasks/run-task-synchronously) API endpoints instead.  To fetch the Actor run results that are typically stored in the default dataset, you&#39;ll need to pass the ID received in the &#x60;defaultDatasetId&#x60; field received in the response JSON to the [Get items](#/reference/datasets/item-collection/get-items) API endpoint. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default, it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks.  **Note**: if you already have a webhook set up for the Actor or task, you do not have to add it again here.  For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <returns>ApiResponse of ActorTaskRunsPost201Response</returns>
        ApiResponse<ActorTaskRunsPost201Response> ActorTaskRunsPostWithHttpInfo(string actorTaskId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), double? waitForFinish = default(double?), string? webhooks = default(string?));
        /// <summary>
        /// Get list of webhooks
        /// </summary>
        /// <remarks>
        /// Gets the list of webhooks of a specific Actor task. The response is a JSON with the list of objects, where each object contains basic information about a single webhook.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 records.  By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order, to sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <returns>ActorTaskWebhooksGet200Response</returns>
        ActorTaskWebhooksGet200Response ActorTaskWebhooksGet(string actorTaskId, double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?));

        /// <summary>
        /// Get list of webhooks
        /// </summary>
        /// <remarks>
        /// Gets the list of webhooks of a specific Actor task. The response is a JSON with the list of objects, where each object contains basic information about a single webhook.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 records.  By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order, to sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <returns>ApiResponse of ActorTaskWebhooksGet200Response</returns>
        ApiResponse<ActorTaskWebhooksGet200Response> ActorTaskWebhooksGetWithHttpInfo(string actorTaskId, double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?));
        /// <summary>
        /// Get list of tasks
        /// </summary>
        /// <remarks>
        /// Gets the complete list of tasks that a user has created or used.  The response is a list of objects in which each object contains essential information about a single task.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters, and it does not return more than a 1000 records.  By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order; therefore you can use pagination to incrementally fetch all tasks while new ones are still being created. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of records that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of records to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <returns>ActorTasksGet200Response</returns>
        ActorTasksGet200Response ActorTasksGet(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?));

        /// <summary>
        /// Get list of tasks
        /// </summary>
        /// <remarks>
        /// Gets the complete list of tasks that a user has created or used.  The response is a list of objects in which each object contains essential information about a single task.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters, and it does not return more than a 1000 records.  By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order; therefore you can use pagination to incrementally fetch all tasks while new ones are still being created. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of records that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of records to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <returns>ApiResponse of ActorTasksGet200Response</returns>
        ApiResponse<ActorTasksGet200Response> ActorTasksGetWithHttpInfo(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?));
        /// <summary>
        /// Create task
        /// </summary>
        /// <remarks>
        /// Create a new task with settings specified by the object passed as JSON in the POST payload.  The response is the full task object as returned by the [Get task](#/reference/tasks/task-object/get-task) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTasksPostRequest"></param>
        /// <returns>ActorTasksPost201Response</returns>
        ActorTasksPost201Response ActorTasksPost(ActorTasksPostRequest actorTasksPostRequest);

        /// <summary>
        /// Create task
        /// </summary>
        /// <remarks>
        /// Create a new task with settings specified by the object passed as JSON in the POST payload.  The response is the full task object as returned by the [Get task](#/reference/tasks/task-object/get-task) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTasksPostRequest"></param>
        /// <returns>ApiResponse of ActorTasksPost201Response</returns>
        ApiResponse<ActorTasksPost201Response> ActorTasksPostWithHttpInfo(ActorTasksPostRequest actorTasksPostRequest);
        #endregion Synchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IActorTasksApiAsync : IApiAccessor
    {
        #region Asynchronous Operations
        /// <summary>
        /// Delete task
        /// </summary>
        /// <remarks>
        /// Delete the task specified through the &#x60;actorTaskId&#x60; parameter.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        System.Threading.Tasks.Task<Object> ActorTaskDeleteAsync(string actorTaskId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Delete task
        /// </summary>
        /// <remarks>
        /// Delete the task specified through the &#x60;actorTaskId&#x60; parameter.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> ActorTaskDeleteWithHttpInfoAsync(string actorTaskId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Get task
        /// </summary>
        /// <remarks>
        /// Get an object that contains all the details about a task.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ActorTasksPost201Response</returns>
        System.Threading.Tasks.Task<ActorTasksPost201Response> ActorTaskGetAsync(string actorTaskId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Get task
        /// </summary>
        /// <remarks>
        /// Get an object that contains all the details about a task.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ActorTasksPost201Response)</returns>
        System.Threading.Tasks.Task<ApiResponse<ActorTasksPost201Response>> ActorTaskGetWithHttpInfoAsync(string actorTaskId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Get task input
        /// </summary>
        /// <remarks>
        /// Returns the input of a given task.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        System.Threading.Tasks.Task<Object> ActorTaskInputGetAsync(string actorTaskId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Get task input
        /// </summary>
        /// <remarks>
        /// Returns the input of a given task.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> ActorTaskInputGetWithHttpInfoAsync(string actorTaskId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Update task input
        /// </summary>
        /// <remarks>
        /// Updates the input of a task using values specified by an object passed as JSON in the PUT payload.  If the object does not define a specific property, its value is not updated.  The response is the full task input as returned by the [Get task input](#/reference/tasks/task-input-object/get-task-input) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        System.Threading.Tasks.Task<Object> ActorTaskInputPutAsync(string actorTaskId, Object body, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Update task input
        /// </summary>
        /// <remarks>
        /// Updates the input of a task using values specified by an object passed as JSON in the PUT payload.  If the object does not define a specific property, its value is not updated.  The response is the full task input as returned by the [Get task input](#/reference/tasks/task-input-object/get-task-input) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> ActorTaskInputPutWithHttpInfoAsync(string actorTaskId, Object body, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Update task
        /// </summary>
        /// <remarks>
        /// Update settings of a task using values specified by an object passed as JSON in the POST payload.  If the object does not define a specific property, its value is not updated.  The response is the full task object as returned by the [Get task](#/reference/tasks/task-object/get-task) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="updateTaskRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ActorTasksPost201Response</returns>
        System.Threading.Tasks.Task<ActorTasksPost201Response> ActorTaskPutAsync(string actorTaskId, UpdateTaskRequest updateTaskRequest, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Update task
        /// </summary>
        /// <remarks>
        /// Update settings of a task using values specified by an object passed as JSON in the POST payload.  If the object does not define a specific property, its value is not updated.  The response is the full task object as returned by the [Get task](#/reference/tasks/task-object/get-task) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="updateTaskRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ActorTasksPost201Response)</returns>
        System.Threading.Tasks.Task<ApiResponse<ActorTasksPost201Response>> ActorTaskPutWithHttpInfoAsync(string actorTaskId, UpdateTaskRequest updateTaskRequest, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Run task synchronously
        /// </summary>
        /// <remarks>
        /// Run a specific task and return its output.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  To run the Task asynchronously, use the [Run task asynchronously](#/reference/actor-tasks/run-collection/run-task) endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        System.Threading.Tasks.Task<Object> ActorTaskRunSyncGetAsync(string actorTaskId, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), string? build = default(string?), string? outputRecordKey = default(string?), string? webhooks = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Run task synchronously
        /// </summary>
        /// <remarks>
        /// Run a specific task and return its output.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  To run the Task asynchronously, use the [Run task asynchronously](#/reference/actor-tasks/run-collection/run-task) endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> ActorTaskRunSyncGetWithHttpInfoAsync(string actorTaskId, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), string? build = default(string?), string? outputRecordKey = default(string?), string? webhooks = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Run task synchronously and get dataset items
        /// </summary>
        /// <remarks>
        /// Run a specific task and return its dataset items.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  You can send all the same options in parameters as the [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  To run the Task asynchronously, use the [Run task asynchronously](#/reference/actor-tasks/run-collection/run-task) endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields  (i.e. fields starting with the # character).  The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters.  Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items,  only these fields will remain in the resulting record objects.  Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter.  You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object.  If the field is an array then every element of  the array will become a separate record and merged with parent object.  If the unwound field is an object then it is merged with the parent object  If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is.  Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures.  For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;.  The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with  the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not.  If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output. This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        System.Threading.Tasks.Task<Object> ActorTaskRunSyncGetDatasetItemsGetAsync(string actorTaskId, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Run task synchronously and get dataset items
        /// </summary>
        /// <remarks>
        /// Run a specific task and return its dataset items.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  You can send all the same options in parameters as the [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  To run the Task asynchronously, use the [Run task asynchronously](#/reference/actor-tasks/run-collection/run-task) endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields  (i.e. fields starting with the # character).  The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters.  Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items,  only these fields will remain in the resulting record objects.  Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter.  You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object.  If the field is an array then every element of  the array will become a separate record and merged with parent object.  If the unwound field is an object then it is merged with the parent object  If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is.  Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures.  For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;.  The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with  the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not.  If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output. This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> ActorTaskRunSyncGetDatasetItemsGetWithHttpInfoAsync(string actorTaskId, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Run task synchronously and get dataset items
        /// </summary>
        /// <remarks>
        /// Runs an Actor task and synchronously returns its dataset items.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  Optionally, you can override the Actor input configuration by passing a JSON object as the POST payload and setting the &#x60;Content-Type: application/json&#x60; HTTP header.  Note that if the object in the POST payload does not define a particular input property, the Actor run uses the default value defined by the task (or the Actor&#39;s input schema if not defined by the task).  You can send all the same options in parameters as the [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  Input fields from Actor task configuration can be overloaded with values passed as the POST payload.  Just make sure to specify the &#x60;Content-Type&#x60; header as &#x60;application/json&#x60; and that the input is an object.  To run the task asynchronously, use the [Run task](#/reference/actor-tasks/run-collection/run-task) API endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the task run. This is useful for pay-per-event tasks, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures. For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;. The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not. If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60;  and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the  legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output.  This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        System.Threading.Tasks.Task<Object> ActorTaskRunSyncGetDatasetItemsPostAsync(string actorTaskId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Run task synchronously and get dataset items
        /// </summary>
        /// <remarks>
        /// Runs an Actor task and synchronously returns its dataset items.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  Optionally, you can override the Actor input configuration by passing a JSON object as the POST payload and setting the &#x60;Content-Type: application/json&#x60; HTTP header.  Note that if the object in the POST payload does not define a particular input property, the Actor run uses the default value defined by the task (or the Actor&#39;s input schema if not defined by the task).  You can send all the same options in parameters as the [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  Input fields from Actor task configuration can be overloaded with values passed as the POST payload.  Just make sure to specify the &#x60;Content-Type&#x60; header as &#x60;application/json&#x60; and that the input is an object.  To run the task asynchronously, use the [Run task](#/reference/actor-tasks/run-collection/run-task) API endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the task run. This is useful for pay-per-event tasks, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures. For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;. The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not. If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60;  and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the  legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output.  This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> ActorTaskRunSyncGetDatasetItemsPostWithHttpInfoAsync(string actorTaskId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Run task synchronously
        /// </summary>
        /// <remarks>
        /// Runs an Actor task and synchronously returns its output.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  Optionally, you can override the Actor input configuration by passing a JSON object as the POST payload and setting the &#x60;Content-Type: application/json&#x60; HTTP header.  Note that if the object in the POST payload does not define a particular input property, the Actor run uses the default value defined by the task (or Actor&#39;s input schema if not defined by the task).  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  Input fields from Actor task configuration can be overloaded with values passed as the POST payload.  Just make sure to specify &#x60;Content-Type&#x60; header to be &#x60;application/json&#x60; and input to be an object.  To run the task asynchronously, use the [Run task](#/reference/actor-tasks/run-collection/run-task) API endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the task run. This parameter is useful for pay-per-event tasks, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification  e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see  [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        System.Threading.Tasks.Task<Object> ActorTaskRunSyncPostAsync(string actorTaskId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? outputRecordKey = default(string?), string? webhooks = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Run task synchronously
        /// </summary>
        /// <remarks>
        /// Runs an Actor task and synchronously returns its output.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  Optionally, you can override the Actor input configuration by passing a JSON object as the POST payload and setting the &#x60;Content-Type: application/json&#x60; HTTP header.  Note that if the object in the POST payload does not define a particular input property, the Actor run uses the default value defined by the task (or Actor&#39;s input schema if not defined by the task).  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  Input fields from Actor task configuration can be overloaded with values passed as the POST payload.  Just make sure to specify &#x60;Content-Type&#x60; header to be &#x60;application/json&#x60; and input to be an object.  To run the task asynchronously, use the [Run task](#/reference/actor-tasks/run-collection/run-task) API endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the task run. This parameter is useful for pay-per-event tasks, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification  e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see  [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> ActorTaskRunSyncPostWithHttpInfoAsync(string actorTaskId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? outputRecordKey = default(string?), string? webhooks = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Get list of task runs
        /// </summary>
        /// <remarks>
        /// Get a list of runs of a specific task. The response is a list of objects, where each object contains essential information about a single task run.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters, and it does not return more than a 1000 array elements.  By default, the records are sorted by the &#x60;startedAt&#x60; field in ascending order; therefore you can use pagination to incrementally fetch all records while new ones are still being created. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. You can also filter runs by status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle)). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="status">Return only runs with the provided status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle))  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ActorTaskRunsGet200Response</returns>
        System.Threading.Tasks.Task<ActorTaskRunsGet200Response> ActorTaskRunsGetAsync(string actorTaskId, double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), string? status = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Get list of task runs
        /// </summary>
        /// <remarks>
        /// Get a list of runs of a specific task. The response is a list of objects, where each object contains essential information about a single task run.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters, and it does not return more than a 1000 array elements.  By default, the records are sorted by the &#x60;startedAt&#x60; field in ascending order; therefore you can use pagination to incrementally fetch all records while new ones are still being created. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. You can also filter runs by status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle)). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="status">Return only runs with the provided status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle))  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ActorTaskRunsGet200Response)</returns>
        System.Threading.Tasks.Task<ApiResponse<ActorTaskRunsGet200Response>> ActorTaskRunsGetWithHttpInfoAsync(string actorTaskId, double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), string? status = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Run task
        /// </summary>
        /// <remarks>
        /// Runs an Actor task and immediately returns without waiting for the run to finish.  Optionally, you can override the Actor input configuration by passing a JSON object as the POST payload and setting the &#x60;Content-Type: application/json&#x60; HTTP header.  Note that if the object in the POST payload does not define a particular input property, the Actor run uses the default value defined by the task (or Actor&#39;s input schema if not defined by the task).  The response is the Actor Run object as returned by the [Get run](#/reference/actor-runs/run-object-and-its-storages/get-run) endpoint.  If you want to wait for the run to finish and receive the actual output of the Actor run as the response, use one of the [Run task synchronously](#/reference/actor-tasks/run-task-synchronously) API endpoints instead.  To fetch the Actor run results that are typically stored in the default dataset, you&#39;ll need to pass the ID received in the &#x60;defaultDatasetId&#x60; field received in the response JSON to the [Get items](#/reference/datasets/item-collection/get-items) API endpoint. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default, it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks.  **Note**: if you already have a webhook set up for the Actor or task, you do not have to add it again here.  For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ActorTaskRunsPost201Response</returns>
        System.Threading.Tasks.Task<ActorTaskRunsPost201Response> ActorTaskRunsPostAsync(string actorTaskId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), double? waitForFinish = default(double?), string? webhooks = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Run task
        /// </summary>
        /// <remarks>
        /// Runs an Actor task and immediately returns without waiting for the run to finish.  Optionally, you can override the Actor input configuration by passing a JSON object as the POST payload and setting the &#x60;Content-Type: application/json&#x60; HTTP header.  Note that if the object in the POST payload does not define a particular input property, the Actor run uses the default value defined by the task (or Actor&#39;s input schema if not defined by the task).  The response is the Actor Run object as returned by the [Get run](#/reference/actor-runs/run-object-and-its-storages/get-run) endpoint.  If you want to wait for the run to finish and receive the actual output of the Actor run as the response, use one of the [Run task synchronously](#/reference/actor-tasks/run-task-synchronously) API endpoints instead.  To fetch the Actor run results that are typically stored in the default dataset, you&#39;ll need to pass the ID received in the &#x60;defaultDatasetId&#x60; field received in the response JSON to the [Get items](#/reference/datasets/item-collection/get-items) API endpoint. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default, it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks.  **Note**: if you already have a webhook set up for the Actor or task, you do not have to add it again here.  For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ActorTaskRunsPost201Response)</returns>
        System.Threading.Tasks.Task<ApiResponse<ActorTaskRunsPost201Response>> ActorTaskRunsPostWithHttpInfoAsync(string actorTaskId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), double? waitForFinish = default(double?), string? webhooks = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Get list of webhooks
        /// </summary>
        /// <remarks>
        /// Gets the list of webhooks of a specific Actor task. The response is a JSON with the list of objects, where each object contains basic information about a single webhook.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 records.  By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order, to sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ActorTaskWebhooksGet200Response</returns>
        System.Threading.Tasks.Task<ActorTaskWebhooksGet200Response> ActorTaskWebhooksGetAsync(string actorTaskId, double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Get list of webhooks
        /// </summary>
        /// <remarks>
        /// Gets the list of webhooks of a specific Actor task. The response is a JSON with the list of objects, where each object contains basic information about a single webhook.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 records.  By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order, to sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ActorTaskWebhooksGet200Response)</returns>
        System.Threading.Tasks.Task<ApiResponse<ActorTaskWebhooksGet200Response>> ActorTaskWebhooksGetWithHttpInfoAsync(string actorTaskId, double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Get list of tasks
        /// </summary>
        /// <remarks>
        /// Gets the complete list of tasks that a user has created or used.  The response is a list of objects in which each object contains essential information about a single task.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters, and it does not return more than a 1000 records.  By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order; therefore you can use pagination to incrementally fetch all tasks while new ones are still being created. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of records that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of records to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ActorTasksGet200Response</returns>
        System.Threading.Tasks.Task<ActorTasksGet200Response> ActorTasksGetAsync(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Get list of tasks
        /// </summary>
        /// <remarks>
        /// Gets the complete list of tasks that a user has created or used.  The response is a list of objects in which each object contains essential information about a single task.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters, and it does not return more than a 1000 records.  By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order; therefore you can use pagination to incrementally fetch all tasks while new ones are still being created. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of records that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of records to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ActorTasksGet200Response)</returns>
        System.Threading.Tasks.Task<ApiResponse<ActorTasksGet200Response>> ActorTasksGetWithHttpInfoAsync(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Create task
        /// </summary>
        /// <remarks>
        /// Create a new task with settings specified by the object passed as JSON in the POST payload.  The response is the full task object as returned by the [Get task](#/reference/tasks/task-object/get-task) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTasksPostRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ActorTasksPost201Response</returns>
        System.Threading.Tasks.Task<ActorTasksPost201Response> ActorTasksPostAsync(ActorTasksPostRequest actorTasksPostRequest, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Create task
        /// </summary>
        /// <remarks>
        /// Create a new task with settings specified by the object passed as JSON in the POST payload.  The response is the full task object as returned by the [Get task](#/reference/tasks/task-object/get-task) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTasksPostRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ActorTasksPost201Response)</returns>
        System.Threading.Tasks.Task<ApiResponse<ActorTasksPost201Response>> ActorTasksPostWithHttpInfoAsync(ActorTasksPostRequest actorTasksPostRequest, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        #endregion Asynchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IActorTasksApi : IActorTasksApiSync, IActorTasksApiAsync
    {

    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public partial class ActorTasksApi : IDisposable, IActorTasksApi
    {
        private Apify.SDK.Client.ExceptionFactory _exceptionFactory = (name, response) => null;

        /// <summary>
        /// Initializes a new instance of the <see cref="ActorTasksApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <returns></returns>
        public ActorTasksApi() : this((string)null)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ActorTasksApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        public ActorTasksApi(string basePath)
        {
            this.Configuration = Apify.SDK.Client.Configuration.MergeConfigurations(
                Apify.SDK.Client.GlobalConfiguration.Instance,
                new Apify.SDK.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Apify.SDK.Client.ApiClient(this.Configuration.BasePath);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Apify.SDK.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ActorTasksApi"/> class using Configuration object.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        public ActorTasksApi(Apify.SDK.Client.Configuration configuration)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Configuration = Apify.SDK.Client.Configuration.MergeConfigurations(
                Apify.SDK.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Apify.SDK.Client.ApiClient(this.Configuration.BasePath);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Apify.SDK.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ActorTasksApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public ActorTasksApi(HttpClient client, HttpClientHandler handler = null) : this(client, (string)null, handler)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ActorTasksApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public ActorTasksApi(HttpClient client, string basePath, HttpClientHandler handler = null)
        {
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Apify.SDK.Client.Configuration.MergeConfigurations(
                Apify.SDK.Client.GlobalConfiguration.Instance,
                new Apify.SDK.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Apify.SDK.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Apify.SDK.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ActorTasksApi"/> class using Configuration object.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public ActorTasksApi(HttpClient client, Apify.SDK.Client.Configuration configuration, HttpClientHandler handler = null)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Apify.SDK.Client.Configuration.MergeConfigurations(
                Apify.SDK.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Apify.SDK.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Apify.SDK.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ActorTasksApi"/> class
        /// using a Configuration object and client instance.
        /// </summary>
        /// <param name="client">The client interface for synchronous API access.</param>
        /// <param name="asyncClient">The client interface for asynchronous API access.</param>
        /// <param name="configuration">The configuration object.</param>
        /// <exception cref="ArgumentNullException"></exception>
        public ActorTasksApi(Apify.SDK.Client.ISynchronousClient client, Apify.SDK.Client.IAsynchronousClient asyncClient, Apify.SDK.Client.IReadableConfiguration configuration)
        {
            if (client == null) throw new ArgumentNullException("client");
            if (asyncClient == null) throw new ArgumentNullException("asyncClient");
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Client = client;
            this.AsynchronousClient = asyncClient;
            this.Configuration = configuration;
            this.ExceptionFactory = Apify.SDK.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Disposes resources if they were created by us
        /// </summary>
        public void Dispose()
        {
            this.ApiClient?.Dispose();
        }

        /// <summary>
        /// Holds the ApiClient if created
        /// </summary>
        public Apify.SDK.Client.ApiClient ApiClient { get; set; } = null;

        /// <summary>
        /// The client for accessing this underlying API asynchronously.
        /// </summary>
        public Apify.SDK.Client.IAsynchronousClient AsynchronousClient { get; set; }

        /// <summary>
        /// The client for accessing this underlying API synchronously.
        /// </summary>
        public Apify.SDK.Client.ISynchronousClient Client { get; set; }

        /// <summary>
        /// Gets the base path of the API client.
        /// </summary>
        /// <value>The base path</value>
        public string GetBasePath()
        {
            return this.Configuration.BasePath;
        }

        /// <summary>
        /// Gets or sets the configuration object
        /// </summary>
        /// <value>An instance of the Configuration</value>
        public Apify.SDK.Client.IReadableConfiguration Configuration { get; set; }

        /// <summary>
        /// Provides a factory method hook for the creation of exceptions.
        /// </summary>
        public Apify.SDK.Client.ExceptionFactory ExceptionFactory
        {
            get
            {
                if (_exceptionFactory != null && _exceptionFactory.GetInvocationList().Length > 1)
                {
                    throw new InvalidOperationException("Multicast delegate for ExceptionFactory is unsupported.");
                }
                return _exceptionFactory;
            }
            set { _exceptionFactory = value; }
        }

        /// <summary>
        /// Delete task Delete the task specified through the &#x60;actorTaskId&#x60; parameter.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <returns>Object</returns>
        public Object ActorTaskDelete(string actorTaskId)
        {
            Apify.SDK.Client.ApiResponse<Object> localVarResponse = ActorTaskDeleteWithHttpInfo(actorTaskId);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Delete task Delete the task specified through the &#x60;actorTaskId&#x60; parameter.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <returns>ApiResponse of Object</returns>
        public Apify.SDK.Client.ApiResponse<Object> ActorTaskDeleteWithHttpInfo(string actorTaskId)
        {
            // verify the required parameter 'actorTaskId' is set
            if (actorTaskId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorTaskId' when calling ActorTasksApi->ActorTaskDelete");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorTaskId", Apify.SDK.Client.ClientUtils.ParameterToString(actorTaskId)); // path parameter

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Delete<Object>("/v2/actor-tasks/{actorTaskId}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorTaskDelete", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Delete task Delete the task specified through the &#x60;actorTaskId&#x60; parameter.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        public async System.Threading.Tasks.Task<Object> ActorTaskDeleteAsync(string actorTaskId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<Object> localVarResponse = await ActorTaskDeleteWithHttpInfoAsync(actorTaskId, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Delete task Delete the task specified through the &#x60;actorTaskId&#x60; parameter.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<Object>> ActorTaskDeleteWithHttpInfoAsync(string actorTaskId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'actorTaskId' is set
            if (actorTaskId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorTaskId' when calling ActorTasksApi->ActorTaskDelete");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorTaskId", Apify.SDK.Client.ClientUtils.ParameterToString(actorTaskId)); // path parameter

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.DeleteAsync<Object>("/v2/actor-tasks/{actorTaskId}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorTaskDelete", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get task Get an object that contains all the details about a task.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <returns>ActorTasksPost201Response</returns>
        public ActorTasksPost201Response ActorTaskGet(string actorTaskId)
        {
            Apify.SDK.Client.ApiResponse<ActorTasksPost201Response> localVarResponse = ActorTaskGetWithHttpInfo(actorTaskId);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get task Get an object that contains all the details about a task.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <returns>ApiResponse of ActorTasksPost201Response</returns>
        public Apify.SDK.Client.ApiResponse<ActorTasksPost201Response> ActorTaskGetWithHttpInfo(string actorTaskId)
        {
            // verify the required parameter 'actorTaskId' is set
            if (actorTaskId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorTaskId' when calling ActorTasksApi->ActorTaskGet");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorTaskId", Apify.SDK.Client.ClientUtils.ParameterToString(actorTaskId)); // path parameter

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<ActorTasksPost201Response>("/v2/actor-tasks/{actorTaskId}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorTaskGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get task Get an object that contains all the details about a task.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ActorTasksPost201Response</returns>
        public async System.Threading.Tasks.Task<ActorTasksPost201Response> ActorTaskGetAsync(string actorTaskId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<ActorTasksPost201Response> localVarResponse = await ActorTaskGetWithHttpInfoAsync(actorTaskId, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get task Get an object that contains all the details about a task.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ActorTasksPost201Response)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<ActorTasksPost201Response>> ActorTaskGetWithHttpInfoAsync(string actorTaskId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'actorTaskId' is set
            if (actorTaskId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorTaskId' when calling ActorTasksApi->ActorTaskGet");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorTaskId", Apify.SDK.Client.ClientUtils.ParameterToString(actorTaskId)); // path parameter

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<ActorTasksPost201Response>("/v2/actor-tasks/{actorTaskId}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorTaskGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get task input Returns the input of a given task.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <returns>Object</returns>
        public Object ActorTaskInputGet(string actorTaskId)
        {
            Apify.SDK.Client.ApiResponse<Object> localVarResponse = ActorTaskInputGetWithHttpInfo(actorTaskId);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get task input Returns the input of a given task.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <returns>ApiResponse of Object</returns>
        public Apify.SDK.Client.ApiResponse<Object> ActorTaskInputGetWithHttpInfo(string actorTaskId)
        {
            // verify the required parameter 'actorTaskId' is set
            if (actorTaskId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorTaskId' when calling ActorTasksApi->ActorTaskInputGet");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorTaskId", Apify.SDK.Client.ClientUtils.ParameterToString(actorTaskId)); // path parameter

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<Object>("/v2/actor-tasks/{actorTaskId}/input", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorTaskInputGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get task input Returns the input of a given task.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        public async System.Threading.Tasks.Task<Object> ActorTaskInputGetAsync(string actorTaskId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<Object> localVarResponse = await ActorTaskInputGetWithHttpInfoAsync(actorTaskId, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get task input Returns the input of a given task.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<Object>> ActorTaskInputGetWithHttpInfoAsync(string actorTaskId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'actorTaskId' is set
            if (actorTaskId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorTaskId' when calling ActorTasksApi->ActorTaskInputGet");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorTaskId", Apify.SDK.Client.ClientUtils.ParameterToString(actorTaskId)); // path parameter

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<Object>("/v2/actor-tasks/{actorTaskId}/input", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorTaskInputGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Update task input Updates the input of a task using values specified by an object passed as JSON in the PUT payload.  If the object does not define a specific property, its value is not updated.  The response is the full task input as returned by the [Get task input](#/reference/tasks/task-input-object/get-task-input) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <returns>Object</returns>
        public Object ActorTaskInputPut(string actorTaskId, Object body)
        {
            Apify.SDK.Client.ApiResponse<Object> localVarResponse = ActorTaskInputPutWithHttpInfo(actorTaskId, body);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Update task input Updates the input of a task using values specified by an object passed as JSON in the PUT payload.  If the object does not define a specific property, its value is not updated.  The response is the full task input as returned by the [Get task input](#/reference/tasks/task-input-object/get-task-input) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <returns>ApiResponse of Object</returns>
        public Apify.SDK.Client.ApiResponse<Object> ActorTaskInputPutWithHttpInfo(string actorTaskId, Object body)
        {
            // verify the required parameter 'actorTaskId' is set
            if (actorTaskId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorTaskId' when calling ActorTasksApi->ActorTaskInputPut");

            // verify the required parameter 'body' is set
            if (body == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'body' when calling ActorTasksApi->ActorTaskInputPut");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorTaskId", Apify.SDK.Client.ClientUtils.ParameterToString(actorTaskId)); // path parameter
            localVarRequestOptions.Data = body;

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Put<Object>("/v2/actor-tasks/{actorTaskId}/input", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorTaskInputPut", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Update task input Updates the input of a task using values specified by an object passed as JSON in the PUT payload.  If the object does not define a specific property, its value is not updated.  The response is the full task input as returned by the [Get task input](#/reference/tasks/task-input-object/get-task-input) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        public async System.Threading.Tasks.Task<Object> ActorTaskInputPutAsync(string actorTaskId, Object body, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<Object> localVarResponse = await ActorTaskInputPutWithHttpInfoAsync(actorTaskId, body, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Update task input Updates the input of a task using values specified by an object passed as JSON in the PUT payload.  If the object does not define a specific property, its value is not updated.  The response is the full task input as returned by the [Get task input](#/reference/tasks/task-input-object/get-task-input) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<Object>> ActorTaskInputPutWithHttpInfoAsync(string actorTaskId, Object body, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'actorTaskId' is set
            if (actorTaskId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorTaskId' when calling ActorTasksApi->ActorTaskInputPut");

            // verify the required parameter 'body' is set
            if (body == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'body' when calling ActorTasksApi->ActorTaskInputPut");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorTaskId", Apify.SDK.Client.ClientUtils.ParameterToString(actorTaskId)); // path parameter
            localVarRequestOptions.Data = body;

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PutAsync<Object>("/v2/actor-tasks/{actorTaskId}/input", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorTaskInputPut", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Update task Update settings of a task using values specified by an object passed as JSON in the POST payload.  If the object does not define a specific property, its value is not updated.  The response is the full task object as returned by the [Get task](#/reference/tasks/task-object/get-task) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="updateTaskRequest"></param>
        /// <returns>ActorTasksPost201Response</returns>
        public ActorTasksPost201Response ActorTaskPut(string actorTaskId, UpdateTaskRequest updateTaskRequest)
        {
            Apify.SDK.Client.ApiResponse<ActorTasksPost201Response> localVarResponse = ActorTaskPutWithHttpInfo(actorTaskId, updateTaskRequest);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Update task Update settings of a task using values specified by an object passed as JSON in the POST payload.  If the object does not define a specific property, its value is not updated.  The response is the full task object as returned by the [Get task](#/reference/tasks/task-object/get-task) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="updateTaskRequest"></param>
        /// <returns>ApiResponse of ActorTasksPost201Response</returns>
        public Apify.SDK.Client.ApiResponse<ActorTasksPost201Response> ActorTaskPutWithHttpInfo(string actorTaskId, UpdateTaskRequest updateTaskRequest)
        {
            // verify the required parameter 'actorTaskId' is set
            if (actorTaskId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorTaskId' when calling ActorTasksApi->ActorTaskPut");

            // verify the required parameter 'updateTaskRequest' is set
            if (updateTaskRequest == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'updateTaskRequest' when calling ActorTasksApi->ActorTaskPut");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorTaskId", Apify.SDK.Client.ClientUtils.ParameterToString(actorTaskId)); // path parameter
            localVarRequestOptions.Data = updateTaskRequest;

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Put<ActorTasksPost201Response>("/v2/actor-tasks/{actorTaskId}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorTaskPut", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Update task Update settings of a task using values specified by an object passed as JSON in the POST payload.  If the object does not define a specific property, its value is not updated.  The response is the full task object as returned by the [Get task](#/reference/tasks/task-object/get-task) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="updateTaskRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ActorTasksPost201Response</returns>
        public async System.Threading.Tasks.Task<ActorTasksPost201Response> ActorTaskPutAsync(string actorTaskId, UpdateTaskRequest updateTaskRequest, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<ActorTasksPost201Response> localVarResponse = await ActorTaskPutWithHttpInfoAsync(actorTaskId, updateTaskRequest, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Update task Update settings of a task using values specified by an object passed as JSON in the POST payload.  If the object does not define a specific property, its value is not updated.  The response is the full task object as returned by the [Get task](#/reference/tasks/task-object/get-task) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="updateTaskRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ActorTasksPost201Response)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<ActorTasksPost201Response>> ActorTaskPutWithHttpInfoAsync(string actorTaskId, UpdateTaskRequest updateTaskRequest, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'actorTaskId' is set
            if (actorTaskId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorTaskId' when calling ActorTasksApi->ActorTaskPut");

            // verify the required parameter 'updateTaskRequest' is set
            if (updateTaskRequest == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'updateTaskRequest' when calling ActorTasksApi->ActorTaskPut");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorTaskId", Apify.SDK.Client.ClientUtils.ParameterToString(actorTaskId)); // path parameter
            localVarRequestOptions.Data = updateTaskRequest;

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PutAsync<ActorTasksPost201Response>("/v2/actor-tasks/{actorTaskId}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorTaskPut", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Run task synchronously Run a specific task and return its output.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  To run the Task asynchronously, use the [Run task asynchronously](#/reference/actor-tasks/run-collection/run-task) endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <returns>Object</returns>
        public Object ActorTaskRunSyncGet(string actorTaskId, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), string? build = default(string?), string? outputRecordKey = default(string?), string? webhooks = default(string?))
        {
            Apify.SDK.Client.ApiResponse<Object> localVarResponse = ActorTaskRunSyncGetWithHttpInfo(actorTaskId, timeout, memory, maxItems, build, outputRecordKey, webhooks);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Run task synchronously Run a specific task and return its output.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  To run the Task asynchronously, use the [Run task asynchronously](#/reference/actor-tasks/run-collection/run-task) endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <returns>ApiResponse of Object</returns>
        public Apify.SDK.Client.ApiResponse<Object> ActorTaskRunSyncGetWithHttpInfo(string actorTaskId, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), string? build = default(string?), string? outputRecordKey = default(string?), string? webhooks = default(string?))
        {
            // verify the required parameter 'actorTaskId' is set
            if (actorTaskId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorTaskId' when calling ActorTasksApi->ActorTaskRunSyncGet");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorTaskId", Apify.SDK.Client.ClientUtils.ParameterToString(actorTaskId)); // path parameter
            if (timeout != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "timeout", timeout));
            }
            if (memory != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "memory", memory));
            }
            if (maxItems != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxItems", maxItems));
            }
            if (build != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "build", build));
            }
            if (outputRecordKey != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "outputRecordKey", outputRecordKey));
            }
            if (webhooks != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "webhooks", webhooks));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<Object>("/v2/actor-tasks/{actorTaskId}/run-sync", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorTaskRunSyncGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Run task synchronously Run a specific task and return its output.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  To run the Task asynchronously, use the [Run task asynchronously](#/reference/actor-tasks/run-collection/run-task) endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        public async System.Threading.Tasks.Task<Object> ActorTaskRunSyncGetAsync(string actorTaskId, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), string? build = default(string?), string? outputRecordKey = default(string?), string? webhooks = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<Object> localVarResponse = await ActorTaskRunSyncGetWithHttpInfoAsync(actorTaskId, timeout, memory, maxItems, build, outputRecordKey, webhooks, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Run task synchronously Run a specific task and return its output.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  To run the Task asynchronously, use the [Run task asynchronously](#/reference/actor-tasks/run-collection/run-task) endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<Object>> ActorTaskRunSyncGetWithHttpInfoAsync(string actorTaskId, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), string? build = default(string?), string? outputRecordKey = default(string?), string? webhooks = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'actorTaskId' is set
            if (actorTaskId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorTaskId' when calling ActorTasksApi->ActorTaskRunSyncGet");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorTaskId", Apify.SDK.Client.ClientUtils.ParameterToString(actorTaskId)); // path parameter
            if (timeout != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "timeout", timeout));
            }
            if (memory != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "memory", memory));
            }
            if (maxItems != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxItems", maxItems));
            }
            if (build != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "build", build));
            }
            if (outputRecordKey != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "outputRecordKey", outputRecordKey));
            }
            if (webhooks != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "webhooks", webhooks));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<Object>("/v2/actor-tasks/{actorTaskId}/run-sync", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorTaskRunSyncGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Run task synchronously and get dataset items Run a specific task and return its dataset items.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  You can send all the same options in parameters as the [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  To run the Task asynchronously, use the [Run task asynchronously](#/reference/actor-tasks/run-collection/run-task) endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields  (i.e. fields starting with the # character).  The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters.  Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items,  only these fields will remain in the resulting record objects.  Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter.  You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object.  If the field is an array then every element of  the array will become a separate record and merged with parent object.  If the unwound field is an object then it is merged with the parent object  If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is.  Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures.  For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;.  The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with  the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not.  If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output. This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <returns>Object</returns>
        public Object ActorTaskRunSyncGetDatasetItemsGet(string actorTaskId, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?))
        {
            Apify.SDK.Client.ApiResponse<Object> localVarResponse = ActorTaskRunSyncGetDatasetItemsGetWithHttpInfo(actorTaskId, timeout, memory, maxItems, build, webhooks, format, clean, offset, limit, fields, omit, unwind, flatten, desc, attachment, delimiter, bom, xmlRoot, xmlRow, skipHeaderRow, skipHidden, skipEmpty, simplified, skipFailedPages);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Run task synchronously and get dataset items Run a specific task and return its dataset items.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  You can send all the same options in parameters as the [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  To run the Task asynchronously, use the [Run task asynchronously](#/reference/actor-tasks/run-collection/run-task) endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields  (i.e. fields starting with the # character).  The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters.  Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items,  only these fields will remain in the resulting record objects.  Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter.  You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object.  If the field is an array then every element of  the array will become a separate record and merged with parent object.  If the unwound field is an object then it is merged with the parent object  If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is.  Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures.  For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;.  The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with  the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not.  If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output. This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <returns>ApiResponse of Object</returns>
        public Apify.SDK.Client.ApiResponse<Object> ActorTaskRunSyncGetDatasetItemsGetWithHttpInfo(string actorTaskId, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?))
        {
            // verify the required parameter 'actorTaskId' is set
            if (actorTaskId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorTaskId' when calling ActorTasksApi->ActorTaskRunSyncGetDatasetItemsGet");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorTaskId", Apify.SDK.Client.ClientUtils.ParameterToString(actorTaskId)); // path parameter
            if (timeout != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "timeout", timeout));
            }
            if (memory != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "memory", memory));
            }
            if (maxItems != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxItems", maxItems));
            }
            if (build != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "build", build));
            }
            if (webhooks != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "webhooks", webhooks));
            }
            if (format != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "format", format));
            }
            if (clean != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "clean", clean));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (fields != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "fields", fields));
            }
            if (omit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "omit", omit));
            }
            if (unwind != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "unwind", unwind));
            }
            if (flatten != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "flatten", flatten));
            }
            if (desc != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "desc", desc));
            }
            if (attachment != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "attachment", attachment));
            }
            if (delimiter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "delimiter", delimiter));
            }
            if (bom != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "bom", bom));
            }
            if (xmlRoot != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "xmlRoot", xmlRoot));
            }
            if (xmlRow != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "xmlRow", xmlRow));
            }
            if (skipHeaderRow != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipHeaderRow", skipHeaderRow));
            }
            if (skipHidden != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipHidden", skipHidden));
            }
            if (skipEmpty != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipEmpty", skipEmpty));
            }
            if (simplified != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "simplified", simplified));
            }
            if (skipFailedPages != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipFailedPages", skipFailedPages));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<Object>("/v2/actor-tasks/{actorTaskId}/run-sync-get-dataset-items", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorTaskRunSyncGetDatasetItemsGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Run task synchronously and get dataset items Run a specific task and return its dataset items.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  You can send all the same options in parameters as the [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  To run the Task asynchronously, use the [Run task asynchronously](#/reference/actor-tasks/run-collection/run-task) endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields  (i.e. fields starting with the # character).  The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters.  Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items,  only these fields will remain in the resulting record objects.  Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter.  You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object.  If the field is an array then every element of  the array will become a separate record and merged with parent object.  If the unwound field is an object then it is merged with the parent object  If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is.  Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures.  For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;.  The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with  the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not.  If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output. This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        public async System.Threading.Tasks.Task<Object> ActorTaskRunSyncGetDatasetItemsGetAsync(string actorTaskId, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<Object> localVarResponse = await ActorTaskRunSyncGetDatasetItemsGetWithHttpInfoAsync(actorTaskId, timeout, memory, maxItems, build, webhooks, format, clean, offset, limit, fields, omit, unwind, flatten, desc, attachment, delimiter, bom, xmlRoot, xmlRow, skipHeaderRow, skipHidden, skipEmpty, simplified, skipFailedPages, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Run task synchronously and get dataset items Run a specific task and return its dataset items.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  You can send all the same options in parameters as the [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  To run the Task asynchronously, use the [Run task asynchronously](#/reference/actor-tasks/run-collection/run-task) endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields  (i.e. fields starting with the # character).  The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters.  Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items,  only these fields will remain in the resulting record objects.  Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter.  You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object.  If the field is an array then every element of  the array will become a separate record and merged with parent object.  If the unwound field is an object then it is merged with the parent object  If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is.  Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures.  For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;.  The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with  the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not.  If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output. This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<Object>> ActorTaskRunSyncGetDatasetItemsGetWithHttpInfoAsync(string actorTaskId, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'actorTaskId' is set
            if (actorTaskId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorTaskId' when calling ActorTasksApi->ActorTaskRunSyncGetDatasetItemsGet");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorTaskId", Apify.SDK.Client.ClientUtils.ParameterToString(actorTaskId)); // path parameter
            if (timeout != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "timeout", timeout));
            }
            if (memory != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "memory", memory));
            }
            if (maxItems != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxItems", maxItems));
            }
            if (build != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "build", build));
            }
            if (webhooks != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "webhooks", webhooks));
            }
            if (format != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "format", format));
            }
            if (clean != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "clean", clean));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (fields != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "fields", fields));
            }
            if (omit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "omit", omit));
            }
            if (unwind != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "unwind", unwind));
            }
            if (flatten != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "flatten", flatten));
            }
            if (desc != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "desc", desc));
            }
            if (attachment != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "attachment", attachment));
            }
            if (delimiter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "delimiter", delimiter));
            }
            if (bom != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "bom", bom));
            }
            if (xmlRoot != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "xmlRoot", xmlRoot));
            }
            if (xmlRow != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "xmlRow", xmlRow));
            }
            if (skipHeaderRow != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipHeaderRow", skipHeaderRow));
            }
            if (skipHidden != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipHidden", skipHidden));
            }
            if (skipEmpty != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipEmpty", skipEmpty));
            }
            if (simplified != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "simplified", simplified));
            }
            if (skipFailedPages != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipFailedPages", skipFailedPages));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<Object>("/v2/actor-tasks/{actorTaskId}/run-sync-get-dataset-items", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorTaskRunSyncGetDatasetItemsGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Run task synchronously and get dataset items Runs an Actor task and synchronously returns its dataset items.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  Optionally, you can override the Actor input configuration by passing a JSON object as the POST payload and setting the &#x60;Content-Type: application/json&#x60; HTTP header.  Note that if the object in the POST payload does not define a particular input property, the Actor run uses the default value defined by the task (or the Actor&#39;s input schema if not defined by the task).  You can send all the same options in parameters as the [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  Input fields from Actor task configuration can be overloaded with values passed as the POST payload.  Just make sure to specify the &#x60;Content-Type&#x60; header as &#x60;application/json&#x60; and that the input is an object.  To run the task asynchronously, use the [Run task](#/reference/actor-tasks/run-collection/run-task) API endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the task run. This is useful for pay-per-event tasks, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures. For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;. The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not. If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60;  and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the  legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output.  This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <returns>Object</returns>
        public Object ActorTaskRunSyncGetDatasetItemsPost(string actorTaskId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?))
        {
            Apify.SDK.Client.ApiResponse<Object> localVarResponse = ActorTaskRunSyncGetDatasetItemsPostWithHttpInfo(actorTaskId, body, timeout, memory, maxItems, maxTotalChargeUsd, build, webhooks, format, clean, offset, limit, fields, omit, unwind, flatten, desc, attachment, delimiter, bom, xmlRoot, xmlRow, skipHeaderRow, skipHidden, skipEmpty, simplified, skipFailedPages);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Run task synchronously and get dataset items Runs an Actor task and synchronously returns its dataset items.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  Optionally, you can override the Actor input configuration by passing a JSON object as the POST payload and setting the &#x60;Content-Type: application/json&#x60; HTTP header.  Note that if the object in the POST payload does not define a particular input property, the Actor run uses the default value defined by the task (or the Actor&#39;s input schema if not defined by the task).  You can send all the same options in parameters as the [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  Input fields from Actor task configuration can be overloaded with values passed as the POST payload.  Just make sure to specify the &#x60;Content-Type&#x60; header as &#x60;application/json&#x60; and that the input is an object.  To run the task asynchronously, use the [Run task](#/reference/actor-tasks/run-collection/run-task) API endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the task run. This is useful for pay-per-event tasks, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures. For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;. The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not. If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60;  and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the  legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output.  This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <returns>ApiResponse of Object</returns>
        public Apify.SDK.Client.ApiResponse<Object> ActorTaskRunSyncGetDatasetItemsPostWithHttpInfo(string actorTaskId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?))
        {
            // verify the required parameter 'actorTaskId' is set
            if (actorTaskId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorTaskId' when calling ActorTasksApi->ActorTaskRunSyncGetDatasetItemsPost");

            // verify the required parameter 'body' is set
            if (body == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'body' when calling ActorTasksApi->ActorTaskRunSyncGetDatasetItemsPost");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorTaskId", Apify.SDK.Client.ClientUtils.ParameterToString(actorTaskId)); // path parameter
            if (timeout != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "timeout", timeout));
            }
            if (memory != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "memory", memory));
            }
            if (maxItems != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxItems", maxItems));
            }
            if (maxTotalChargeUsd != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxTotalChargeUsd", maxTotalChargeUsd));
            }
            if (build != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "build", build));
            }
            if (webhooks != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "webhooks", webhooks));
            }
            if (format != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "format", format));
            }
            if (clean != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "clean", clean));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (fields != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "fields", fields));
            }
            if (omit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "omit", omit));
            }
            if (unwind != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "unwind", unwind));
            }
            if (flatten != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "flatten", flatten));
            }
            if (desc != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "desc", desc));
            }
            if (attachment != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "attachment", attachment));
            }
            if (delimiter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "delimiter", delimiter));
            }
            if (bom != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "bom", bom));
            }
            if (xmlRoot != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "xmlRoot", xmlRoot));
            }
            if (xmlRow != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "xmlRow", xmlRow));
            }
            if (skipHeaderRow != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipHeaderRow", skipHeaderRow));
            }
            if (skipHidden != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipHidden", skipHidden));
            }
            if (skipEmpty != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipEmpty", skipEmpty));
            }
            if (simplified != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "simplified", simplified));
            }
            if (skipFailedPages != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipFailedPages", skipFailedPages));
            }
            localVarRequestOptions.Data = body;

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<Object>("/v2/actor-tasks/{actorTaskId}/run-sync-get-dataset-items", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorTaskRunSyncGetDatasetItemsPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Run task synchronously and get dataset items Runs an Actor task and synchronously returns its dataset items.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  Optionally, you can override the Actor input configuration by passing a JSON object as the POST payload and setting the &#x60;Content-Type: application/json&#x60; HTTP header.  Note that if the object in the POST payload does not define a particular input property, the Actor run uses the default value defined by the task (or the Actor&#39;s input schema if not defined by the task).  You can send all the same options in parameters as the [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  Input fields from Actor task configuration can be overloaded with values passed as the POST payload.  Just make sure to specify the &#x60;Content-Type&#x60; header as &#x60;application/json&#x60; and that the input is an object.  To run the task asynchronously, use the [Run task](#/reference/actor-tasks/run-collection/run-task) API endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the task run. This is useful for pay-per-event tasks, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures. For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;. The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not. If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60;  and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the  legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output.  This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        public async System.Threading.Tasks.Task<Object> ActorTaskRunSyncGetDatasetItemsPostAsync(string actorTaskId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<Object> localVarResponse = await ActorTaskRunSyncGetDatasetItemsPostWithHttpInfoAsync(actorTaskId, body, timeout, memory, maxItems, maxTotalChargeUsd, build, webhooks, format, clean, offset, limit, fields, omit, unwind, flatten, desc, attachment, delimiter, bom, xmlRoot, xmlRow, skipHeaderRow, skipHidden, skipEmpty, simplified, skipFailedPages, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Run task synchronously and get dataset items Runs an Actor task and synchronously returns its dataset items.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  Optionally, you can override the Actor input configuration by passing a JSON object as the POST payload and setting the &#x60;Content-Type: application/json&#x60; HTTP header.  Note that if the object in the POST payload does not define a particular input property, the Actor run uses the default value defined by the task (or the Actor&#39;s input schema if not defined by the task).  You can send all the same options in parameters as the [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  Input fields from Actor task configuration can be overloaded with values passed as the POST payload.  Just make sure to specify the &#x60;Content-Type&#x60; header as &#x60;application/json&#x60; and that the input is an object.  To run the task asynchronously, use the [Run task](#/reference/actor-tasks/run-collection/run-task) API endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the task run. This is useful for pay-per-event tasks, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures. For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;. The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not. If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60;  and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the  legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output.  This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<Object>> ActorTaskRunSyncGetDatasetItemsPostWithHttpInfoAsync(string actorTaskId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'actorTaskId' is set
            if (actorTaskId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorTaskId' when calling ActorTasksApi->ActorTaskRunSyncGetDatasetItemsPost");

            // verify the required parameter 'body' is set
            if (body == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'body' when calling ActorTasksApi->ActorTaskRunSyncGetDatasetItemsPost");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorTaskId", Apify.SDK.Client.ClientUtils.ParameterToString(actorTaskId)); // path parameter
            if (timeout != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "timeout", timeout));
            }
            if (memory != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "memory", memory));
            }
            if (maxItems != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxItems", maxItems));
            }
            if (maxTotalChargeUsd != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxTotalChargeUsd", maxTotalChargeUsd));
            }
            if (build != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "build", build));
            }
            if (webhooks != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "webhooks", webhooks));
            }
            if (format != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "format", format));
            }
            if (clean != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "clean", clean));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (fields != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "fields", fields));
            }
            if (omit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "omit", omit));
            }
            if (unwind != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "unwind", unwind));
            }
            if (flatten != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "flatten", flatten));
            }
            if (desc != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "desc", desc));
            }
            if (attachment != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "attachment", attachment));
            }
            if (delimiter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "delimiter", delimiter));
            }
            if (bom != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "bom", bom));
            }
            if (xmlRoot != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "xmlRoot", xmlRoot));
            }
            if (xmlRow != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "xmlRow", xmlRow));
            }
            if (skipHeaderRow != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipHeaderRow", skipHeaderRow));
            }
            if (skipHidden != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipHidden", skipHidden));
            }
            if (skipEmpty != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipEmpty", skipEmpty));
            }
            if (simplified != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "simplified", simplified));
            }
            if (skipFailedPages != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipFailedPages", skipFailedPages));
            }
            localVarRequestOptions.Data = body;

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<Object>("/v2/actor-tasks/{actorTaskId}/run-sync-get-dataset-items", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorTaskRunSyncGetDatasetItemsPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Run task synchronously Runs an Actor task and synchronously returns its output.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  Optionally, you can override the Actor input configuration by passing a JSON object as the POST payload and setting the &#x60;Content-Type: application/json&#x60; HTTP header.  Note that if the object in the POST payload does not define a particular input property, the Actor run uses the default value defined by the task (or Actor&#39;s input schema if not defined by the task).  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  Input fields from Actor task configuration can be overloaded with values passed as the POST payload.  Just make sure to specify &#x60;Content-Type&#x60; header to be &#x60;application/json&#x60; and input to be an object.  To run the task asynchronously, use the [Run task](#/reference/actor-tasks/run-collection/run-task) API endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the task run. This parameter is useful for pay-per-event tasks, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification  e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see  [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <returns>Object</returns>
        public Object ActorTaskRunSyncPost(string actorTaskId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? outputRecordKey = default(string?), string? webhooks = default(string?))
        {
            Apify.SDK.Client.ApiResponse<Object> localVarResponse = ActorTaskRunSyncPostWithHttpInfo(actorTaskId, body, timeout, memory, maxItems, maxTotalChargeUsd, build, outputRecordKey, webhooks);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Run task synchronously Runs an Actor task and synchronously returns its output.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  Optionally, you can override the Actor input configuration by passing a JSON object as the POST payload and setting the &#x60;Content-Type: application/json&#x60; HTTP header.  Note that if the object in the POST payload does not define a particular input property, the Actor run uses the default value defined by the task (or Actor&#39;s input schema if not defined by the task).  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  Input fields from Actor task configuration can be overloaded with values passed as the POST payload.  Just make sure to specify &#x60;Content-Type&#x60; header to be &#x60;application/json&#x60; and input to be an object.  To run the task asynchronously, use the [Run task](#/reference/actor-tasks/run-collection/run-task) API endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the task run. This parameter is useful for pay-per-event tasks, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification  e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see  [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <returns>ApiResponse of Object</returns>
        public Apify.SDK.Client.ApiResponse<Object> ActorTaskRunSyncPostWithHttpInfo(string actorTaskId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? outputRecordKey = default(string?), string? webhooks = default(string?))
        {
            // verify the required parameter 'actorTaskId' is set
            if (actorTaskId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorTaskId' when calling ActorTasksApi->ActorTaskRunSyncPost");

            // verify the required parameter 'body' is set
            if (body == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'body' when calling ActorTasksApi->ActorTaskRunSyncPost");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorTaskId", Apify.SDK.Client.ClientUtils.ParameterToString(actorTaskId)); // path parameter
            if (timeout != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "timeout", timeout));
            }
            if (memory != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "memory", memory));
            }
            if (maxItems != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxItems", maxItems));
            }
            if (maxTotalChargeUsd != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxTotalChargeUsd", maxTotalChargeUsd));
            }
            if (build != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "build", build));
            }
            if (outputRecordKey != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "outputRecordKey", outputRecordKey));
            }
            if (webhooks != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "webhooks", webhooks));
            }
            localVarRequestOptions.Data = body;

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<Object>("/v2/actor-tasks/{actorTaskId}/run-sync", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorTaskRunSyncPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Run task synchronously Runs an Actor task and synchronously returns its output.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  Optionally, you can override the Actor input configuration by passing a JSON object as the POST payload and setting the &#x60;Content-Type: application/json&#x60; HTTP header.  Note that if the object in the POST payload does not define a particular input property, the Actor run uses the default value defined by the task (or Actor&#39;s input schema if not defined by the task).  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  Input fields from Actor task configuration can be overloaded with values passed as the POST payload.  Just make sure to specify &#x60;Content-Type&#x60; header to be &#x60;application/json&#x60; and input to be an object.  To run the task asynchronously, use the [Run task](#/reference/actor-tasks/run-collection/run-task) API endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the task run. This parameter is useful for pay-per-event tasks, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification  e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see  [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        public async System.Threading.Tasks.Task<Object> ActorTaskRunSyncPostAsync(string actorTaskId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? outputRecordKey = default(string?), string? webhooks = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<Object> localVarResponse = await ActorTaskRunSyncPostWithHttpInfoAsync(actorTaskId, body, timeout, memory, maxItems, maxTotalChargeUsd, build, outputRecordKey, webhooks, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Run task synchronously Runs an Actor task and synchronously returns its output.  The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the HTTP request fails with a timeout error (this won&#39;t abort the run itself).  Optionally, you can override the Actor input configuration by passing a JSON object as the POST payload and setting the &#x60;Content-Type: application/json&#x60; HTTP header.  Note that if the object in the POST payload does not define a particular input property, the Actor run uses the default value defined by the task (or Actor&#39;s input schema if not defined by the task).  Beware that it might be impossible to maintain an idle HTTP connection for an extended period, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout.  If the connection breaks, you will not receive any information about the run and its status.  Input fields from Actor task configuration can be overloaded with values passed as the POST payload.  Just make sure to specify &#x60;Content-Type&#x60; header to be &#x60;application/json&#x60; and input to be an object.  To run the task asynchronously, use the [Run task](#/reference/actor-tasks/run-collection/run-task) API endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the task run should return. This is useful for pay-per-result tasks, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the task run. This parameter is useful for pay-per-event tasks, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification  e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see  [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<Object>> ActorTaskRunSyncPostWithHttpInfoAsync(string actorTaskId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? outputRecordKey = default(string?), string? webhooks = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'actorTaskId' is set
            if (actorTaskId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorTaskId' when calling ActorTasksApi->ActorTaskRunSyncPost");

            // verify the required parameter 'body' is set
            if (body == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'body' when calling ActorTasksApi->ActorTaskRunSyncPost");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorTaskId", Apify.SDK.Client.ClientUtils.ParameterToString(actorTaskId)); // path parameter
            if (timeout != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "timeout", timeout));
            }
            if (memory != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "memory", memory));
            }
            if (maxItems != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxItems", maxItems));
            }
            if (maxTotalChargeUsd != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxTotalChargeUsd", maxTotalChargeUsd));
            }
            if (build != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "build", build));
            }
            if (outputRecordKey != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "outputRecordKey", outputRecordKey));
            }
            if (webhooks != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "webhooks", webhooks));
            }
            localVarRequestOptions.Data = body;

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<Object>("/v2/actor-tasks/{actorTaskId}/run-sync", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorTaskRunSyncPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get list of task runs Get a list of runs of a specific task. The response is a list of objects, where each object contains essential information about a single task run.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters, and it does not return more than a 1000 array elements.  By default, the records are sorted by the &#x60;startedAt&#x60; field in ascending order; therefore you can use pagination to incrementally fetch all records while new ones are still being created. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. You can also filter runs by status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle)). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="status">Return only runs with the provided status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle))  (optional)</param>
        /// <returns>ActorTaskRunsGet200Response</returns>
        public ActorTaskRunsGet200Response ActorTaskRunsGet(string actorTaskId, double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), string? status = default(string?))
        {
            Apify.SDK.Client.ApiResponse<ActorTaskRunsGet200Response> localVarResponse = ActorTaskRunsGetWithHttpInfo(actorTaskId, offset, limit, desc, status);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get list of task runs Get a list of runs of a specific task. The response is a list of objects, where each object contains essential information about a single task run.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters, and it does not return more than a 1000 array elements.  By default, the records are sorted by the &#x60;startedAt&#x60; field in ascending order; therefore you can use pagination to incrementally fetch all records while new ones are still being created. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. You can also filter runs by status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle)). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="status">Return only runs with the provided status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle))  (optional)</param>
        /// <returns>ApiResponse of ActorTaskRunsGet200Response</returns>
        public Apify.SDK.Client.ApiResponse<ActorTaskRunsGet200Response> ActorTaskRunsGetWithHttpInfo(string actorTaskId, double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), string? status = default(string?))
        {
            // verify the required parameter 'actorTaskId' is set
            if (actorTaskId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorTaskId' when calling ActorTasksApi->ActorTaskRunsGet");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorTaskId", Apify.SDK.Client.ClientUtils.ParameterToString(actorTaskId)); // path parameter
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (desc != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "desc", desc));
            }
            if (status != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "status", status));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<ActorTaskRunsGet200Response>("/v2/actor-tasks/{actorTaskId}/runs", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorTaskRunsGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get list of task runs Get a list of runs of a specific task. The response is a list of objects, where each object contains essential information about a single task run.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters, and it does not return more than a 1000 array elements.  By default, the records are sorted by the &#x60;startedAt&#x60; field in ascending order; therefore you can use pagination to incrementally fetch all records while new ones are still being created. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. You can also filter runs by status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle)). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="status">Return only runs with the provided status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle))  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ActorTaskRunsGet200Response</returns>
        public async System.Threading.Tasks.Task<ActorTaskRunsGet200Response> ActorTaskRunsGetAsync(string actorTaskId, double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), string? status = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<ActorTaskRunsGet200Response> localVarResponse = await ActorTaskRunsGetWithHttpInfoAsync(actorTaskId, offset, limit, desc, status, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get list of task runs Get a list of runs of a specific task. The response is a list of objects, where each object contains essential information about a single task run.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters, and it does not return more than a 1000 array elements.  By default, the records are sorted by the &#x60;startedAt&#x60; field in ascending order; therefore you can use pagination to incrementally fetch all records while new ones are still being created. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. You can also filter runs by status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle)). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="status">Return only runs with the provided status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle))  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ActorTaskRunsGet200Response)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<ActorTaskRunsGet200Response>> ActorTaskRunsGetWithHttpInfoAsync(string actorTaskId, double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), string? status = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'actorTaskId' is set
            if (actorTaskId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorTaskId' when calling ActorTasksApi->ActorTaskRunsGet");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorTaskId", Apify.SDK.Client.ClientUtils.ParameterToString(actorTaskId)); // path parameter
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (desc != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "desc", desc));
            }
            if (status != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "status", status));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<ActorTaskRunsGet200Response>("/v2/actor-tasks/{actorTaskId}/runs", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorTaskRunsGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Run task Runs an Actor task and immediately returns without waiting for the run to finish.  Optionally, you can override the Actor input configuration by passing a JSON object as the POST payload and setting the &#x60;Content-Type: application/json&#x60; HTTP header.  Note that if the object in the POST payload does not define a particular input property, the Actor run uses the default value defined by the task (or Actor&#39;s input schema if not defined by the task).  The response is the Actor Run object as returned by the [Get run](#/reference/actor-runs/run-object-and-its-storages/get-run) endpoint.  If you want to wait for the run to finish and receive the actual output of the Actor run as the response, use one of the [Run task synchronously](#/reference/actor-tasks/run-task-synchronously) API endpoints instead.  To fetch the Actor run results that are typically stored in the default dataset, you&#39;ll need to pass the ID received in the &#x60;defaultDatasetId&#x60; field received in the response JSON to the [Get items](#/reference/datasets/item-collection/get-items) API endpoint. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default, it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks.  **Note**: if you already have a webhook set up for the Actor or task, you do not have to add it again here.  For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <returns>ActorTaskRunsPost201Response</returns>
        public ActorTaskRunsPost201Response ActorTaskRunsPost(string actorTaskId, Object? body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), double? waitForFinish = default(double?), string? webhooks = default(string?))
        {
            Apify.SDK.Client.ApiResponse<ActorTaskRunsPost201Response> localVarResponse = ActorTaskRunsPostWithHttpInfo(actorTaskId, body, timeout, memory, maxItems, maxTotalChargeUsd, build, waitForFinish, webhooks);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Run task Runs an Actor task and immediately returns without waiting for the run to finish.  Optionally, you can override the Actor input configuration by passing a JSON object as the POST payload and setting the &#x60;Content-Type: application/json&#x60; HTTP header.  Note that if the object in the POST payload does not define a particular input property, the Actor run uses the default value defined by the task (or Actor&#39;s input schema if not defined by the task).  The response is the Actor Run object as returned by the [Get run](#/reference/actor-runs/run-object-and-its-storages/get-run) endpoint.  If you want to wait for the run to finish and receive the actual output of the Actor run as the response, use one of the [Run task synchronously](#/reference/actor-tasks/run-task-synchronously) API endpoints instead.  To fetch the Actor run results that are typically stored in the default dataset, you&#39;ll need to pass the ID received in the &#x60;defaultDatasetId&#x60; field received in the response JSON to the [Get items](#/reference/datasets/item-collection/get-items) API endpoint. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default, it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks.  **Note**: if you already have a webhook set up for the Actor or task, you do not have to add it again here.  For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <returns>ApiResponse of ActorTaskRunsPost201Response</returns>
        public Apify.SDK.Client.ApiResponse<ActorTaskRunsPost201Response> ActorTaskRunsPostWithHttpInfo(string actorTaskId, Object? body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), double? waitForFinish = default(double?), string? webhooks = default(string?))
        {
            // verify the required parameter 'actorTaskId' is set
            if (actorTaskId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorTaskId' when calling ActorTasksApi->ActorTaskRunsPost");

            // verify the required parameter 'body' is set
            //if (body == null)
            //    throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'body' when calling ActorTasksApi->ActorTaskRunsPost");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorTaskId", Apify.SDK.Client.ClientUtils.ParameterToString(actorTaskId)); // path parameter
            if (timeout != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "timeout", timeout));
            }
            if (memory != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "memory", memory));
            }
            if (maxItems != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxItems", maxItems));
            }
            if (maxTotalChargeUsd != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxTotalChargeUsd", maxTotalChargeUsd));
            }
            if (build != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "build", build));
            }
            if (waitForFinish != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "waitForFinish", waitForFinish));
            }
            if (webhooks != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "webhooks", webhooks));
            }
            localVarRequestOptions.Data = body;

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<ActorTaskRunsPost201Response>("/v2/actor-tasks/{actorTaskId}/runs", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorTaskRunsPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Run task Runs an Actor task and immediately returns without waiting for the run to finish.  Optionally, you can override the Actor input configuration by passing a JSON object as the POST payload and setting the &#x60;Content-Type: application/json&#x60; HTTP header.  Note that if the object in the POST payload does not define a particular input property, the Actor run uses the default value defined by the task (or Actor&#39;s input schema if not defined by the task).  The response is the Actor Run object as returned by the [Get run](#/reference/actor-runs/run-object-and-its-storages/get-run) endpoint.  If you want to wait for the run to finish and receive the actual output of the Actor run as the response, use one of the [Run task synchronously](#/reference/actor-tasks/run-task-synchronously) API endpoints instead.  To fetch the Actor run results that are typically stored in the default dataset, you&#39;ll need to pass the ID received in the &#x60;defaultDatasetId&#x60; field received in the response JSON to the [Get items](#/reference/datasets/item-collection/get-items) API endpoint. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default, it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks.  **Note**: if you already have a webhook set up for the Actor or task, you do not have to add it again here.  For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ActorTaskRunsPost201Response</returns>
        public async System.Threading.Tasks.Task<ActorTaskRunsPost201Response> ActorTaskRunsPostAsync(string actorTaskId, Object? body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), double? waitForFinish = default(double?), string? webhooks = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<ActorTaskRunsPost201Response> localVarResponse = await ActorTaskRunsPostWithHttpInfoAsync(actorTaskId, body, timeout, memory, maxItems, maxTotalChargeUsd, build, waitForFinish, webhooks, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Run task Runs an Actor task and immediately returns without waiting for the run to finish.  Optionally, you can override the Actor input configuration by passing a JSON object as the POST payload and setting the &#x60;Content-Type: application/json&#x60; HTTP header.  Note that if the object in the POST payload does not define a particular input property, the Actor run uses the default value defined by the task (or Actor&#39;s input schema if not defined by the task).  The response is the Actor Run object as returned by the [Get run](#/reference/actor-runs/run-object-and-its-storages/get-run) endpoint.  If you want to wait for the run to finish and receive the actual output of the Actor run as the response, use one of the [Run task synchronously](#/reference/actor-tasks/run-task-synchronously) API endpoints instead.  To fetch the Actor run results that are typically stored in the default dataset, you&#39;ll need to pass the ID received in the &#x60;defaultDatasetId&#x60; field received in the response JSON to the [Get items](#/reference/datasets/item-collection/get-items) API endpoint. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the task settings.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the task settings.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the task settings (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default, it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks.  **Note**: if you already have a webhook set up for the Actor or task, you do not have to add it again here.  For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ActorTaskRunsPost201Response)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<ActorTaskRunsPost201Response>> ActorTaskRunsPostWithHttpInfoAsync(string actorTaskId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), double? waitForFinish = default(double?), string? webhooks = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'actorTaskId' is set
            if (actorTaskId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorTaskId' when calling ActorTasksApi->ActorTaskRunsPost");

            // verify the required parameter 'body' is set
            //if (body == null)
            //    throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'body' when calling ActorTasksApi->ActorTaskRunsPost");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorTaskId", Apify.SDK.Client.ClientUtils.ParameterToString(actorTaskId)); // path parameter
            if (timeout != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "timeout", timeout));
            }
            if (memory != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "memory", memory));
            }
            if (maxItems != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxItems", maxItems));
            }
            if (maxTotalChargeUsd != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxTotalChargeUsd", maxTotalChargeUsd));
            }
            if (build != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "build", build));
            }
            if (waitForFinish != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "waitForFinish", waitForFinish));
            }
            if (webhooks != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "webhooks", webhooks));
            }
            localVarRequestOptions.Data = body;

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<ActorTaskRunsPost201Response>("/v2/actor-tasks/{actorTaskId}/runs", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorTaskRunsPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get list of webhooks Gets the list of webhooks of a specific Actor task. The response is a JSON with the list of objects, where each object contains basic information about a single webhook.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 records.  By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order, to sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <returns>ActorTaskWebhooksGet200Response</returns>
        public ActorTaskWebhooksGet200Response ActorTaskWebhooksGet(string actorTaskId, double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?))
        {
            Apify.SDK.Client.ApiResponse<ActorTaskWebhooksGet200Response> localVarResponse = ActorTaskWebhooksGetWithHttpInfo(actorTaskId, offset, limit, desc);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get list of webhooks Gets the list of webhooks of a specific Actor task. The response is a JSON with the list of objects, where each object contains basic information about a single webhook.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 records.  By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order, to sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <returns>ApiResponse of ActorTaskWebhooksGet200Response</returns>
        public Apify.SDK.Client.ApiResponse<ActorTaskWebhooksGet200Response> ActorTaskWebhooksGetWithHttpInfo(string actorTaskId, double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?))
        {
            // verify the required parameter 'actorTaskId' is set
            if (actorTaskId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorTaskId' when calling ActorTasksApi->ActorTaskWebhooksGet");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorTaskId", Apify.SDK.Client.ClientUtils.ParameterToString(actorTaskId)); // path parameter
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (desc != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "desc", desc));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<ActorTaskWebhooksGet200Response>("/v2/actor-tasks/{actorTaskId}/webhooks", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorTaskWebhooksGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get list of webhooks Gets the list of webhooks of a specific Actor task. The response is a JSON with the list of objects, where each object contains basic information about a single webhook.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 records.  By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order, to sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ActorTaskWebhooksGet200Response</returns>
        public async System.Threading.Tasks.Task<ActorTaskWebhooksGet200Response> ActorTaskWebhooksGetAsync(string actorTaskId, double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<ActorTaskWebhooksGet200Response> localVarResponse = await ActorTaskWebhooksGetWithHttpInfoAsync(actorTaskId, offset, limit, desc, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get list of webhooks Gets the list of webhooks of a specific Actor task. The response is a JSON with the list of objects, where each object contains basic information about a single webhook.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 records.  By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order, to sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTaskId">Task ID or a tilde-separated owner&#39;s username and task&#39;s name.</param>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ActorTaskWebhooksGet200Response)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<ActorTaskWebhooksGet200Response>> ActorTaskWebhooksGetWithHttpInfoAsync(string actorTaskId, double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'actorTaskId' is set
            if (actorTaskId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorTaskId' when calling ActorTasksApi->ActorTaskWebhooksGet");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorTaskId", Apify.SDK.Client.ClientUtils.ParameterToString(actorTaskId)); // path parameter
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (desc != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "desc", desc));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<ActorTaskWebhooksGet200Response>("/v2/actor-tasks/{actorTaskId}/webhooks", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorTaskWebhooksGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get list of tasks Gets the complete list of tasks that a user has created or used.  The response is a list of objects in which each object contains essential information about a single task.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters, and it does not return more than a 1000 records.  By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order; therefore you can use pagination to incrementally fetch all tasks while new ones are still being created. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of records that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of records to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <returns>ActorTasksGet200Response</returns>
        public ActorTasksGet200Response ActorTasksGet(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?))
        {
            Apify.SDK.Client.ApiResponse<ActorTasksGet200Response> localVarResponse = ActorTasksGetWithHttpInfo(offset, limit, desc);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get list of tasks Gets the complete list of tasks that a user has created or used.  The response is a list of objects in which each object contains essential information about a single task.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters, and it does not return more than a 1000 records.  By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order; therefore you can use pagination to incrementally fetch all tasks while new ones are still being created. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of records that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of records to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <returns>ApiResponse of ActorTasksGet200Response</returns>
        public Apify.SDK.Client.ApiResponse<ActorTasksGet200Response> ActorTasksGetWithHttpInfo(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?))
        {
            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (desc != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "desc", desc));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<ActorTasksGet200Response>("/v2/actor-tasks", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorTasksGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get list of tasks Gets the complete list of tasks that a user has created or used.  The response is a list of objects in which each object contains essential information about a single task.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters, and it does not return more than a 1000 records.  By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order; therefore you can use pagination to incrementally fetch all tasks while new ones are still being created. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of records that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of records to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ActorTasksGet200Response</returns>
        public async System.Threading.Tasks.Task<ActorTasksGet200Response> ActorTasksGetAsync(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<ActorTasksGet200Response> localVarResponse = await ActorTasksGetWithHttpInfoAsync(offset, limit, desc, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get list of tasks Gets the complete list of tasks that a user has created or used.  The response is a list of objects in which each object contains essential information about a single task.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters, and it does not return more than a 1000 records.  By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order; therefore you can use pagination to incrementally fetch all tasks while new ones are still being created. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of records that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of records to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ActorTasksGet200Response)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<ActorTasksGet200Response>> ActorTasksGetWithHttpInfoAsync(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (desc != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "desc", desc));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<ActorTasksGet200Response>("/v2/actor-tasks", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorTasksGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Create task Create a new task with settings specified by the object passed as JSON in the POST payload.  The response is the full task object as returned by the [Get task](#/reference/tasks/task-object/get-task) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTasksPostRequest"></param>
        /// <returns>ActorTasksPost201Response</returns>
        public ActorTasksPost201Response ActorTasksPost(ActorTasksPostRequest actorTasksPostRequest)
        {
            Apify.SDK.Client.ApiResponse<ActorTasksPost201Response> localVarResponse = ActorTasksPostWithHttpInfo(actorTasksPostRequest);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Create task Create a new task with settings specified by the object passed as JSON in the POST payload.  The response is the full task object as returned by the [Get task](#/reference/tasks/task-object/get-task) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTasksPostRequest"></param>
        /// <returns>ApiResponse of ActorTasksPost201Response</returns>
        public Apify.SDK.Client.ApiResponse<ActorTasksPost201Response> ActorTasksPostWithHttpInfo(ActorTasksPostRequest actorTasksPostRequest)
        {
            // verify the required parameter 'actorTasksPostRequest' is set
            if (actorTasksPostRequest == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorTasksPostRequest' when calling ActorTasksApi->ActorTasksPost");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = actorTasksPostRequest;

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<ActorTasksPost201Response>("/v2/actor-tasks", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorTasksPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Create task Create a new task with settings specified by the object passed as JSON in the POST payload.  The response is the full task object as returned by the [Get task](#/reference/tasks/task-object/get-task) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTasksPostRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ActorTasksPost201Response</returns>
        public async System.Threading.Tasks.Task<ActorTasksPost201Response> ActorTasksPostAsync(ActorTasksPostRequest actorTasksPostRequest, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<ActorTasksPost201Response> localVarResponse = await ActorTasksPostWithHttpInfoAsync(actorTasksPostRequest, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Create task Create a new task with settings specified by the object passed as JSON in the POST payload.  The response is the full task object as returned by the [Get task](#/reference/tasks/task-object/get-task) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorTasksPostRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (ActorTasksPost201Response)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<ActorTasksPost201Response>> ActorTasksPostWithHttpInfoAsync(ActorTasksPostRequest actorTasksPostRequest, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'actorTasksPostRequest' is set
            if (actorTasksPostRequest == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorTasksPostRequest' when calling ActorTasksApi->ActorTasksPost");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.Data = actorTasksPostRequest;

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<ActorTasksPost201Response>("/v2/actor-tasks", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorTasksPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

    }
}

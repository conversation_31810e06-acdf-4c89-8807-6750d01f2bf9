﻿using Apify.SDK.Api;
using Apify.SDK.Client;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json.Linq;
using Phlex.Core.Apify.Extensions;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Models;

namespace Phlex.Core.Apify.Services;

public class DatasetService : IDatasetService
{
    private readonly StorageDatasetsApi datasetsApi;

    private readonly ILogger<DatasetService> logger;

    public DatasetService(IConfiguration configuration, HttpClient httpClient, ILogger<DatasetService> logger)
    {
        var apifyConfiguration = ApifyConfiguration.Get(configuration);
        datasetsApi = new StorageDatasetsApi(httpClient, apifyConfiguration.BaseUri);
        this.logger = logger;
    }

    public async Task<List<object>> GetDatasetItemsAsync(string datasetId, CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await datasetsApi.DatasetItemsGetAsync(datasetId, cancellationToken: cancellationToken);
            if (response is null)
            {
                logger.LogError("Failed to retrieve dataset items for dataset ID: {DatasetId}", datasetId);
                throw new InvalidOperationException($"Failed to retrieve dataset items for dataset ID '{datasetId}'");
            }
            return response;
        }
        catch (ApiException ex)
        {
            logger.LogApiException(ex);
            throw new InvalidOperationException(ex.Message, ex);
        }
    }

    public List<DatasetItem?> ProcessDatasetItemsResponse(List<object> datasetItemsResponse)
    {
        return datasetItemsResponse
           .OfType<JObject>()
           .Select(static jObject => GetDatasetItemFromJObject(jObject))
           .Where(item => item != null)
           .ToList();
    }

    private static DatasetItem? GetDatasetItemFromJObject(JObject jObject)
    {
        if (jObject["url"] is not JToken { Type: JTokenType.String } urlToken || string.IsNullOrWhiteSpace(urlToken.ToString()))
        {
            return null;
        }

        var items = new DatasetItem
        {
            Url = urlToken.ToString(),
            Text = jObject["text"] is JToken { Type: JTokenType.String } textToken ? textToken.ToString() : string.Empty,
            Metadata = jObject.TryGetValue("metadata", out var metadataToken) ? metadataToken.ToObject<DatasetMetadata>() : null,
            Markdown = jObject["markdown"] is JToken { Type: JTokenType.String } mdToken ? mdToken.ToString() : null,
            ScreenshotUrl = jObject["screenshoturl"] is JToken { Type: JTokenType.String } screenshotUrlToken ? screenshotUrlToken.ToString() : string.Empty,
        };

        return items;
    }
}
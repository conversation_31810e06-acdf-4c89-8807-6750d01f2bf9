﻿namespace Phlex.Core.Apify.Webhook.Models
{
    public class ApifyWebhookPayload
    {
        public string? userId { get; set; }
        public DateTime? createdAt { get; set; }
        public string? eventType { get; set; }
        public EventData? eventData { get; set; }
        public Resource? resource { get; set; }
    }

    public class EventData
    {
        public string? actorId { get; set; }
        public string? actorTaskId { get; set; }
        public string? actorRunId { get; set; }
    }

    public class Resource
    {
        public string? id { get; set; }
        public string? actId { get; set; }
        public string? userId { get; set; }
        public string? actorTaskId { get; set; }
        public DateTime? startedAt { get; set; }
        public DateTime? finishedAt { get; set; }
        public string? status { get; set; }
        public string? statusMessage { get; set; }
        public string? createdByOrganizationMemberUserId { get; set; }
        public string? buildId { get; set; }
        public int? exitCode { get; set; }
        public string? defaultKeyValueStoreId { get; set; }
        public string? defaultDatasetId { get; set; }
        public Usage? usage { get; set; }
        public double? usageTotalUsd { get; set; }
        public UsageUsd? usageUsd { get; set; }
    }

    public class Usage
    {
        public double? ACTOR_COMPUTE_UNITS { get; set; }
    }

    public class UsageUsd
    {
        public double? ACTOR_COMPUTE_UNITS { get; set; }
    }

}

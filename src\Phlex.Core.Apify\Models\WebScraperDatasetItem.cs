﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Org.BouncyCastle.Asn1.Cms;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Phlex.Core.Apify.Models
{
    public class WebScraperDatasetItem
    {
        public string? url { get; set; }
        public Crawl? crawl { get; set; }
        public Metadata? metadata { get; set; }
        public string? screenshotUrl { get; set; }
        public string? text { get; set; }
        public string? markdown { get; set; }
        public string? fileUrl { get; set; }
    }


    public class About
    {
        [JsonProperty("@id")]
        public string? id { get; set; }
    }

    public class Author
    {
        [JsonProperty("@type")]
        public string? type { get; set; }
        public string? name { get; set; }
    }

    public class Breadcrumb
    {
        [JsonProperty("@id")]
        public string? id { get; set; }
    }

    public class Crawl
    {
        public string? loadedUrl { get; set; }
        public DateTime? loadedTime { get; set; }
        public string? referrerUrl { get; set; }
        public int depth { get; set; }
        public int httpStatusCode { get; set; }
    }

   

    public class Graph
    {
        [JsonProperty("@type")]
        public string? type { get; set; }

        [JsonProperty("@id")]
        public string? id { get; set; }
        public string? name { get; set; }
        public string? url { get; set; }
        public Publisher? publisher { get; set; }
        public PotentialAction? potentialAction { get; set; }
        public string? description { get; set; }
        public IsPartOf? isPartOf { get; set; }
        public About? about { get; set; }
        public string? inLanguage { get; set; }
        public Breadcrumb? breadcrumb { get; set; }
        public string? headline { get; set; }
        public Author? author { get; set; }
        public string? articleSection { get; set; }
        public DateTime? dateCreated { get; set; }
        public InteractionStatistic? interactionStatistic { get; set; }
    }

    public class Headers
    {
        public string? server { get; set; }
        public string? date { get; set; }

        [JsonProperty("content-type")]
        public string? contenttype { get; set; }
        public string? vary { get; set; }

        [JsonProperty("x-powered-by")]
        public string? xpoweredby { get; set; }

        [JsonProperty("x-frame-options")]
        public string? xframeoptions { get; set; }

        [JsonProperty("referrer-policy")]
        public string? referrerpolicy { get; set; }

        [JsonProperty("cross-origin-opener-policy")]
        public string? crossoriginopenerpolicy { get; set; }
        public string? expires { get; set; }

        [JsonProperty("cache-control")]
        public string? cachecontrol { get; set; }
        public string? pragma { get; set; }

        [JsonProperty("x-content-type-options")]
        public string? xcontenttypeoptions { get; set; }

        [JsonProperty("last-modified")]
        public string? lastmodified { get; set; }

        [JsonProperty("x-scale")]
        public string? xscale { get; set; }

        [JsonProperty("content-encoding")]
        public string? contentencoding { get; set; }

        [JsonProperty("x-firefox-spdy")]
        public string? xfirefoxspdy { get; set; }
    }

    public class InteractionStatistic
    {
        [JsonProperty("@type")]
        public string? type { get; set; }
        public int userInteractionCount { get; set; }
    }

    public class IsPartOf
    {
        [JsonProperty("@id")]
        public string? id { get; set; }
    }

    public class JsonLd
    {
        [JsonProperty("@context")]
        public string? context { get; set; }

        [JsonProperty("@graph")]
        public List<Graph>? graph { get; set; }
    }

    public class Metadata
    {
        public string? canonicalUrl { get; set; }
        public string? title { get; set; }
        public string? description { get; set; }
        public string? author { get; set; }
        public object? keywords { get; set; }
        public string? languageCode { get; set; }
        public List<JsonLd>? jsonLd { get; set; }
        public Headers? headers { get; set; }
    }

    public class PotentialAction
    {
        [JsonProperty("@type")]
        public string? type { get; set; }
        public string? target { get; set; }

        [JsonProperty("query-input")]
        public string? queryinput { get; set; }
    }

    public class Publisher
    {
        [JsonProperty("@id")]
        public string? id { get; set; }
    }
}

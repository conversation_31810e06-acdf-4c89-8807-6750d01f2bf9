﻿using Phlex.Core.Apify.IntegrationTests.Builders;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Models;

namespace Phlex.Core.Apify.IntegrationTests.Utils;

public class FakeCrawlerDataProcessBatches : ICrawlerData
{
    public Task<CrawlerConfiguration> GetConfigurationAsync(string configName)
    {
        var configuration = CrawlerConfigurationBuilder.Default().WithName(configName)
            .WithJsonConfiguration(
                "{\"startUrls\": [], \"maxCrawlDepth\": 0, \"proxyConfiguration\": {\"mode\": \"global\"}}").Build();

        return Task.FromResult(configuration);
    }

    public Task<IQueryable<UrlItem>> GetStartUrlsAsync()
    {
        var urls = Enumerable.Range(1, 25).Select(i =>
            UrlItemBuilder.Default()
                .WithDomain("domain1.com")
                .WithConfigurationName("config1")
                .WithMaxCrawlDepth(1)
                .WithUrl($"https://domain1.com/page{i}")
                .Build())
            .ToList();

        return Task.FromResult(urls.AsQueryable());
    }
}

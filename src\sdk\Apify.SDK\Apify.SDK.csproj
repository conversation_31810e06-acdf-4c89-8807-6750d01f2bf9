﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo><!-- setting GenerateAssemblyInfo to false causes this bug https://github.com/dotnet/project-system/issues/3934 -->
    <TargetFramework>net8.0</TargetFramework>
    <AssemblyName>Apify.SDK</AssemblyName>
    <PackageId>Apify.SDK</PackageId>
    <OutputType>Library</OutputType>
    <Authors>OpenAPI</Authors>
    <Company>OpenAPI</Company>
    <AssemblyTitle>OpenAPI Library</AssemblyTitle>
    <Description>A library generated from a OpenAPI doc</Description>
    <Copyright>No Copyright</Copyright>
    <RootNamespace>Apify.SDK</RootNamespace>
    <Version>1.0.0</Version>
    <DocumentationFile>bin\$(Configuration)\$(TargetFramework)\Apify.SDK.xml</DocumentationFile>
    <RepositoryUrl>https://github.com/GIT_USER_ID/GIT_REPO_ID.git</RepositoryUrl>
    <RepositoryType>git</RepositoryType>
    <PackageReleaseNotes>Minor update</PackageReleaseNotes>
    <Nullable>annotations</Nullable>
    <ManagePackageVersionsCentrally>false</ManagePackageVersionsCentrally>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="JsonSubTypes" Version="2.0.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Polly" Version="8.1.0" />
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
</Project>

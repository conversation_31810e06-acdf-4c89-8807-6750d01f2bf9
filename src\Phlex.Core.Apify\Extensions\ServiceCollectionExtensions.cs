﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Services;

namespace Phlex.Core.Apify.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddApify(this IServiceCollection services, IConfiguration configuration)
    {
        var apifyConfiguration = ApifyConfiguration.Get(configuration);
        services.AddScoped<IDownloadService, DownloadService>();
        services.AddScoped<IDatasetService, DatasetService>();
        services.AddScoped<ICrawlerConfigurationService, CrawlerConfigurationService>();
        services.AddScoped<IApifyClient, ApifyClient>();

        services.AddHttpClient<IApifyClient, ApifyClient>((_, client) =>
        {
            client.DefaultRequestHeaders.Add("Authorization", "Bearer " + apifyConfiguration.AccessToken);
            client.BaseAddress = new Uri(apifyConfiguration.BaseUri);
        }).ConfigurePrimaryHttpMessageHandler(() =>
            new SocketsHttpHandler() { PooledConnectionLifetime = TimeSpan.FromMinutes(15) });

        return services;
    }
}
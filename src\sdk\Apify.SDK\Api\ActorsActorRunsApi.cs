/*
 * Apify API
 *
 *  > **UPDATE 2025-01-14:** > We have rolled out this new Apify API Documentation. In case of any issues, please [report here](https://github.com/apify/apify-docs/issues). > The old API Documentation is still [available here](https://docs.apify.com/api/v2-old).  The Apify API (version 2) provides programmatic access to the [Apify platform](https://docs.apify.com). The API is organized around [RESTful](https://en.wikipedia.org/wiki/Representational_state_transfer) HTTP endpoints.  You can download the complete OpenAPI schema of Apify API in the [YAML](http://docs.apify.com/api/openapi.yaml) or [JSON](http://docs.apify.com/api/openapi.json) formats. The source code is also available on [GitHub](https://github.com/apify/apify-docs/tree/master/apify-api/openapi).  All requests and responses (including errors) are encoded in [JSON](http://www.json.org/) format with UTF-8 encoding, with a few exceptions that are explicitly described in the reference.  To access the API using [Node.js](https://nodejs.org/en/), we recommend the [`apify-client`](https://docs.apify.com/api/client/js) [NPM package](https://www.npmjs.com/package/apify-client).  To access the API using [Python](https://www.python.org/), we recommend the [`apify-client`](https://docs.apify.com/api/client/python) [PyPI package](https://pypi.org/project/apify-client/). The clients' functions correspond to the API endpoints and have the same parameters. This simplifies development of apps that depend on the Apify platform.  **Note:** All requests with JSON payloads need to specify the `Content-Type: application/json` HTTP header! All API endpoints support the `method` query parameter that can override the HTTP method. For example, if you want to call a POST endpoint using a GET request, simply add the query parameter `method=POST` to the URL and send the GET request. This feature is especially useful if you want to call Apify API endpoints from services that can only send GET requests.  ## Authentication <span id=\"/introduction/authentication\"></span>  You can find your API token on the [Integrations](https://console.apify.com/account#/integrations) page in the Apify Console.  To use your token in a request, either:  - Add the token to your request's `Authorization` header as `Bearer <token>`. E.g., `Authorization: Bearer xxxxxxx`. [More info](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Authorization). (Recommended). - Add it as the `token` parameter to your request URL. (Less secure).  Using your token in the request header is more secure than using it as a URL parameter because URLs are often stored in browser history and server logs. This creates a chance for someone unauthorized to access your API token.  **Do not share your API token or password with untrusted parties.**  For more information, see our [integrations](https://docs.apify.com/platform/integrations) documentation.  ## Basic usage <span id=\"/introduction/basic-usage\"></span>  To run an Actor, send a POST request to the [Run Actor](#/reference/actors/run-collection/run-actor) endpoint using either the Actor ID code (e.g. `vKg4IjxZbEYTYeW8T`) or its name (e.g. `janedoe~my-actor`):  `https://api.apify.com/v2/acts/[actor_id]/runs`  If the Actor is not runnable anonymously, you will receive a 401 or 403 [response code](https://developer.mozilla.org/en-US/docs/Web/HTTP/Status). This means you need to add your [secret API token](https://console.apify.com/account#/integrations) to the request's `Authorization` header ([recommended](#/introduction/authentication)) or as a URL query parameter `?token=[your_token]` (less secure).  Optionally, you can include the query parameters described in the [Run Actor](#/reference/actors/run-collection/run-actor) section to customize your run.  If you're using Node.js, the best way to run an Actor is using the `Apify.call()` method from the [Apify SDK](https://sdk.apify.com/docs/api/apify#apifycallactid-input-options). It runs the Actor using the account you are currently logged into (determined by the [secret API token](https://console.apify.com/account#/integrations)). The result is an [Actor run object](https://sdk.apify.com/docs/typedefs/actor-run) and its output (if any).  A typical workflow is as follows:  1. Run an Actor or task using the [Run Actor](#/reference/actors/run-collection/run-actor) or [Run task](#/reference/actor-tasks/run-collection/run-task) API endpoints. 2. Monitor the Actor run by periodically polling its progress using the [Get run](#/reference/actor-runs/run-object-and-its-storages/get-run) API endpoint. 3. Fetch the results from the [Get items](#/reference/datasets/item-collection/get-items) API endpoint using the `defaultDatasetId`, which you receive in the Run request response. Additional data may be stored in a key-value store. You can fetch them from the [Get record](#/reference/key-value-stores/record/get-record) API endpoint using the `defaultKeyValueStoreId` and the store's `key`.  **Note**: Instead of periodic polling, you can also run your [Actor](#/reference/actors/run-actor-synchronously) or [task](#/reference/actor-tasks/runs-collection/run-task-synchronously) synchronously. This will ensure that the request waits for 300 seconds (5 minutes) for the run to finish and returns its output. If the run takes longer, the request will time out and throw an error.  ## Response structure <span id=\"/introduction/response-structure\"></span>  Most API endpoints return a JSON object with the `data` property:  ``` {     \"data\": {         ...     } } ```  However, there are a few explicitly described exceptions, such as Dataset [Get items](#/reference/datasets/item-collection/get-items) or Key-value store [Get record](#/reference/key-value-stores/record/get-record) API endpoints, which return data in other formats. In case of an error, the response has the HTTP status code in the range of 4xx or 5xx and the `data` property is replaced with `error`. For example:  ``` {     \"error\": {         \"type\": \"record-not-found\",         \"message\": \"Store was not found.\"     } } ```  See [Errors](#/introduction/errors) for more details.  ## Pagination <span id=\"/introduction/pagination\"></span>  All API endpoints that return a list of records (e.g. [Get list of Actors](#/reference/actors/actor-collection/get-list-of-actors)) enforce pagination in order to limit the size of their responses.  Most of these API endpoints are paginated using the `offset` and `limit` query parameters. The only exception is [Get list of keys](#/reference/key-value-stores/key-collection/get-list-of-keys), which is paginated using the `exclusiveStartKey` query parameter.  **IMPORTANT**: Each API endpoint that supports pagination enforces a certain maximum value for the `limit` parameter, in order to reduce the load on Apify servers. The maximum limit could change in future so you should never rely on a specific value and check the responses of these API endpoints.  ### Using offset <span id=\"/introduction/pagination/using-offset\"></span>  Most API endpoints that return a list of records enable pagination using the following query parameters:  <table>   <tr>     <td><code>limit</code></td>     <td>Limits the response to contain a specific maximum number of items, e.g. <code>limit=20</code>.</td>   </tr>   <tr>     <td><code>offset</code></td>     <td>Skips a number of items from the beginning of the list, e.g. <code>offset=100</code>.</td>   </tr>   <tr>     <td><code>desc</code></td>     <td>     By default, items are sorted in the order in which they were created or added to the list.     This feature is useful when fetching all the items, because it ensures that items     created after the client started the pagination will not be skipped.     If you specify the <code>desc=1</code> parameter, the items will be returned in the reverse order,     i.e. from the newest to the oldest items.     </td>   </tr> </table>  The response of these API endpoints is always a JSON object with the following structure:  ``` {     \"data\": {         \"total\": 2560,         \"offset\": 250,         \"limit\": 1000,         \"count\": 1000,         \"desc\": false,         \"items\": [             { 1st object },             { 2nd object },             ...             { 1000th object }         ]     } } ```  The following table describes the meaning of the response properties:  <table>   <tr>     <th>Property</th>     <th>Description</th>   </tr>   <tr>     <td><code>total</code></td>     <td>The total number of items available in the list.</td>   </tr>   <tr>     <td><code>offset</code></td>     <td>The number of items that were skipped at the start.     This is equal to the <code>offset</code> query parameter if it was provided, otherwise it is <code>0</code>.</td>   </tr>   <tr>     <td><code>limit</code></td>     <td>The maximum number of items that can be returned in the HTTP response.     It equals to the <code>limit</code> query parameter if it was provided or     the maximum limit enforced for the particular API endpoint, whichever is smaller.</td>   </tr>   <tr>     <td><code>count</code></td>     <td>The actual number of items returned in the HTTP response.</td>   </tr>   <tr>     <td><code>desc</code></td>     <td><code>true</code> if data were requested in descending order and <code>false</code> otherwise.</td>   </tr>   <tr>     <td><code>items</code></td>     <td>An array of requested items.</td>   </tr> </table>  ### Using key <span id=\"/introduction/pagination/using-key\"></span>  The records in the [key-value store](https://docs.apify.com/platform/storage/key-value-store) are not ordered based on numerical indexes, but rather by their keys in the UTF-8 binary order. Therefore the [Get list of keys](#/reference/key-value-stores/key-collection/get-list-of-keys) API endpoint only supports pagination using the following query parameters:  <table>   <tr>     <td><code>limit</code></td>     <td>Limits the response to contain a specific maximum number items, e.g. <code>limit=20</code>.</td>   </tr>   <tr>     <td><code>exclusiveStartKey</code></td>     <td>Skips all records with keys up to the given key including the given key,     in the UTF-8 binary order.</td>   </tr> </table>  The response of the API endpoint is always a JSON object with following structure:  ``` {     \"data\": {         \"limit\": 1000,         \"isTruncated\": true,         \"exclusiveStartKey\": \"my-key\",         \"nextExclusiveStartKey\": \"some-other-key\",         \"items\": [             { 1st object },             { 2nd object },             ...             { 1000th object }         ]     } } ```  The following table describes the meaning of the response properties:  <table>   <tr>     <th>Property</th>     <th>Description</th>   </tr>   <tr>     <td><code>limit</code></td>     <td>The maximum number of items that can be returned in the HTTP response.     It equals to the <code>limit</code> query parameter if it was provided or     the maximum limit enforced for the particular endpoint, whichever is smaller.</td>   </tr>   <tr>     <td><code>isTruncated</code></td>     <td><code>true</code> if there are more items left to be queried. Otherwise <code>false</code>.</td>   </tr>   <tr>     <td><code>exclusiveStartKey</code></td>     <td>The last key that was skipped at the start. Is `null` for the first page.</td>   </tr>   <tr>     <td><code>nextExclusiveStartKey</code></td>     <td>The value for the <code>exclusiveStartKey</code> parameter to query the next page of items.</td>   </tr> </table>  ## Errors <span id=\"/introduction/errors\"></span>  The Apify API uses common HTTP status codes: `2xx` range for success, `4xx` range for errors caused by the caller (invalid requests) and `5xx` range for server errors (these are rare). Each error response contains a JSON object defining the `error` property, which is an object with the `type` and `message` properties that contain the error code and a human-readable error description, respectively.  For example:  ``` {     \"error\": {         \"type\": \"record-not-found\",         \"message\": \"Store was not found.\"     } } ```  Here is the table of the most common errors that can occur for many API endpoints:  <table>   <tr>     <th>status</th>     <th>type</th>     <th>message</th>   </tr>   <tr>     <td><code>400</code></td>     <td><code>invalid-request</code></td>     <td>POST data must be a JSON object</td>   </tr>   <tr>     <td><code>400</code></td>     <td><code>invalid-value</code></td>     <td>Invalid value provided: Comments required</td>   </tr>   <tr>     <td><code>400</code></td>     <td><code>invalid-record-key</code></td>     <td>Record key contains invalid character</td>   </tr>   <tr>     <td><code>401</code></td>     <td><code>token-not-provided</code></td>     <td>Authentication token was not provided</td>   </tr>   <tr>     <td><code>404</code></td>     <td><code>record-not-found</code></td>     <td>Store was not found</td>   </tr>   <tr>     <td><code>429</code></td>     <td><code>rate-limit-exceeded</code></td>     <td>You have exceeded the rate limit of 30 requests per second</td>   </tr>   <tr>     <td><code>405</code></td>     <td><code>method-not-allowed</code></td>     <td>This API endpoint can only be accessed using the following HTTP methods: OPTIONS, POST</td>   </tr> </table>  ## Rate limiting <span id=\"/introduction/rate-limiting\"></span>  All API endpoints limit the rate of requests in order to prevent overloading of Apify servers by misbehaving clients.  There are two kinds of rate limits - a global rate limit and a per-resource rate limit.  ### Global rate limit <span id=\"/introduction/rate-limiting/global-rate-limit\"></span>  The global rate limit is set to _250 000 requests per minute_. For [authenticated](#/introduction/authentication) requests, it is counted per user, and for unauthenticated requests, it is counted per IP address.  ### Per-resource rate limit <span id=\"/introduction/rate-limiting/per-resource-rate-limit\"></span>  The default per-resource rate limit is _30 requests per second per resource_, which in this context means a single Actor, a single Actor run, a single dataset, single key-value store etc. The default rate limit is applied to every API endpoint except a few select ones, which have higher rate limits. Each API endpoint returns its rate limit in `X-RateLimit-Limit` header.  These endpoints have a rate limit of _100 requests per second per resource_:  * CRUD ([get](#/reference/key-value-stores/record/get-record),   [put](#/reference/key-value-stores/record/put-record),   [delete](#/reference/key-value-stores/record/delete-record))   operations on key-value store records  These endpoints have a rate limit of _200 requests per second per resource_: * [Run Actor](#/reference/actors/run-collection/run-actor) * [Run Actor task asynchronously](#/reference/actor-tasks/runs-collection/run-task-asynchronously) * [Run Actor task synchronously](#/reference/actor-tasks/runs-collection/run-task-synchronously) * [Metamorph Actor run](#/reference/actors/metamorph-run/metamorph-run) * [Push items](#/reference/datasets/item-collection/put-items) to dataset * CRUD   ([add](#/reference/request-queues/request-collection/add-request),   [get](#/reference/request-queues/request-collection/get-request),   [update](#/reference/request-queues/request-collection/update-request),   [delete](#/reference/request-queues/request-collection/delete-request))   operations on requests in request queues  ### Rate limit exceeded errors <span id=\"/introduction/rate-limiting/rate-limit-exceeded-errors\"></span>  If the client is sending too many requests, the API endpoints respond with the HTTP status code `429 Too Many Requests` and the following body:  ``` {     \"error\": {         \"type\": \"rate-limit-exceeded\",         \"message\": \"You have exceeded the rate limit of ... requests per second\"     } } ```  ### Retrying rate-limited requests with exponential backoff <span id=\"/introduction/rate-limiting/retrying-rate-limited-requests-with-exponential-backoff\"></span>  If the client receives the rate limit error, it should wait a certain period of time and then retry the request. If the error happens again, the client should double the wait period and retry the request, and so on. This algorithm is known as _exponential backoff_ and it can be described using the following pseudo-code:  1. Define a variable `DELAY=500` 2. Send the HTTP request to the API endpoint 3. If the response has status code not equal to `429` then you are done. Otherwise:    * Wait for a period of time chosen randomly from the interval `DELAY` to `2*DELAY` milliseconds    * Double the future wait period by setting `DELAY = 2*DELAY`    * Continue with step 2  If all requests sent by the client implement the above steps, the client will automatically use the maximum available bandwidth for its requests.  Note that the Apify API clients [for JavaScript](https://docs.apify.com/api/client/js) and [for Python](https://docs.apify.com/api/client/python) use the exponential backoff algorithm transparently, so that you do not need to worry about it.  ## Referring to resources <span id=\"/introduction/referring-to-resources\"></span>  There are three main ways to refer to a resource you're accessing via API.  - the resource ID (e.g. `iKkPcIgVvwmztduf8`) - `username~resourcename` - when using this access method, you will need to use your API token, and access will only work if you have the correct permissions. - `~resourcename` - for this, you need to use an API token, and the `resourcename` refers to a resource in the API token owner's account. 
 *
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Mime;
using Apify.SDK.Client;
using Apify.SDK.Model;

namespace Apify.SDK.Api
{

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IActorsActorRunsApiSync : IApiAccessor
    {
        #region Synchronous Operations
        /// <summary>
        /// Abort run
        /// </summary>
        /// <remarks>
        /// **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs). Aborts an Actor run and returns an object that contains all the details about the run.  Only runs that are starting or running are aborted. For runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTING&#x60; and &#x60;TIMED-OUT&#x60; this call does nothing. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Run ID.</param>
        /// <param name="gracefully">If true passed, the Actor run will abort gracefully. It will send &#x60;aborting&#x60; and &#x60;persistState&#x60; event into run and force-stop the run after 30 seconds. It is helpful in cases where you plan to resurrect the run later.  (optional)</param>
        /// <returns>RunResponse</returns>
        [Obsolete]
        RunResponse ActRunAbortPost(string actorId, string runId, bool? gracefully = default(bool?));

        /// <summary>
        /// Abort run
        /// </summary>
        /// <remarks>
        /// **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs). Aborts an Actor run and returns an object that contains all the details about the run.  Only runs that are starting or running are aborted. For runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTING&#x60; and &#x60;TIMED-OUT&#x60; this call does nothing. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Run ID.</param>
        /// <param name="gracefully">If true passed, the Actor run will abort gracefully. It will send &#x60;aborting&#x60; and &#x60;persistState&#x60; event into run and force-stop the run after 30 seconds. It is helpful in cases where you plan to resurrect the run later.  (optional)</param>
        /// <returns>ApiResponse of RunResponse</returns>
        [Obsolete]
        ApiResponse<RunResponse> ActRunAbortPostWithHttpInfo(string actorId, string runId, bool? gracefully = default(bool?));
        /// <summary>
        /// Get run
        /// </summary>
        /// <remarks>
        /// **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs).  Gets an object that contains all the details about a specific run of an Actor.  By passing the optional &#x60;waitForFinish&#x60; parameter the API endpoint will synchronously wait for the run to finish. This is useful to avoid periodic polling when waiting for Actor run to complete.  This endpoint does not require the authentication token. Instead, calls are authenticated using a hard-to-guess ID of the run. However, if you access the endpoint without the token, certain attributes, such as &#x60;usageUsd&#x60; and &#x60;usageTotalUsd&#x60;, will be hidden. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Run ID.</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <returns>RunResponse</returns>
        [Obsolete]
        RunResponse ActRunGet(string actorId, string runId, double? waitForFinish = default(double?));

        /// <summary>
        /// Get run
        /// </summary>
        /// <remarks>
        /// **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs).  Gets an object that contains all the details about a specific run of an Actor.  By passing the optional &#x60;waitForFinish&#x60; parameter the API endpoint will synchronously wait for the run to finish. This is useful to avoid periodic polling when waiting for Actor run to complete.  This endpoint does not require the authentication token. Instead, calls are authenticated using a hard-to-guess ID of the run. However, if you access the endpoint without the token, certain attributes, such as &#x60;usageUsd&#x60; and &#x60;usageTotalUsd&#x60;, will be hidden. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Run ID.</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <returns>ApiResponse of RunResponse</returns>
        [Obsolete]
        ApiResponse<RunResponse> ActRunGetWithHttpInfo(string actorId, string runId, double? waitForFinish = default(double?));
        /// <summary>
        /// Metamorph run
        /// </summary>
        /// <remarks>
        /// **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs).Transforms an Actor run into a run of another Actor with a new input.  This is useful if you want to use another Actor to finish the work of your current Actor run, without the need to create a completely new run and waiting for its finish. For the users of your Actors, the metamorph operation is transparent, they will just see your Actor got the work done.  There is a limit on how many times you can metamorph a single run. You can check the limit in [the Actor runtime limits](https://docs.apify.com/platform/limits#actor-limits).  Internally, the system stops the Docker container corresponding to the Actor run and starts a new container using a different Docker image. All the default storages are preserved and the new input is stored under the &#x60;INPUT-METAMORPH-1&#x60; key in the same default key-value store.  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/development/programming-interface/metamorph). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Actor run ID.</param>
        /// <param name="targetActorId">ID of a target Actor that the run should be transformed into.</param>
        /// <param name="build">Optional build of the target Actor.  It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the target Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <returns>RunResponse</returns>
        [Obsolete]
        RunResponse ActRunMetamorphPost(string actorId, string runId, string targetActorId, string? build = default(string?));

        /// <summary>
        /// Metamorph run
        /// </summary>
        /// <remarks>
        /// **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs).Transforms an Actor run into a run of another Actor with a new input.  This is useful if you want to use another Actor to finish the work of your current Actor run, without the need to create a completely new run and waiting for its finish. For the users of your Actors, the metamorph operation is transparent, they will just see your Actor got the work done.  There is a limit on how many times you can metamorph a single run. You can check the limit in [the Actor runtime limits](https://docs.apify.com/platform/limits#actor-limits).  Internally, the system stops the Docker container corresponding to the Actor run and starts a new container using a different Docker image. All the default storages are preserved and the new input is stored under the &#x60;INPUT-METAMORPH-1&#x60; key in the same default key-value store.  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/development/programming-interface/metamorph). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Actor run ID.</param>
        /// <param name="targetActorId">ID of a target Actor that the run should be transformed into.</param>
        /// <param name="build">Optional build of the target Actor.  It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the target Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <returns>ApiResponse of RunResponse</returns>
        [Obsolete]
        ApiResponse<RunResponse> ActRunMetamorphPostWithHttpInfo(string actorId, string runId, string targetActorId, string? build = default(string?));
        /// <summary>
        /// Resurrect run
        /// </summary>
        /// <remarks>
        /// **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs).Resurrects a finished Actor run and returns an object that contains all the details about the resurrected run.  Only finished runs, i.e. runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTED&#x60; and &#x60;TIMED-OUT&#x60; can be resurrected. Run status will be updated to RUNNING and its container will be restarted with the same storages (the same behaviour as when the run gets migrated to the new server).  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/running/runs-and-builds#resurrection-of-finished-run). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Run ID.</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the run that is being resurrected (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the run that is being resurrected.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the run that is being resurrected.  (optional)</param>
        /// <returns>RunResponse</returns>
        RunResponse ActRunResurrectPost(string actorId, string runId, string? build = default(string?), double? timeout = default(double?), double? memory = default(double?));

        /// <summary>
        /// Resurrect run
        /// </summary>
        /// <remarks>
        /// **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs).Resurrects a finished Actor run and returns an object that contains all the details about the resurrected run.  Only finished runs, i.e. runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTED&#x60; and &#x60;TIMED-OUT&#x60; can be resurrected. Run status will be updated to RUNNING and its container will be restarted with the same storages (the same behaviour as when the run gets migrated to the new server).  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/running/runs-and-builds#resurrection-of-finished-run). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Run ID.</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the run that is being resurrected (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the run that is being resurrected.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the run that is being resurrected.  (optional)</param>
        /// <returns>ApiResponse of RunResponse</returns>
        ApiResponse<RunResponse> ActRunResurrectPostWithHttpInfo(string actorId, string runId, string? build = default(string?), double? timeout = default(double?), double? memory = default(double?));
        /// <summary>
        /// Without input
        /// </summary>
        /// <remarks>
        /// Runs a specific Actor and returns its output. The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the API endpoint returns a timeout error. The Actor is not passed any input.  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <returns>Object</returns>
        Object ActRunSyncGet(string actorId, string? outputRecordKey = default(string?), double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?));

        /// <summary>
        /// Without input
        /// </summary>
        /// <remarks>
        /// Runs a specific Actor and returns its output. The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the API endpoint returns a timeout error. The Actor is not passed any input.  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <returns>ApiResponse of Object</returns>
        ApiResponse<Object> ActRunSyncGetWithHttpInfo(string actorId, string? outputRecordKey = default(string?), double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?));
        /// <summary>
        /// Run Actor synchronously without input and get dataset items
        /// </summary>
        /// <remarks>
        /// Runs a specific Actor and returns its dataset items. The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the API endpoint returns a timeout error. The Actor is not passed any input.  It allows to send all possible options in parameters from [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures. For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;. The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not. If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output. This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <returns>Object</returns>
        Object ActRunSyncGetDatasetItemsGet(string actorId, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?));

        /// <summary>
        /// Run Actor synchronously without input and get dataset items
        /// </summary>
        /// <remarks>
        /// Runs a specific Actor and returns its dataset items. The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the API endpoint returns a timeout error. The Actor is not passed any input.  It allows to send all possible options in parameters from [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures. For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;. The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not. If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output. This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <returns>ApiResponse of Object</returns>
        ApiResponse<Object> ActRunSyncGetDatasetItemsGetWithHttpInfo(string actorId, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?));
        /// <summary>
        /// Run Actor synchronously with input and get dataset items
        /// </summary>
        /// <remarks>
        /// Runs a specific Actor and returns its dataset items.  The POST payload including its &#x60;Content-Type&#x60; header is passed as &#x60;INPUT&#x60; to the Actor (usually &#x60;application/json&#x60;). The HTTP response contains the Actors dataset items, while the format of items depends on specifying dataset items&#39; &#x60;format&#x60; parameter.  You can send all the same options in parameters as the [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  The Actor is started with the default options; you can override them using URL query parameters. If the Actor run exceeds 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds, the HTTP response will return the 408 status code (Request Timeout).  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object. If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures. For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;. The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not. If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output. This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <returns>Object</returns>
        Object ActRunSyncGetDatasetItemsPost(string actorId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?));

        /// <summary>
        /// Run Actor synchronously with input and get dataset items
        /// </summary>
        /// <remarks>
        /// Runs a specific Actor and returns its dataset items.  The POST payload including its &#x60;Content-Type&#x60; header is passed as &#x60;INPUT&#x60; to the Actor (usually &#x60;application/json&#x60;). The HTTP response contains the Actors dataset items, while the format of items depends on specifying dataset items&#39; &#x60;format&#x60; parameter.  You can send all the same options in parameters as the [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  The Actor is started with the default options; you can override them using URL query parameters. If the Actor run exceeds 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds, the HTTP response will return the 408 status code (Request Timeout).  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object. If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures. For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;. The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not. If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output. This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <returns>ApiResponse of Object</returns>
        ApiResponse<Object> ActRunSyncGetDatasetItemsPostWithHttpInfo(string actorId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?));
        /// <summary>
        /// Run Actor synchronously with input and return output
        /// </summary>
        /// <remarks>
        /// Runs a specific Actor and returns its output.  The POST payload including its &#x60;Content-Type&#x60; header is passed as &#x60;INPUT&#x60; to the Actor (usually &lt;code&gt;application/json&lt;/code&gt;). The HTTP response contains Actors &#x60;OUTPUT&#x60; record from its default key-value store.  The Actor is started with the default options; you can override them using various URL query parameters. If the Actor run exceeds 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds, the HTTP response will have status 408 (Request Timeout).  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="body"></param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <returns>Object</returns>
        Object ActRunSyncPost(string actorId, Object body, string? outputRecordKey = default(string?), double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?));

        /// <summary>
        /// Run Actor synchronously with input and return output
        /// </summary>
        /// <remarks>
        /// Runs a specific Actor and returns its output.  The POST payload including its &#x60;Content-Type&#x60; header is passed as &#x60;INPUT&#x60; to the Actor (usually &lt;code&gt;application/json&lt;/code&gt;). The HTTP response contains Actors &#x60;OUTPUT&#x60; record from its default key-value store.  The Actor is started with the default options; you can override them using various URL query parameters. If the Actor run exceeds 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds, the HTTP response will have status 408 (Request Timeout).  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="body"></param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <returns>ApiResponse of Object</returns>
        ApiResponse<Object> ActRunSyncPostWithHttpInfo(string actorId, Object body, string? outputRecordKey = default(string?), double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?));
        /// <summary>
        /// Get list of runs
        /// </summary>
        /// <remarks>
        /// Gets the list of runs of a specific Actor. The response is a list of objects, where each object contains basic information about a single Actor run.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 array elements.  By default, the records are sorted by the &#x60;startedAt&#x60; field in ascending order, therefore you can use pagination to incrementally fetch all records while new ones are still being created. To sort the records in descending order, use &#x60;desc&#x3D;1&#x60; parameter. You can also filter runs by status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle)). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="status">Return only runs with the provided status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle))  (optional)</param>
        /// <returns>GetUserRunsListResponse</returns>
        GetUserRunsListResponse ActRunsGet(string actorId, double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), string? status = default(string?));

        /// <summary>
        /// Get list of runs
        /// </summary>
        /// <remarks>
        /// Gets the list of runs of a specific Actor. The response is a list of objects, where each object contains basic information about a single Actor run.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 array elements.  By default, the records are sorted by the &#x60;startedAt&#x60; field in ascending order, therefore you can use pagination to incrementally fetch all records while new ones are still being created. To sort the records in descending order, use &#x60;desc&#x3D;1&#x60; parameter. You can also filter runs by status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle)). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="status">Return only runs with the provided status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle))  (optional)</param>
        /// <returns>ApiResponse of GetUserRunsListResponse</returns>
        ApiResponse<GetUserRunsListResponse> ActRunsGetWithHttpInfo(string actorId, double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), string? status = default(string?));
        /// <summary>
        /// Get last run
        /// </summary>
        /// <remarks>
        /// This is not a single endpoint, but an entire group of endpoints that lets you to retrieve and manage the last run of given Actor or any of its default storages. All the endpoints require an authentication token.  The endpoints accept the same HTTP methods and query parameters as the respective storage endpoints. The base path represents the last Actor run object is:  &#x60;/v2/acts/{actorId}/runs/last{?token,status}&#x60;  Using the &#x60;status&#x60; query parameter you can ensure to only get a run with a certain status (e.g. &#x60;status&#x3D;SUCCEEDED&#x60;). The output of this endpoint and other query parameters are the same as in the [Run object](#/reference/actors/run-object) endpoint.  In order to access the default storages of the last Actor run, i.e. log, key-value store, dataset and request queue, use the following endpoints:  * &#x60;/v2/acts/{actorId}/runs/last/log{?token,status}&#x60; * &#x60;/v2/acts/{actorId}/runs/last/key-value-store{?token,status}&#x60; * &#x60;/v2/acts/{actorId}/runs/last/dataset{?token,status}&#x60; * &#x60;/v2/acts/{actorId}/runs/last/request-queue{?token,status}&#x60;  These API endpoints have the same usage as the equivalent storage endpoints. For example, &#x60;/v2/acts/{actorId}/runs/last/key-value-store&#x60; has the same HTTP method and parameters as the [Key-value store object](#/reference/key-value-stores/store-object) endpoint.  Additionally, each of the above API endpoints supports all sub-endpoints of the original one:  #### Key-value store  * &#x60;/v2/acts/{actorId}/runs/last/key-value-store/keys{?token,status}&#x60; [Key collection](#/reference/key-value-stores/key-collection) * &#x60;/v2/acts/{actorId}/runs/last/key-value-store/records/{recordKey}{?token,status}&#x60; [Record](#/reference/key-value-stores/record)  #### Dataset  * &#x60;/v2/acts/{actorId}/runs/last/dataset/items{?token,status}&#x60; [Item collection](#/reference/datasets/item-collection)  #### Request queue  * &#x60;/v2/acts/{actorId}/runs/last/request-queue/requests{?token,status}&#x60; [Request collection](#/reference/request-queues/request-collection) * &#x60;/v2/acts/{actorId}/runs/last/request-queue/requests/{requestId}{?token,status}&#x60; [Request collection](#/reference/request-queues/request) * &#x60;/v2/acts/{actorId}/runs/last/request-queue/head{?token,status}&#x60; [Queue head](#/reference/request-queues/queue-head)  For example, to download data from a dataset of the last succeeded Actor run in XML format, send HTTP GET request to the following URL:  &#x60;&#x60;&#x60; https://api.apify.com/v2/acts/{actorId}/runs/last/dataset/items?token&#x3D;{yourApiToken}&amp;format&#x3D;xml&amp;status&#x3D;SUCCEEDED &#x60;&#x60;&#x60;  In order to save new items to the dataset, send HTTP POST request with JSON payload to the same URL. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="status">Filter for the run status. (optional)</param>
        /// <returns>RunResponse</returns>
        RunResponse ActRunsLastGet(string actorId, string? status = default(string?));

        /// <summary>
        /// Get last run
        /// </summary>
        /// <remarks>
        /// This is not a single endpoint, but an entire group of endpoints that lets you to retrieve and manage the last run of given Actor or any of its default storages. All the endpoints require an authentication token.  The endpoints accept the same HTTP methods and query parameters as the respective storage endpoints. The base path represents the last Actor run object is:  &#x60;/v2/acts/{actorId}/runs/last{?token,status}&#x60;  Using the &#x60;status&#x60; query parameter you can ensure to only get a run with a certain status (e.g. &#x60;status&#x3D;SUCCEEDED&#x60;). The output of this endpoint and other query parameters are the same as in the [Run object](#/reference/actors/run-object) endpoint.  In order to access the default storages of the last Actor run, i.e. log, key-value store, dataset and request queue, use the following endpoints:  * &#x60;/v2/acts/{actorId}/runs/last/log{?token,status}&#x60; * &#x60;/v2/acts/{actorId}/runs/last/key-value-store{?token,status}&#x60; * &#x60;/v2/acts/{actorId}/runs/last/dataset{?token,status}&#x60; * &#x60;/v2/acts/{actorId}/runs/last/request-queue{?token,status}&#x60;  These API endpoints have the same usage as the equivalent storage endpoints. For example, &#x60;/v2/acts/{actorId}/runs/last/key-value-store&#x60; has the same HTTP method and parameters as the [Key-value store object](#/reference/key-value-stores/store-object) endpoint.  Additionally, each of the above API endpoints supports all sub-endpoints of the original one:  #### Key-value store  * &#x60;/v2/acts/{actorId}/runs/last/key-value-store/keys{?token,status}&#x60; [Key collection](#/reference/key-value-stores/key-collection) * &#x60;/v2/acts/{actorId}/runs/last/key-value-store/records/{recordKey}{?token,status}&#x60; [Record](#/reference/key-value-stores/record)  #### Dataset  * &#x60;/v2/acts/{actorId}/runs/last/dataset/items{?token,status}&#x60; [Item collection](#/reference/datasets/item-collection)  #### Request queue  * &#x60;/v2/acts/{actorId}/runs/last/request-queue/requests{?token,status}&#x60; [Request collection](#/reference/request-queues/request-collection) * &#x60;/v2/acts/{actorId}/runs/last/request-queue/requests/{requestId}{?token,status}&#x60; [Request collection](#/reference/request-queues/request) * &#x60;/v2/acts/{actorId}/runs/last/request-queue/head{?token,status}&#x60; [Queue head](#/reference/request-queues/queue-head)  For example, to download data from a dataset of the last succeeded Actor run in XML format, send HTTP GET request to the following URL:  &#x60;&#x60;&#x60; https://api.apify.com/v2/acts/{actorId}/runs/last/dataset/items?token&#x3D;{yourApiToken}&amp;format&#x3D;xml&amp;status&#x3D;SUCCEEDED &#x60;&#x60;&#x60;  In order to save new items to the dataset, send HTTP POST request with JSON payload to the same URL. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="status">Filter for the run status. (optional)</param>
        /// <returns>ApiResponse of RunResponse</returns>
        ApiResponse<RunResponse> ActRunsLastGetWithHttpInfo(string actorId, string? status = default(string?));
        /// <summary>
        /// Run Actor
        /// </summary>
        /// <remarks>
        /// Runs an Actor and immediately returns without waiting for the run to finish.  The POST payload including its &#x60;Content-Type&#x60; header is passed as &#x60;INPUT&#x60; to the Actor (usually &#x60;application/json&#x60;).  The Actor is started with the default options; you can override them using various URL query parameters.  The response is the Run object as returned by the [Get run](#/reference/actor-runs/run-object-and-its-storages/get-run) API endpoint.  If you want to wait for the run to finish and receive the actual output of the Actor as the response, please use one of the [Run Actor synchronously](#/reference/actors/run-actor-synchronously) API endpoints instead.  To fetch the Actor run results that are typically stored in the default dataset, you&#39;ll need to pass the ID received in the &#x60;defaultDatasetId&#x60; field received in the response JSON to the [Get items](#/reference/datasets/item-collection/get-items) API endpoint. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default, it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <returns>RunResponse</returns>
        RunResponse ActRunsPost(string actorId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), double? waitForFinish = default(double?), string? webhooks = default(string?));

        /// <summary>
        /// Run Actor
        /// </summary>
        /// <remarks>
        /// Runs an Actor and immediately returns without waiting for the run to finish.  The POST payload including its &#x60;Content-Type&#x60; header is passed as &#x60;INPUT&#x60; to the Actor (usually &#x60;application/json&#x60;).  The Actor is started with the default options; you can override them using various URL query parameters.  The response is the Run object as returned by the [Get run](#/reference/actor-runs/run-object-and-its-storages/get-run) API endpoint.  If you want to wait for the run to finish and receive the actual output of the Actor as the response, please use one of the [Run Actor synchronously](#/reference/actors/run-actor-synchronously) API endpoints instead.  To fetch the Actor run results that are typically stored in the default dataset, you&#39;ll need to pass the ID received in the &#x60;defaultDatasetId&#x60; field received in the response JSON to the [Get items](#/reference/datasets/item-collection/get-items) API endpoint. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default, it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <returns>ApiResponse of RunResponse</returns>
        ApiResponse<RunResponse> ActRunsPostWithHttpInfo(string actorId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), double? waitForFinish = default(double?), string? webhooks = default(string?));
        #endregion Synchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IActorsActorRunsApiAsync : IApiAccessor
    {
        #region Asynchronous Operations
        /// <summary>
        /// Abort run
        /// </summary>
        /// <remarks>
        /// **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs). Aborts an Actor run and returns an object that contains all the details about the run.  Only runs that are starting or running are aborted. For runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTING&#x60; and &#x60;TIMED-OUT&#x60; this call does nothing. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Run ID.</param>
        /// <param name="gracefully">If true passed, the Actor run will abort gracefully. It will send &#x60;aborting&#x60; and &#x60;persistState&#x60; event into run and force-stop the run after 30 seconds. It is helpful in cases where you plan to resurrect the run later.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of RunResponse</returns>
        [Obsolete]
        System.Threading.Tasks.Task<RunResponse> ActRunAbortPostAsync(string actorId, string runId, bool? gracefully = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Abort run
        /// </summary>
        /// <remarks>
        /// **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs). Aborts an Actor run and returns an object that contains all the details about the run.  Only runs that are starting or running are aborted. For runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTING&#x60; and &#x60;TIMED-OUT&#x60; this call does nothing. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Run ID.</param>
        /// <param name="gracefully">If true passed, the Actor run will abort gracefully. It will send &#x60;aborting&#x60; and &#x60;persistState&#x60; event into run and force-stop the run after 30 seconds. It is helpful in cases where you plan to resurrect the run later.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (RunResponse)</returns>
        [Obsolete]
        System.Threading.Tasks.Task<ApiResponse<RunResponse>> ActRunAbortPostWithHttpInfoAsync(string actorId, string runId, bool? gracefully = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Get run
        /// </summary>
        /// <remarks>
        /// **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs).  Gets an object that contains all the details about a specific run of an Actor.  By passing the optional &#x60;waitForFinish&#x60; parameter the API endpoint will synchronously wait for the run to finish. This is useful to avoid periodic polling when waiting for Actor run to complete.  This endpoint does not require the authentication token. Instead, calls are authenticated using a hard-to-guess ID of the run. However, if you access the endpoint without the token, certain attributes, such as &#x60;usageUsd&#x60; and &#x60;usageTotalUsd&#x60;, will be hidden. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Run ID.</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of RunResponse</returns>
        [Obsolete]
        System.Threading.Tasks.Task<RunResponse> ActRunGetAsync(string actorId, string runId, double? waitForFinish = default(double?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Get run
        /// </summary>
        /// <remarks>
        /// **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs).  Gets an object that contains all the details about a specific run of an Actor.  By passing the optional &#x60;waitForFinish&#x60; parameter the API endpoint will synchronously wait for the run to finish. This is useful to avoid periodic polling when waiting for Actor run to complete.  This endpoint does not require the authentication token. Instead, calls are authenticated using a hard-to-guess ID of the run. However, if you access the endpoint without the token, certain attributes, such as &#x60;usageUsd&#x60; and &#x60;usageTotalUsd&#x60;, will be hidden. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Run ID.</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (RunResponse)</returns>
        [Obsolete]
        System.Threading.Tasks.Task<ApiResponse<RunResponse>> ActRunGetWithHttpInfoAsync(string actorId, string runId, double? waitForFinish = default(double?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Metamorph run
        /// </summary>
        /// <remarks>
        /// **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs).Transforms an Actor run into a run of another Actor with a new input.  This is useful if you want to use another Actor to finish the work of your current Actor run, without the need to create a completely new run and waiting for its finish. For the users of your Actors, the metamorph operation is transparent, they will just see your Actor got the work done.  There is a limit on how many times you can metamorph a single run. You can check the limit in [the Actor runtime limits](https://docs.apify.com/platform/limits#actor-limits).  Internally, the system stops the Docker container corresponding to the Actor run and starts a new container using a different Docker image. All the default storages are preserved and the new input is stored under the &#x60;INPUT-METAMORPH-1&#x60; key in the same default key-value store.  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/development/programming-interface/metamorph). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Actor run ID.</param>
        /// <param name="targetActorId">ID of a target Actor that the run should be transformed into.</param>
        /// <param name="build">Optional build of the target Actor.  It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the target Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of RunResponse</returns>
        [Obsolete]
        System.Threading.Tasks.Task<RunResponse> ActRunMetamorphPostAsync(string actorId, string runId, string targetActorId, string? build = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Metamorph run
        /// </summary>
        /// <remarks>
        /// **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs).Transforms an Actor run into a run of another Actor with a new input.  This is useful if you want to use another Actor to finish the work of your current Actor run, without the need to create a completely new run and waiting for its finish. For the users of your Actors, the metamorph operation is transparent, they will just see your Actor got the work done.  There is a limit on how many times you can metamorph a single run. You can check the limit in [the Actor runtime limits](https://docs.apify.com/platform/limits#actor-limits).  Internally, the system stops the Docker container corresponding to the Actor run and starts a new container using a different Docker image. All the default storages are preserved and the new input is stored under the &#x60;INPUT-METAMORPH-1&#x60; key in the same default key-value store.  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/development/programming-interface/metamorph). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Actor run ID.</param>
        /// <param name="targetActorId">ID of a target Actor that the run should be transformed into.</param>
        /// <param name="build">Optional build of the target Actor.  It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the target Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (RunResponse)</returns>
        [Obsolete]
        System.Threading.Tasks.Task<ApiResponse<RunResponse>> ActRunMetamorphPostWithHttpInfoAsync(string actorId, string runId, string targetActorId, string? build = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Resurrect run
        /// </summary>
        /// <remarks>
        /// **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs).Resurrects a finished Actor run and returns an object that contains all the details about the resurrected run.  Only finished runs, i.e. runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTED&#x60; and &#x60;TIMED-OUT&#x60; can be resurrected. Run status will be updated to RUNNING and its container will be restarted with the same storages (the same behaviour as when the run gets migrated to the new server).  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/running/runs-and-builds#resurrection-of-finished-run). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Run ID.</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the run that is being resurrected (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the run that is being resurrected.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the run that is being resurrected.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of RunResponse</returns>
        System.Threading.Tasks.Task<RunResponse> ActRunResurrectPostAsync(string actorId, string runId, string? build = default(string?), double? timeout = default(double?), double? memory = default(double?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Resurrect run
        /// </summary>
        /// <remarks>
        /// **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs).Resurrects a finished Actor run and returns an object that contains all the details about the resurrected run.  Only finished runs, i.e. runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTED&#x60; and &#x60;TIMED-OUT&#x60; can be resurrected. Run status will be updated to RUNNING and its container will be restarted with the same storages (the same behaviour as when the run gets migrated to the new server).  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/running/runs-and-builds#resurrection-of-finished-run). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Run ID.</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the run that is being resurrected (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the run that is being resurrected.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the run that is being resurrected.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (RunResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<RunResponse>> ActRunResurrectPostWithHttpInfoAsync(string actorId, string runId, string? build = default(string?), double? timeout = default(double?), double? memory = default(double?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Without input
        /// </summary>
        /// <remarks>
        /// Runs a specific Actor and returns its output. The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the API endpoint returns a timeout error. The Actor is not passed any input.  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        System.Threading.Tasks.Task<Object> ActRunSyncGetAsync(string actorId, string? outputRecordKey = default(string?), double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Without input
        /// </summary>
        /// <remarks>
        /// Runs a specific Actor and returns its output. The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the API endpoint returns a timeout error. The Actor is not passed any input.  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> ActRunSyncGetWithHttpInfoAsync(string actorId, string? outputRecordKey = default(string?), double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Run Actor synchronously without input and get dataset items
        /// </summary>
        /// <remarks>
        /// Runs a specific Actor and returns its dataset items. The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the API endpoint returns a timeout error. The Actor is not passed any input.  It allows to send all possible options in parameters from [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures. For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;. The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not. If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output. This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        System.Threading.Tasks.Task<Object> ActRunSyncGetDatasetItemsGetAsync(string actorId, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Run Actor synchronously without input and get dataset items
        /// </summary>
        /// <remarks>
        /// Runs a specific Actor and returns its dataset items. The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the API endpoint returns a timeout error. The Actor is not passed any input.  It allows to send all possible options in parameters from [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures. For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;. The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not. If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output. This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> ActRunSyncGetDatasetItemsGetWithHttpInfoAsync(string actorId, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Run Actor synchronously with input and get dataset items
        /// </summary>
        /// <remarks>
        /// Runs a specific Actor and returns its dataset items.  The POST payload including its &#x60;Content-Type&#x60; header is passed as &#x60;INPUT&#x60; to the Actor (usually &#x60;application/json&#x60;). The HTTP response contains the Actors dataset items, while the format of items depends on specifying dataset items&#39; &#x60;format&#x60; parameter.  You can send all the same options in parameters as the [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  The Actor is started with the default options; you can override them using URL query parameters. If the Actor run exceeds 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds, the HTTP response will return the 408 status code (Request Timeout).  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object. If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures. For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;. The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not. If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output. This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        System.Threading.Tasks.Task<Object> ActRunSyncGetDatasetItemsPostAsync(string actorId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Run Actor synchronously with input and get dataset items
        /// </summary>
        /// <remarks>
        /// Runs a specific Actor and returns its dataset items.  The POST payload including its &#x60;Content-Type&#x60; header is passed as &#x60;INPUT&#x60; to the Actor (usually &#x60;application/json&#x60;). The HTTP response contains the Actors dataset items, while the format of items depends on specifying dataset items&#39; &#x60;format&#x60; parameter.  You can send all the same options in parameters as the [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  The Actor is started with the default options; you can override them using URL query parameters. If the Actor run exceeds 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds, the HTTP response will return the 408 status code (Request Timeout).  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object. If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures. For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;. The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not. If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output. This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> ActRunSyncGetDatasetItemsPostWithHttpInfoAsync(string actorId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Run Actor synchronously with input and return output
        /// </summary>
        /// <remarks>
        /// Runs a specific Actor and returns its output.  The POST payload including its &#x60;Content-Type&#x60; header is passed as &#x60;INPUT&#x60; to the Actor (usually &lt;code&gt;application/json&lt;/code&gt;). The HTTP response contains Actors &#x60;OUTPUT&#x60; record from its default key-value store.  The Actor is started with the default options; you can override them using various URL query parameters. If the Actor run exceeds 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds, the HTTP response will have status 408 (Request Timeout).  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="body"></param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        System.Threading.Tasks.Task<Object> ActRunSyncPostAsync(string actorId, Object body, string? outputRecordKey = default(string?), double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Run Actor synchronously with input and return output
        /// </summary>
        /// <remarks>
        /// Runs a specific Actor and returns its output.  The POST payload including its &#x60;Content-Type&#x60; header is passed as &#x60;INPUT&#x60; to the Actor (usually &lt;code&gt;application/json&lt;/code&gt;). The HTTP response contains Actors &#x60;OUTPUT&#x60; record from its default key-value store.  The Actor is started with the default options; you can override them using various URL query parameters. If the Actor run exceeds 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds, the HTTP response will have status 408 (Request Timeout).  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="body"></param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> ActRunSyncPostWithHttpInfoAsync(string actorId, Object body, string? outputRecordKey = default(string?), double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Get list of runs
        /// </summary>
        /// <remarks>
        /// Gets the list of runs of a specific Actor. The response is a list of objects, where each object contains basic information about a single Actor run.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 array elements.  By default, the records are sorted by the &#x60;startedAt&#x60; field in ascending order, therefore you can use pagination to incrementally fetch all records while new ones are still being created. To sort the records in descending order, use &#x60;desc&#x3D;1&#x60; parameter. You can also filter runs by status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle)). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="status">Return only runs with the provided status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle))  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of GetUserRunsListResponse</returns>
        System.Threading.Tasks.Task<GetUserRunsListResponse> ActRunsGetAsync(string actorId, double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), string? status = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Get list of runs
        /// </summary>
        /// <remarks>
        /// Gets the list of runs of a specific Actor. The response is a list of objects, where each object contains basic information about a single Actor run.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 array elements.  By default, the records are sorted by the &#x60;startedAt&#x60; field in ascending order, therefore you can use pagination to incrementally fetch all records while new ones are still being created. To sort the records in descending order, use &#x60;desc&#x3D;1&#x60; parameter. You can also filter runs by status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle)). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="status">Return only runs with the provided status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle))  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (GetUserRunsListResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<GetUserRunsListResponse>> ActRunsGetWithHttpInfoAsync(string actorId, double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), string? status = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Get last run
        /// </summary>
        /// <remarks>
        /// This is not a single endpoint, but an entire group of endpoints that lets you to retrieve and manage the last run of given Actor or any of its default storages. All the endpoints require an authentication token.  The endpoints accept the same HTTP methods and query parameters as the respective storage endpoints. The base path represents the last Actor run object is:  &#x60;/v2/acts/{actorId}/runs/last{?token,status}&#x60;  Using the &#x60;status&#x60; query parameter you can ensure to only get a run with a certain status (e.g. &#x60;status&#x3D;SUCCEEDED&#x60;). The output of this endpoint and other query parameters are the same as in the [Run object](#/reference/actors/run-object) endpoint.  In order to access the default storages of the last Actor run, i.e. log, key-value store, dataset and request queue, use the following endpoints:  * &#x60;/v2/acts/{actorId}/runs/last/log{?token,status}&#x60; * &#x60;/v2/acts/{actorId}/runs/last/key-value-store{?token,status}&#x60; * &#x60;/v2/acts/{actorId}/runs/last/dataset{?token,status}&#x60; * &#x60;/v2/acts/{actorId}/runs/last/request-queue{?token,status}&#x60;  These API endpoints have the same usage as the equivalent storage endpoints. For example, &#x60;/v2/acts/{actorId}/runs/last/key-value-store&#x60; has the same HTTP method and parameters as the [Key-value store object](#/reference/key-value-stores/store-object) endpoint.  Additionally, each of the above API endpoints supports all sub-endpoints of the original one:  #### Key-value store  * &#x60;/v2/acts/{actorId}/runs/last/key-value-store/keys{?token,status}&#x60; [Key collection](#/reference/key-value-stores/key-collection) * &#x60;/v2/acts/{actorId}/runs/last/key-value-store/records/{recordKey}{?token,status}&#x60; [Record](#/reference/key-value-stores/record)  #### Dataset  * &#x60;/v2/acts/{actorId}/runs/last/dataset/items{?token,status}&#x60; [Item collection](#/reference/datasets/item-collection)  #### Request queue  * &#x60;/v2/acts/{actorId}/runs/last/request-queue/requests{?token,status}&#x60; [Request collection](#/reference/request-queues/request-collection) * &#x60;/v2/acts/{actorId}/runs/last/request-queue/requests/{requestId}{?token,status}&#x60; [Request collection](#/reference/request-queues/request) * &#x60;/v2/acts/{actorId}/runs/last/request-queue/head{?token,status}&#x60; [Queue head](#/reference/request-queues/queue-head)  For example, to download data from a dataset of the last succeeded Actor run in XML format, send HTTP GET request to the following URL:  &#x60;&#x60;&#x60; https://api.apify.com/v2/acts/{actorId}/runs/last/dataset/items?token&#x3D;{yourApiToken}&amp;format&#x3D;xml&amp;status&#x3D;SUCCEEDED &#x60;&#x60;&#x60;  In order to save new items to the dataset, send HTTP POST request with JSON payload to the same URL. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="status">Filter for the run status. (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of RunResponse</returns>
        System.Threading.Tasks.Task<RunResponse> ActRunsLastGetAsync(string actorId, string? status = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Get last run
        /// </summary>
        /// <remarks>
        /// This is not a single endpoint, but an entire group of endpoints that lets you to retrieve and manage the last run of given Actor or any of its default storages. All the endpoints require an authentication token.  The endpoints accept the same HTTP methods and query parameters as the respective storage endpoints. The base path represents the last Actor run object is:  &#x60;/v2/acts/{actorId}/runs/last{?token,status}&#x60;  Using the &#x60;status&#x60; query parameter you can ensure to only get a run with a certain status (e.g. &#x60;status&#x3D;SUCCEEDED&#x60;). The output of this endpoint and other query parameters are the same as in the [Run object](#/reference/actors/run-object) endpoint.  In order to access the default storages of the last Actor run, i.e. log, key-value store, dataset and request queue, use the following endpoints:  * &#x60;/v2/acts/{actorId}/runs/last/log{?token,status}&#x60; * &#x60;/v2/acts/{actorId}/runs/last/key-value-store{?token,status}&#x60; * &#x60;/v2/acts/{actorId}/runs/last/dataset{?token,status}&#x60; * &#x60;/v2/acts/{actorId}/runs/last/request-queue{?token,status}&#x60;  These API endpoints have the same usage as the equivalent storage endpoints. For example, &#x60;/v2/acts/{actorId}/runs/last/key-value-store&#x60; has the same HTTP method and parameters as the [Key-value store object](#/reference/key-value-stores/store-object) endpoint.  Additionally, each of the above API endpoints supports all sub-endpoints of the original one:  #### Key-value store  * &#x60;/v2/acts/{actorId}/runs/last/key-value-store/keys{?token,status}&#x60; [Key collection](#/reference/key-value-stores/key-collection) * &#x60;/v2/acts/{actorId}/runs/last/key-value-store/records/{recordKey}{?token,status}&#x60; [Record](#/reference/key-value-stores/record)  #### Dataset  * &#x60;/v2/acts/{actorId}/runs/last/dataset/items{?token,status}&#x60; [Item collection](#/reference/datasets/item-collection)  #### Request queue  * &#x60;/v2/acts/{actorId}/runs/last/request-queue/requests{?token,status}&#x60; [Request collection](#/reference/request-queues/request-collection) * &#x60;/v2/acts/{actorId}/runs/last/request-queue/requests/{requestId}{?token,status}&#x60; [Request collection](#/reference/request-queues/request) * &#x60;/v2/acts/{actorId}/runs/last/request-queue/head{?token,status}&#x60; [Queue head](#/reference/request-queues/queue-head)  For example, to download data from a dataset of the last succeeded Actor run in XML format, send HTTP GET request to the following URL:  &#x60;&#x60;&#x60; https://api.apify.com/v2/acts/{actorId}/runs/last/dataset/items?token&#x3D;{yourApiToken}&amp;format&#x3D;xml&amp;status&#x3D;SUCCEEDED &#x60;&#x60;&#x60;  In order to save new items to the dataset, send HTTP POST request with JSON payload to the same URL. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="status">Filter for the run status. (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (RunResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<RunResponse>> ActRunsLastGetWithHttpInfoAsync(string actorId, string? status = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Run Actor
        /// </summary>
        /// <remarks>
        /// Runs an Actor and immediately returns without waiting for the run to finish.  The POST payload including its &#x60;Content-Type&#x60; header is passed as &#x60;INPUT&#x60; to the Actor (usually &#x60;application/json&#x60;).  The Actor is started with the default options; you can override them using various URL query parameters.  The response is the Run object as returned by the [Get run](#/reference/actor-runs/run-object-and-its-storages/get-run) API endpoint.  If you want to wait for the run to finish and receive the actual output of the Actor as the response, please use one of the [Run Actor synchronously](#/reference/actors/run-actor-synchronously) API endpoints instead.  To fetch the Actor run results that are typically stored in the default dataset, you&#39;ll need to pass the ID received in the &#x60;defaultDatasetId&#x60; field received in the response JSON to the [Get items](#/reference/datasets/item-collection/get-items) API endpoint. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default, it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of RunResponse</returns>
        System.Threading.Tasks.Task<RunResponse> ActRunsPostAsync(string actorId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), double? waitForFinish = default(double?), string? webhooks = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Run Actor
        /// </summary>
        /// <remarks>
        /// Runs an Actor and immediately returns without waiting for the run to finish.  The POST payload including its &#x60;Content-Type&#x60; header is passed as &#x60;INPUT&#x60; to the Actor (usually &#x60;application/json&#x60;).  The Actor is started with the default options; you can override them using various URL query parameters.  The response is the Run object as returned by the [Get run](#/reference/actor-runs/run-object-and-its-storages/get-run) API endpoint.  If you want to wait for the run to finish and receive the actual output of the Actor as the response, please use one of the [Run Actor synchronously](#/reference/actors/run-actor-synchronously) API endpoints instead.  To fetch the Actor run results that are typically stored in the default dataset, you&#39;ll need to pass the ID received in the &#x60;defaultDatasetId&#x60; field received in the response JSON to the [Get items](#/reference/datasets/item-collection/get-items) API endpoint. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default, it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (RunResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<RunResponse>> ActRunsPostWithHttpInfoAsync(string actorId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), double? waitForFinish = default(double?), string? webhooks = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        #endregion Asynchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IActorsActorRunsApi : IActorsActorRunsApiSync, IActorsActorRunsApiAsync
    {

    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public partial class ActorsActorRunsApi : IDisposable, IActorsActorRunsApi
    {
        private Apify.SDK.Client.ExceptionFactory _exceptionFactory = (name, response) => null;

        /// <summary>
        /// Initializes a new instance of the <see cref="ActorsActorRunsApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <returns></returns>
        public ActorsActorRunsApi() : this((string)null)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ActorsActorRunsApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        public ActorsActorRunsApi(string basePath)
        {
            this.Configuration = Apify.SDK.Client.Configuration.MergeConfigurations(
                Apify.SDK.Client.GlobalConfiguration.Instance,
                new Apify.SDK.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Apify.SDK.Client.ApiClient(this.Configuration.BasePath);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Apify.SDK.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ActorsActorRunsApi"/> class using Configuration object.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        public ActorsActorRunsApi(Apify.SDK.Client.Configuration configuration)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Configuration = Apify.SDK.Client.Configuration.MergeConfigurations(
                Apify.SDK.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Apify.SDK.Client.ApiClient(this.Configuration.BasePath);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Apify.SDK.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ActorsActorRunsApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public ActorsActorRunsApi(HttpClient client, HttpClientHandler handler = null) : this(client, (string)null, handler)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ActorsActorRunsApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public ActorsActorRunsApi(HttpClient client, string basePath, HttpClientHandler handler = null)
        {
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Apify.SDK.Client.Configuration.MergeConfigurations(
                Apify.SDK.Client.GlobalConfiguration.Instance,
                new Apify.SDK.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Apify.SDK.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Apify.SDK.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ActorsActorRunsApi"/> class using Configuration object.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public ActorsActorRunsApi(HttpClient client, Apify.SDK.Client.Configuration configuration, HttpClientHandler handler = null)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Apify.SDK.Client.Configuration.MergeConfigurations(
                Apify.SDK.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Apify.SDK.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Apify.SDK.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ActorsActorRunsApi"/> class
        /// using a Configuration object and client instance.
        /// </summary>
        /// <param name="client">The client interface for synchronous API access.</param>
        /// <param name="asyncClient">The client interface for asynchronous API access.</param>
        /// <param name="configuration">The configuration object.</param>
        /// <exception cref="ArgumentNullException"></exception>
        public ActorsActorRunsApi(Apify.SDK.Client.ISynchronousClient client, Apify.SDK.Client.IAsynchronousClient asyncClient, Apify.SDK.Client.IReadableConfiguration configuration)
        {
            if (client == null) throw new ArgumentNullException("client");
            if (asyncClient == null) throw new ArgumentNullException("asyncClient");
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Client = client;
            this.AsynchronousClient = asyncClient;
            this.Configuration = configuration;
            this.ExceptionFactory = Apify.SDK.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Disposes resources if they were created by us
        /// </summary>
        public void Dispose()
        {
            this.ApiClient?.Dispose();
        }

        /// <summary>
        /// Holds the ApiClient if created
        /// </summary>
        public Apify.SDK.Client.ApiClient ApiClient { get; set; } = null;

        /// <summary>
        /// The client for accessing this underlying API asynchronously.
        /// </summary>
        public Apify.SDK.Client.IAsynchronousClient AsynchronousClient { get; set; }

        /// <summary>
        /// The client for accessing this underlying API synchronously.
        /// </summary>
        public Apify.SDK.Client.ISynchronousClient Client { get; set; }

        /// <summary>
        /// Gets the base path of the API client.
        /// </summary>
        /// <value>The base path</value>
        public string GetBasePath()
        {
            return this.Configuration.BasePath;
        }

        /// <summary>
        /// Gets or sets the configuration object
        /// </summary>
        /// <value>An instance of the Configuration</value>
        public Apify.SDK.Client.IReadableConfiguration Configuration { get; set; }

        /// <summary>
        /// Provides a factory method hook for the creation of exceptions.
        /// </summary>
        public Apify.SDK.Client.ExceptionFactory ExceptionFactory
        {
            get
            {
                if (_exceptionFactory != null && _exceptionFactory.GetInvocationList().Length > 1)
                {
                    throw new InvalidOperationException("Multicast delegate for ExceptionFactory is unsupported.");
                }
                return _exceptionFactory;
            }
            set { _exceptionFactory = value; }
        }

        /// <summary>
        /// Abort run **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs). Aborts an Actor run and returns an object that contains all the details about the run.  Only runs that are starting or running are aborted. For runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTING&#x60; and &#x60;TIMED-OUT&#x60; this call does nothing. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Run ID.</param>
        /// <param name="gracefully">If true passed, the Actor run will abort gracefully. It will send &#x60;aborting&#x60; and &#x60;persistState&#x60; event into run and force-stop the run after 30 seconds. It is helpful in cases where you plan to resurrect the run later.  (optional)</param>
        /// <returns>RunResponse</returns>
        [Obsolete]
        public RunResponse ActRunAbortPost(string actorId, string runId, bool? gracefully = default(bool?))
        {
            Apify.SDK.Client.ApiResponse<RunResponse> localVarResponse = ActRunAbortPostWithHttpInfo(actorId, runId, gracefully);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Abort run **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs). Aborts an Actor run and returns an object that contains all the details about the run.  Only runs that are starting or running are aborted. For runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTING&#x60; and &#x60;TIMED-OUT&#x60; this call does nothing. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Run ID.</param>
        /// <param name="gracefully">If true passed, the Actor run will abort gracefully. It will send &#x60;aborting&#x60; and &#x60;persistState&#x60; event into run and force-stop the run after 30 seconds. It is helpful in cases where you plan to resurrect the run later.  (optional)</param>
        /// <returns>ApiResponse of RunResponse</returns>
        [Obsolete]
        public Apify.SDK.Client.ApiResponse<RunResponse> ActRunAbortPostWithHttpInfo(string actorId, string runId, bool? gracefully = default(bool?))
        {
            // verify the required parameter 'actorId' is set
            if (actorId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorId' when calling ActorsActorRunsApi->ActRunAbortPost");

            // verify the required parameter 'runId' is set
            if (runId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'runId' when calling ActorsActorRunsApi->ActRunAbortPost");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorId", Apify.SDK.Client.ClientUtils.ParameterToString(actorId)); // path parameter
            localVarRequestOptions.PathParameters.Add("runId", Apify.SDK.Client.ClientUtils.ParameterToString(runId)); // path parameter
            if (gracefully != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "gracefully", gracefully));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<RunResponse>("/v2/acts/{actorId}/runs/{runId}/abort", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActRunAbortPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Abort run **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs). Aborts an Actor run and returns an object that contains all the details about the run.  Only runs that are starting or running are aborted. For runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTING&#x60; and &#x60;TIMED-OUT&#x60; this call does nothing. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Run ID.</param>
        /// <param name="gracefully">If true passed, the Actor run will abort gracefully. It will send &#x60;aborting&#x60; and &#x60;persistState&#x60; event into run and force-stop the run after 30 seconds. It is helpful in cases where you plan to resurrect the run later.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of RunResponse</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<RunResponse> ActRunAbortPostAsync(string actorId, string runId, bool? gracefully = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<RunResponse> localVarResponse = await ActRunAbortPostWithHttpInfoAsync(actorId, runId, gracefully, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Abort run **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs). Aborts an Actor run and returns an object that contains all the details about the run.  Only runs that are starting or running are aborted. For runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTING&#x60; and &#x60;TIMED-OUT&#x60; this call does nothing. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Run ID.</param>
        /// <param name="gracefully">If true passed, the Actor run will abort gracefully. It will send &#x60;aborting&#x60; and &#x60;persistState&#x60; event into run and force-stop the run after 30 seconds. It is helpful in cases where you plan to resurrect the run later.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (RunResponse)</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<RunResponse>> ActRunAbortPostWithHttpInfoAsync(string actorId, string runId, bool? gracefully = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'actorId' is set
            if (actorId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorId' when calling ActorsActorRunsApi->ActRunAbortPost");

            // verify the required parameter 'runId' is set
            if (runId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'runId' when calling ActorsActorRunsApi->ActRunAbortPost");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorId", Apify.SDK.Client.ClientUtils.ParameterToString(actorId)); // path parameter
            localVarRequestOptions.PathParameters.Add("runId", Apify.SDK.Client.ClientUtils.ParameterToString(runId)); // path parameter
            if (gracefully != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "gracefully", gracefully));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<RunResponse>("/v2/acts/{actorId}/runs/{runId}/abort", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActRunAbortPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get run **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs).  Gets an object that contains all the details about a specific run of an Actor.  By passing the optional &#x60;waitForFinish&#x60; parameter the API endpoint will synchronously wait for the run to finish. This is useful to avoid periodic polling when waiting for Actor run to complete.  This endpoint does not require the authentication token. Instead, calls are authenticated using a hard-to-guess ID of the run. However, if you access the endpoint without the token, certain attributes, such as &#x60;usageUsd&#x60; and &#x60;usageTotalUsd&#x60;, will be hidden. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Run ID.</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <returns>RunResponse</returns>
        [Obsolete]
        public RunResponse ActRunGet(string actorId, string runId, double? waitForFinish = default(double?))
        {
            Apify.SDK.Client.ApiResponse<RunResponse> localVarResponse = ActRunGetWithHttpInfo(actorId, runId, waitForFinish);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get run **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs).  Gets an object that contains all the details about a specific run of an Actor.  By passing the optional &#x60;waitForFinish&#x60; parameter the API endpoint will synchronously wait for the run to finish. This is useful to avoid periodic polling when waiting for Actor run to complete.  This endpoint does not require the authentication token. Instead, calls are authenticated using a hard-to-guess ID of the run. However, if you access the endpoint without the token, certain attributes, such as &#x60;usageUsd&#x60; and &#x60;usageTotalUsd&#x60;, will be hidden. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Run ID.</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <returns>ApiResponse of RunResponse</returns>
        [Obsolete]
        public Apify.SDK.Client.ApiResponse<RunResponse> ActRunGetWithHttpInfo(string actorId, string runId, double? waitForFinish = default(double?))
        {
            // verify the required parameter 'actorId' is set
            if (actorId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorId' when calling ActorsActorRunsApi->ActRunGet");

            // verify the required parameter 'runId' is set
            if (runId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'runId' when calling ActorsActorRunsApi->ActRunGet");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorId", Apify.SDK.Client.ClientUtils.ParameterToString(actorId)); // path parameter
            localVarRequestOptions.PathParameters.Add("runId", Apify.SDK.Client.ClientUtils.ParameterToString(runId)); // path parameter
            if (waitForFinish != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "waitForFinish", waitForFinish));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<RunResponse>("/v2/acts/{actorId}/runs/{runId}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActRunGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get run **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs).  Gets an object that contains all the details about a specific run of an Actor.  By passing the optional &#x60;waitForFinish&#x60; parameter the API endpoint will synchronously wait for the run to finish. This is useful to avoid periodic polling when waiting for Actor run to complete.  This endpoint does not require the authentication token. Instead, calls are authenticated using a hard-to-guess ID of the run. However, if you access the endpoint without the token, certain attributes, such as &#x60;usageUsd&#x60; and &#x60;usageTotalUsd&#x60;, will be hidden. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Run ID.</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of RunResponse</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<RunResponse> ActRunGetAsync(string actorId, string runId, double? waitForFinish = default(double?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<RunResponse> localVarResponse = await ActRunGetWithHttpInfoAsync(actorId, runId, waitForFinish, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get run **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs).  Gets an object that contains all the details about a specific run of an Actor.  By passing the optional &#x60;waitForFinish&#x60; parameter the API endpoint will synchronously wait for the run to finish. This is useful to avoid periodic polling when waiting for Actor run to complete.  This endpoint does not require the authentication token. Instead, calls are authenticated using a hard-to-guess ID of the run. However, if you access the endpoint without the token, certain attributes, such as &#x60;usageUsd&#x60; and &#x60;usageTotalUsd&#x60;, will be hidden. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Run ID.</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (RunResponse)</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<RunResponse>> ActRunGetWithHttpInfoAsync(string actorId, string runId, double? waitForFinish = default(double?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'actorId' is set
            if (actorId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorId' when calling ActorsActorRunsApi->ActRunGet");

            // verify the required parameter 'runId' is set
            if (runId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'runId' when calling ActorsActorRunsApi->ActRunGet");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorId", Apify.SDK.Client.ClientUtils.ParameterToString(actorId)); // path parameter
            localVarRequestOptions.PathParameters.Add("runId", Apify.SDK.Client.ClientUtils.ParameterToString(runId)); // path parameter
            if (waitForFinish != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "waitForFinish", waitForFinish));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<RunResponse>("/v2/acts/{actorId}/runs/{runId}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActRunGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Metamorph run **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs).Transforms an Actor run into a run of another Actor with a new input.  This is useful if you want to use another Actor to finish the work of your current Actor run, without the need to create a completely new run and waiting for its finish. For the users of your Actors, the metamorph operation is transparent, they will just see your Actor got the work done.  There is a limit on how many times you can metamorph a single run. You can check the limit in [the Actor runtime limits](https://docs.apify.com/platform/limits#actor-limits).  Internally, the system stops the Docker container corresponding to the Actor run and starts a new container using a different Docker image. All the default storages are preserved and the new input is stored under the &#x60;INPUT-METAMORPH-1&#x60; key in the same default key-value store.  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/development/programming-interface/metamorph). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Actor run ID.</param>
        /// <param name="targetActorId">ID of a target Actor that the run should be transformed into.</param>
        /// <param name="build">Optional build of the target Actor.  It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the target Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <returns>RunResponse</returns>
        [Obsolete]
        public RunResponse ActRunMetamorphPost(string actorId, string runId, string targetActorId, string? build = default(string?))
        {
            Apify.SDK.Client.ApiResponse<RunResponse> localVarResponse = ActRunMetamorphPostWithHttpInfo(actorId, runId, targetActorId, build);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Metamorph run **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs).Transforms an Actor run into a run of another Actor with a new input.  This is useful if you want to use another Actor to finish the work of your current Actor run, without the need to create a completely new run and waiting for its finish. For the users of your Actors, the metamorph operation is transparent, they will just see your Actor got the work done.  There is a limit on how many times you can metamorph a single run. You can check the limit in [the Actor runtime limits](https://docs.apify.com/platform/limits#actor-limits).  Internally, the system stops the Docker container corresponding to the Actor run and starts a new container using a different Docker image. All the default storages are preserved and the new input is stored under the &#x60;INPUT-METAMORPH-1&#x60; key in the same default key-value store.  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/development/programming-interface/metamorph). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Actor run ID.</param>
        /// <param name="targetActorId">ID of a target Actor that the run should be transformed into.</param>
        /// <param name="build">Optional build of the target Actor.  It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the target Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <returns>ApiResponse of RunResponse</returns>
        [Obsolete]
        public Apify.SDK.Client.ApiResponse<RunResponse> ActRunMetamorphPostWithHttpInfo(string actorId, string runId, string targetActorId, string? build = default(string?))
        {
            // verify the required parameter 'actorId' is set
            if (actorId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorId' when calling ActorsActorRunsApi->ActRunMetamorphPost");

            // verify the required parameter 'runId' is set
            if (runId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'runId' when calling ActorsActorRunsApi->ActRunMetamorphPost");

            // verify the required parameter 'targetActorId' is set
            if (targetActorId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'targetActorId' when calling ActorsActorRunsApi->ActRunMetamorphPost");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorId", Apify.SDK.Client.ClientUtils.ParameterToString(actorId)); // path parameter
            localVarRequestOptions.PathParameters.Add("runId", Apify.SDK.Client.ClientUtils.ParameterToString(runId)); // path parameter
            localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "targetActorId", targetActorId));
            if (build != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "build", build));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<RunResponse>("/v2/acts/{actorId}/runs/{runId}/metamorph", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActRunMetamorphPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Metamorph run **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs).Transforms an Actor run into a run of another Actor with a new input.  This is useful if you want to use another Actor to finish the work of your current Actor run, without the need to create a completely new run and waiting for its finish. For the users of your Actors, the metamorph operation is transparent, they will just see your Actor got the work done.  There is a limit on how many times you can metamorph a single run. You can check the limit in [the Actor runtime limits](https://docs.apify.com/platform/limits#actor-limits).  Internally, the system stops the Docker container corresponding to the Actor run and starts a new container using a different Docker image. All the default storages are preserved and the new input is stored under the &#x60;INPUT-METAMORPH-1&#x60; key in the same default key-value store.  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/development/programming-interface/metamorph). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Actor run ID.</param>
        /// <param name="targetActorId">ID of a target Actor that the run should be transformed into.</param>
        /// <param name="build">Optional build of the target Actor.  It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the target Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of RunResponse</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<RunResponse> ActRunMetamorphPostAsync(string actorId, string runId, string targetActorId, string? build = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<RunResponse> localVarResponse = await ActRunMetamorphPostWithHttpInfoAsync(actorId, runId, targetActorId, build, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Metamorph run **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs).Transforms an Actor run into a run of another Actor with a new input.  This is useful if you want to use another Actor to finish the work of your current Actor run, without the need to create a completely new run and waiting for its finish. For the users of your Actors, the metamorph operation is transparent, they will just see your Actor got the work done.  There is a limit on how many times you can metamorph a single run. You can check the limit in [the Actor runtime limits](https://docs.apify.com/platform/limits#actor-limits).  Internally, the system stops the Docker container corresponding to the Actor run and starts a new container using a different Docker image. All the default storages are preserved and the new input is stored under the &#x60;INPUT-METAMORPH-1&#x60; key in the same default key-value store.  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/development/programming-interface/metamorph). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Actor run ID.</param>
        /// <param name="targetActorId">ID of a target Actor that the run should be transformed into.</param>
        /// <param name="build">Optional build of the target Actor.  It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the target Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (RunResponse)</returns>
        [Obsolete]
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<RunResponse>> ActRunMetamorphPostWithHttpInfoAsync(string actorId, string runId, string targetActorId, string? build = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'actorId' is set
            if (actorId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorId' when calling ActorsActorRunsApi->ActRunMetamorphPost");

            // verify the required parameter 'runId' is set
            if (runId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'runId' when calling ActorsActorRunsApi->ActRunMetamorphPost");

            // verify the required parameter 'targetActorId' is set
            if (targetActorId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'targetActorId' when calling ActorsActorRunsApi->ActRunMetamorphPost");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorId", Apify.SDK.Client.ClientUtils.ParameterToString(actorId)); // path parameter
            localVarRequestOptions.PathParameters.Add("runId", Apify.SDK.Client.ClientUtils.ParameterToString(runId)); // path parameter
            localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "targetActorId", targetActorId));
            if (build != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "build", build));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<RunResponse>("/v2/acts/{actorId}/runs/{runId}/metamorph", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActRunMetamorphPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Resurrect run **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs).Resurrects a finished Actor run and returns an object that contains all the details about the resurrected run.  Only finished runs, i.e. runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTED&#x60; and &#x60;TIMED-OUT&#x60; can be resurrected. Run status will be updated to RUNNING and its container will be restarted with the same storages (the same behaviour as when the run gets migrated to the new server).  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/running/runs-and-builds#resurrection-of-finished-run). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Run ID.</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the run that is being resurrected (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the run that is being resurrected.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the run that is being resurrected.  (optional)</param>
        /// <returns>RunResponse</returns>
        public RunResponse ActRunResurrectPost(string actorId, string runId, string? build = default(string?), double? timeout = default(double?), double? memory = default(double?))
        {
            Apify.SDK.Client.ApiResponse<RunResponse> localVarResponse = ActRunResurrectPostWithHttpInfo(actorId, runId, build, timeout, memory);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Resurrect run **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs).Resurrects a finished Actor run and returns an object that contains all the details about the resurrected run.  Only finished runs, i.e. runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTED&#x60; and &#x60;TIMED-OUT&#x60; can be resurrected. Run status will be updated to RUNNING and its container will be restarted with the same storages (the same behaviour as when the run gets migrated to the new server).  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/running/runs-and-builds#resurrection-of-finished-run). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Run ID.</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the run that is being resurrected (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the run that is being resurrected.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the run that is being resurrected.  (optional)</param>
        /// <returns>ApiResponse of RunResponse</returns>
        public Apify.SDK.Client.ApiResponse<RunResponse> ActRunResurrectPostWithHttpInfo(string actorId, string runId, string? build = default(string?), double? timeout = default(double?), double? memory = default(double?))
        {
            // verify the required parameter 'actorId' is set
            if (actorId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorId' when calling ActorsActorRunsApi->ActRunResurrectPost");

            // verify the required parameter 'runId' is set
            if (runId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'runId' when calling ActorsActorRunsApi->ActRunResurrectPost");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorId", Apify.SDK.Client.ClientUtils.ParameterToString(actorId)); // path parameter
            localVarRequestOptions.PathParameters.Add("runId", Apify.SDK.Client.ClientUtils.ParameterToString(runId)); // path parameter
            if (build != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "build", build));
            }
            if (timeout != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "timeout", timeout));
            }
            if (memory != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "memory", memory));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<RunResponse>("/v2/acts/{actorId}/runs/{runId}/resurrect", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActRunResurrectPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Resurrect run **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs).Resurrects a finished Actor run and returns an object that contains all the details about the resurrected run.  Only finished runs, i.e. runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTED&#x60; and &#x60;TIMED-OUT&#x60; can be resurrected. Run status will be updated to RUNNING and its container will be restarted with the same storages (the same behaviour as when the run gets migrated to the new server).  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/running/runs-and-builds#resurrection-of-finished-run). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Run ID.</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the run that is being resurrected (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the run that is being resurrected.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the run that is being resurrected.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of RunResponse</returns>
        public async System.Threading.Tasks.Task<RunResponse> ActRunResurrectPostAsync(string actorId, string runId, string? build = default(string?), double? timeout = default(double?), double? memory = default(double?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<RunResponse> localVarResponse = await ActRunResurrectPostWithHttpInfoAsync(actorId, runId, build, timeout, memory, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Resurrect run **[DEPRECATED]** API endpoints related to run of the Actor were moved under new namespace [&#x60;actor-runs&#x60;](#/reference/actor-runs).Resurrects a finished Actor run and returns an object that contains all the details about the resurrected run.  Only finished runs, i.e. runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTED&#x60; and &#x60;TIMED-OUT&#x60; can be resurrected. Run status will be updated to RUNNING and its container will be restarted with the same storages (the same behaviour as when the run gets migrated to the new server).  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/running/runs-and-builds#resurrection-of-finished-run). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="runId">Run ID.</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the run that is being resurrected (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the run that is being resurrected.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the run that is being resurrected.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (RunResponse)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<RunResponse>> ActRunResurrectPostWithHttpInfoAsync(string actorId, string runId, string? build = default(string?), double? timeout = default(double?), double? memory = default(double?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'actorId' is set
            if (actorId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorId' when calling ActorsActorRunsApi->ActRunResurrectPost");

            // verify the required parameter 'runId' is set
            if (runId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'runId' when calling ActorsActorRunsApi->ActRunResurrectPost");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorId", Apify.SDK.Client.ClientUtils.ParameterToString(actorId)); // path parameter
            localVarRequestOptions.PathParameters.Add("runId", Apify.SDK.Client.ClientUtils.ParameterToString(runId)); // path parameter
            if (build != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "build", build));
            }
            if (timeout != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "timeout", timeout));
            }
            if (memory != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "memory", memory));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<RunResponse>("/v2/acts/{actorId}/runs/{runId}/resurrect", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActRunResurrectPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Without input Runs a specific Actor and returns its output. The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the API endpoint returns a timeout error. The Actor is not passed any input.  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <returns>Object</returns>
        public Object ActRunSyncGet(string actorId, string? outputRecordKey = default(string?), double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?))
        {
            Apify.SDK.Client.ApiResponse<Object> localVarResponse = ActRunSyncGetWithHttpInfo(actorId, outputRecordKey, timeout, memory, maxItems, maxTotalChargeUsd, build, webhooks);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Without input Runs a specific Actor and returns its output. The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the API endpoint returns a timeout error. The Actor is not passed any input.  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <returns>ApiResponse of Object</returns>
        public Apify.SDK.Client.ApiResponse<Object> ActRunSyncGetWithHttpInfo(string actorId, string? outputRecordKey = default(string?), double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?))
        {
            // verify the required parameter 'actorId' is set
            if (actorId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorId' when calling ActorsActorRunsApi->ActRunSyncGet");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorId", Apify.SDK.Client.ClientUtils.ParameterToString(actorId)); // path parameter
            if (outputRecordKey != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "outputRecordKey", outputRecordKey));
            }
            if (timeout != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "timeout", timeout));
            }
            if (memory != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "memory", memory));
            }
            if (maxItems != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxItems", maxItems));
            }
            if (maxTotalChargeUsd != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxTotalChargeUsd", maxTotalChargeUsd));
            }
            if (build != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "build", build));
            }
            if (webhooks != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "webhooks", webhooks));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<Object>("/v2/acts/{actorId}/run-sync", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActRunSyncGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Without input Runs a specific Actor and returns its output. The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the API endpoint returns a timeout error. The Actor is not passed any input.  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        public async System.Threading.Tasks.Task<Object> ActRunSyncGetAsync(string actorId, string? outputRecordKey = default(string?), double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<Object> localVarResponse = await ActRunSyncGetWithHttpInfoAsync(actorId, outputRecordKey, timeout, memory, maxItems, maxTotalChargeUsd, build, webhooks, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Without input Runs a specific Actor and returns its output. The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the API endpoint returns a timeout error. The Actor is not passed any input.  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<Object>> ActRunSyncGetWithHttpInfoAsync(string actorId, string? outputRecordKey = default(string?), double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'actorId' is set
            if (actorId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorId' when calling ActorsActorRunsApi->ActRunSyncGet");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorId", Apify.SDK.Client.ClientUtils.ParameterToString(actorId)); // path parameter
            if (outputRecordKey != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "outputRecordKey", outputRecordKey));
            }
            if (timeout != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "timeout", timeout));
            }
            if (memory != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "memory", memory));
            }
            if (maxItems != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxItems", maxItems));
            }
            if (maxTotalChargeUsd != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxTotalChargeUsd", maxTotalChargeUsd));
            }
            if (build != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "build", build));
            }
            if (webhooks != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "webhooks", webhooks));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<Object>("/v2/acts/{actorId}/run-sync", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActRunSyncGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Run Actor synchronously without input and get dataset items Runs a specific Actor and returns its dataset items. The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the API endpoint returns a timeout error. The Actor is not passed any input.  It allows to send all possible options in parameters from [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures. For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;. The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not. If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output. This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <returns>Object</returns>
        public Object ActRunSyncGetDatasetItemsGet(string actorId, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?))
        {
            Apify.SDK.Client.ApiResponse<Object> localVarResponse = ActRunSyncGetDatasetItemsGetWithHttpInfo(actorId, timeout, memory, maxItems, maxTotalChargeUsd, build, webhooks, format, clean, offset, limit, fields, omit, unwind, flatten, desc, attachment, delimiter, bom, xmlRoot, xmlRow, skipHeaderRow, skipHidden, skipEmpty, simplified, skipFailedPages);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Run Actor synchronously without input and get dataset items Runs a specific Actor and returns its dataset items. The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the API endpoint returns a timeout error. The Actor is not passed any input.  It allows to send all possible options in parameters from [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures. For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;. The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not. If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output. This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <returns>ApiResponse of Object</returns>
        public Apify.SDK.Client.ApiResponse<Object> ActRunSyncGetDatasetItemsGetWithHttpInfo(string actorId, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?))
        {
            // verify the required parameter 'actorId' is set
            if (actorId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorId' when calling ActorsActorRunsApi->ActRunSyncGetDatasetItemsGet");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorId", Apify.SDK.Client.ClientUtils.ParameterToString(actorId)); // path parameter
            if (timeout != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "timeout", timeout));
            }
            if (memory != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "memory", memory));
            }
            if (maxItems != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxItems", maxItems));
            }
            if (maxTotalChargeUsd != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxTotalChargeUsd", maxTotalChargeUsd));
            }
            if (build != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "build", build));
            }
            if (webhooks != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "webhooks", webhooks));
            }
            if (format != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "format", format));
            }
            if (clean != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "clean", clean));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (fields != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "fields", fields));
            }
            if (omit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "omit", omit));
            }
            if (unwind != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "unwind", unwind));
            }
            if (flatten != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "flatten", flatten));
            }
            if (desc != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "desc", desc));
            }
            if (attachment != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "attachment", attachment));
            }
            if (delimiter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "delimiter", delimiter));
            }
            if (bom != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "bom", bom));
            }
            if (xmlRoot != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "xmlRoot", xmlRoot));
            }
            if (xmlRow != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "xmlRow", xmlRow));
            }
            if (skipHeaderRow != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipHeaderRow", skipHeaderRow));
            }
            if (skipHidden != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipHidden", skipHidden));
            }
            if (skipEmpty != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipEmpty", skipEmpty));
            }
            if (simplified != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "simplified", simplified));
            }
            if (skipFailedPages != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipFailedPages", skipFailedPages));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<Object>("/v2/acts/{actorId}/run-sync-get-dataset-items", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActRunSyncGetDatasetItemsGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Run Actor synchronously without input and get dataset items Runs a specific Actor and returns its dataset items. The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the API endpoint returns a timeout error. The Actor is not passed any input.  It allows to send all possible options in parameters from [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures. For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;. The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not. If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output. This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        public async System.Threading.Tasks.Task<Object> ActRunSyncGetDatasetItemsGetAsync(string actorId, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<Object> localVarResponse = await ActRunSyncGetDatasetItemsGetWithHttpInfoAsync(actorId, timeout, memory, maxItems, maxTotalChargeUsd, build, webhooks, format, clean, offset, limit, fields, omit, unwind, flatten, desc, attachment, delimiter, bom, xmlRoot, xmlRow, skipHeaderRow, skipHidden, skipEmpty, simplified, skipFailedPages, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Run Actor synchronously without input and get dataset items Runs a specific Actor and returns its dataset items. The run must finish in 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds otherwise the API endpoint returns a timeout error. The Actor is not passed any input.  It allows to send all possible options in parameters from [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures. For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;. The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not. If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output. This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<Object>> ActRunSyncGetDatasetItemsGetWithHttpInfoAsync(string actorId, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'actorId' is set
            if (actorId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorId' when calling ActorsActorRunsApi->ActRunSyncGetDatasetItemsGet");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorId", Apify.SDK.Client.ClientUtils.ParameterToString(actorId)); // path parameter
            if (timeout != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "timeout", timeout));
            }
            if (memory != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "memory", memory));
            }
            if (maxItems != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxItems", maxItems));
            }
            if (maxTotalChargeUsd != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxTotalChargeUsd", maxTotalChargeUsd));
            }
            if (build != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "build", build));
            }
            if (webhooks != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "webhooks", webhooks));
            }
            if (format != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "format", format));
            }
            if (clean != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "clean", clean));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (fields != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "fields", fields));
            }
            if (omit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "omit", omit));
            }
            if (unwind != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "unwind", unwind));
            }
            if (flatten != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "flatten", flatten));
            }
            if (desc != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "desc", desc));
            }
            if (attachment != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "attachment", attachment));
            }
            if (delimiter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "delimiter", delimiter));
            }
            if (bom != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "bom", bom));
            }
            if (xmlRoot != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "xmlRoot", xmlRoot));
            }
            if (xmlRow != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "xmlRow", xmlRow));
            }
            if (skipHeaderRow != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipHeaderRow", skipHeaderRow));
            }
            if (skipHidden != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipHidden", skipHidden));
            }
            if (skipEmpty != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipEmpty", skipEmpty));
            }
            if (simplified != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "simplified", simplified));
            }
            if (skipFailedPages != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipFailedPages", skipFailedPages));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<Object>("/v2/acts/{actorId}/run-sync-get-dataset-items", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActRunSyncGetDatasetItemsGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Run Actor synchronously with input and get dataset items Runs a specific Actor and returns its dataset items.  The POST payload including its &#x60;Content-Type&#x60; header is passed as &#x60;INPUT&#x60; to the Actor (usually &#x60;application/json&#x60;). The HTTP response contains the Actors dataset items, while the format of items depends on specifying dataset items&#39; &#x60;format&#x60; parameter.  You can send all the same options in parameters as the [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  The Actor is started with the default options; you can override them using URL query parameters. If the Actor run exceeds 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds, the HTTP response will return the 408 status code (Request Timeout).  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object. If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures. For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;. The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not. If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output. This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <returns>Object</returns>
        public Object ActRunSyncGetDatasetItemsPost(string actorId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?))
        {
            Apify.SDK.Client.ApiResponse<Object> localVarResponse = ActRunSyncGetDatasetItemsPostWithHttpInfo(actorId, body, timeout, memory, maxItems, maxTotalChargeUsd, build, webhooks, format, clean, offset, limit, fields, omit, unwind, flatten, desc, attachment, delimiter, bom, xmlRoot, xmlRow, skipHeaderRow, skipHidden, skipEmpty, simplified, skipFailedPages);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Run Actor synchronously with input and get dataset items Runs a specific Actor and returns its dataset items.  The POST payload including its &#x60;Content-Type&#x60; header is passed as &#x60;INPUT&#x60; to the Actor (usually &#x60;application/json&#x60;). The HTTP response contains the Actors dataset items, while the format of items depends on specifying dataset items&#39; &#x60;format&#x60; parameter.  You can send all the same options in parameters as the [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  The Actor is started with the default options; you can override them using URL query parameters. If the Actor run exceeds 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds, the HTTP response will return the 408 status code (Request Timeout).  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object. If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures. For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;. The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not. If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output. This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <returns>ApiResponse of Object</returns>
        public Apify.SDK.Client.ApiResponse<Object> ActRunSyncGetDatasetItemsPostWithHttpInfo(string actorId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?))
        {
            // verify the required parameter 'actorId' is set
            if (actorId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorId' when calling ActorsActorRunsApi->ActRunSyncGetDatasetItemsPost");

            // verify the required parameter 'body' is set
            if (body == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'body' when calling ActorsActorRunsApi->ActRunSyncGetDatasetItemsPost");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorId", Apify.SDK.Client.ClientUtils.ParameterToString(actorId)); // path parameter
            if (timeout != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "timeout", timeout));
            }
            if (memory != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "memory", memory));
            }
            if (maxItems != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxItems", maxItems));
            }
            if (maxTotalChargeUsd != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxTotalChargeUsd", maxTotalChargeUsd));
            }
            if (build != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "build", build));
            }
            if (webhooks != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "webhooks", webhooks));
            }
            if (format != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "format", format));
            }
            if (clean != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "clean", clean));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (fields != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "fields", fields));
            }
            if (omit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "omit", omit));
            }
            if (unwind != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "unwind", unwind));
            }
            if (flatten != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "flatten", flatten));
            }
            if (desc != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "desc", desc));
            }
            if (attachment != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "attachment", attachment));
            }
            if (delimiter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "delimiter", delimiter));
            }
            if (bom != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "bom", bom));
            }
            if (xmlRoot != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "xmlRoot", xmlRoot));
            }
            if (xmlRow != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "xmlRow", xmlRow));
            }
            if (skipHeaderRow != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipHeaderRow", skipHeaderRow));
            }
            if (skipHidden != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipHidden", skipHidden));
            }
            if (skipEmpty != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipEmpty", skipEmpty));
            }
            if (simplified != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "simplified", simplified));
            }
            if (skipFailedPages != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipFailedPages", skipFailedPages));
            }
            localVarRequestOptions.Data = body;

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<Object>("/v2/acts/{actorId}/run-sync-get-dataset-items", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActRunSyncGetDatasetItemsPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Run Actor synchronously with input and get dataset items Runs a specific Actor and returns its dataset items.  The POST payload including its &#x60;Content-Type&#x60; header is passed as &#x60;INPUT&#x60; to the Actor (usually &#x60;application/json&#x60;). The HTTP response contains the Actors dataset items, while the format of items depends on specifying dataset items&#39; &#x60;format&#x60; parameter.  You can send all the same options in parameters as the [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  The Actor is started with the default options; you can override them using URL query parameters. If the Actor run exceeds 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds, the HTTP response will return the 408 status code (Request Timeout).  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object. If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures. For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;. The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not. If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output. This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        public async System.Threading.Tasks.Task<Object> ActRunSyncGetDatasetItemsPostAsync(string actorId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<Object> localVarResponse = await ActRunSyncGetDatasetItemsPostWithHttpInfoAsync(actorId, body, timeout, memory, maxItems, maxTotalChargeUsd, build, webhooks, format, clean, offset, limit, fields, omit, unwind, flatten, desc, attachment, delimiter, bom, xmlRoot, xmlRow, skipHeaderRow, skipHidden, skipEmpty, simplified, skipFailedPages, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Run Actor synchronously with input and get dataset items Runs a specific Actor and returns its dataset items.  The POST payload including its &#x60;Content-Type&#x60; header is passed as &#x60;INPUT&#x60; to the Actor (usually &#x60;application/json&#x60;). The HTTP response contains the Actors dataset items, while the format of items depends on specifying dataset items&#39; &#x60;format&#x60; parameter.  You can send all the same options in parameters as the [Get Dataset Items](#/reference/datasets/item-collection/get-items) API endpoint.  The Actor is started with the default options; you can override them using URL query parameters. If the Actor run exceeds 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds, the HTTP response will return the 408 status code (Request Timeout).  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="format">Format of the results, possible values are: &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;csv&#x60;, &#x60;html&#x60;, &#x60;xlsx&#x60;, &#x60;xml&#x60; and &#x60;rss&#x60;. The default value is &#x60;json&#x60;.  (optional)</param>
        /// <param name="clean">If &#x60;true&#x60; or &#x60;1&#x60; then the API endpoint returns only non-empty items and skips hidden fields (i.e. fields starting with the # character). The &#x60;clean&#x60; parameter is just a shortcut for &#x60;skipHidden&#x3D;true&#x60; and &#x60;skipEmpty&#x3D;true&#x60; parameters. Note that since some objects might be skipped from the output, that the result might contain less items than the &#x60;limit&#x60; value.  (optional)</param>
        /// <param name="offset">Number of items that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of items to return. By default there is no limit. (optional)</param>
        /// <param name="fields">A comma-separated list of fields which should be picked from the items, only these fields will remain in the resulting record objects. Note that the fields in the outputted items are sorted the same way as they are specified in the &#x60;fields&#x60; query parameter. You can use this feature to effectively fix the output format.  (optional)</param>
        /// <param name="omit">A comma-separated list of fields which should be omitted from the items. (optional)</param>
        /// <param name="unwind">A comma-separated list of fields which should be unwound, in order which they should be processed. Each field should be either an array or an object. If the field is an array then every element of the array will become a separate record and merged with parent object. If the unwound field is an object then it is merged with the parent object. If the unwound field is missing or its value is neither an array nor an object and therefore cannot be merged with a parent object then the item gets preserved as it is. Note that the unwound items ignore the &#x60;desc&#x60; parameter.  (optional)</param>
        /// <param name="flatten">A comma-separated list of fields which should transform nested objects into flat structures. For example, with &#x60;flatten&#x3D;\&quot;foo\&quot;&#x60; the object &#x60;{\&quot;foo\&quot;:{\&quot;bar\&quot;: \&quot;hello\&quot;}}&#x60; is turned into &#x60;{\&quot;foo.bar\&quot;: \&quot;hello\&quot;}&#x60;. The original object with properties is replaced with the flattened object.  (optional)</param>
        /// <param name="desc">By default, results are returned in the same order as they were stored. To reverse the order, set this parameter to &#x60;true&#x60; or &#x60;1&#x60;.  (optional)</param>
        /// <param name="attachment">If &#x60;true&#x60; or &#x60;1&#x60; then the response will define the &#x60;Content-Disposition: attachment&#x60; header, forcing a web browser to download the file rather than to display it. By default this header is not present.  (optional)</param>
        /// <param name="delimiter">A delimiter character for CSV files, only used if &#x60;format&#x3D;csv&#x60;. You might need to URL-encode the character (e.g. use &#x60;%09&#x60; for tab or &#x60;%3B&#x60; for semicolon). The default delimiter is a simple comma (&#x60;,&#x60;).  (optional)</param>
        /// <param name="bom">All text responses are encoded in UTF-8 encoding. By default, the &#x60;format&#x3D;csv&#x60; files are prefixed with the UTF-8 Byte Order Mark (BOM), while &#x60;json&#x60;, &#x60;jsonl&#x60;, &#x60;xml&#x60;, &#x60;html&#x60; and &#x60;rss&#x60; files are not. If you want to override this default behavior, specify &#x60;bom&#x3D;1&#x60; query parameter to include the BOM or &#x60;bom&#x3D;0&#x60; to skip it.  (optional)</param>
        /// <param name="xmlRoot">Overrides default root element name of &#x60;xml&#x60; output. By default the root element is &#x60;items&#x60;.  (optional)</param>
        /// <param name="xmlRow">Overrides default element name that wraps each page or page function result object in &#x60;xml&#x60; output. By default the element name is &#x60;item&#x60;.  (optional)</param>
        /// <param name="skipHeaderRow">If &#x60;true&#x60; or &#x60;1&#x60; then header row in the &#x60;csv&#x60; format is skipped. (optional)</param>
        /// <param name="skipHidden">If &#x60;true&#x60; or &#x60;1&#x60; then hidden fields are skipped from the output, i.e. fields starting with the &#x60;#&#x60; character.  (optional)</param>
        /// <param name="skipEmpty">If &#x60;true&#x60; or &#x60;1&#x60; then empty items are skipped from the output.  Note that if used, the results might contain less items than the limit value.  (optional)</param>
        /// <param name="simplified">If &#x60;true&#x60; or &#x60;1&#x60; then, the endpoint applies the &#x60;fields&#x3D;url,pageFunctionResult,errorInfo&#x60; and &#x60;unwind&#x3D;pageFunctionResult&#x60; query parameters. This feature is used to emulate simplified results provided by the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="skipFailedPages">If &#x60;true&#x60; or &#x60;1&#x60; then, the all the items with errorInfo property will be skipped from the output. This feature is here to emulate functionality of API version 1 used for the legacy Apify Crawler product and it&#39;s not recommended to use it in new integrations.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<Object>> ActRunSyncGetDatasetItemsPostWithHttpInfoAsync(string actorId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), string? format = default(string?), bool? clean = default(bool?), double? offset = default(double?), double? limit = default(double?), string? fields = default(string?), string? omit = default(string?), string? unwind = default(string?), string? flatten = default(string?), bool? desc = default(bool?), bool? attachment = default(bool?), string? delimiter = default(string?), bool? bom = default(bool?), string? xmlRoot = default(string?), string? xmlRow = default(string?), bool? skipHeaderRow = default(bool?), bool? skipHidden = default(bool?), bool? skipEmpty = default(bool?), bool? simplified = default(bool?), bool? skipFailedPages = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'actorId' is set
            if (actorId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorId' when calling ActorsActorRunsApi->ActRunSyncGetDatasetItemsPost");

            // verify the required parameter 'body' is set
            if (body == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'body' when calling ActorsActorRunsApi->ActRunSyncGetDatasetItemsPost");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorId", Apify.SDK.Client.ClientUtils.ParameterToString(actorId)); // path parameter
            if (timeout != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "timeout", timeout));
            }
            if (memory != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "memory", memory));
            }
            if (maxItems != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxItems", maxItems));
            }
            if (maxTotalChargeUsd != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxTotalChargeUsd", maxTotalChargeUsd));
            }
            if (build != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "build", build));
            }
            if (webhooks != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "webhooks", webhooks));
            }
            if (format != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "format", format));
            }
            if (clean != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "clean", clean));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (fields != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "fields", fields));
            }
            if (omit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "omit", omit));
            }
            if (unwind != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "unwind", unwind));
            }
            if (flatten != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "flatten", flatten));
            }
            if (desc != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "desc", desc));
            }
            if (attachment != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "attachment", attachment));
            }
            if (delimiter != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "delimiter", delimiter));
            }
            if (bom != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "bom", bom));
            }
            if (xmlRoot != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "xmlRoot", xmlRoot));
            }
            if (xmlRow != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "xmlRow", xmlRow));
            }
            if (skipHeaderRow != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipHeaderRow", skipHeaderRow));
            }
            if (skipHidden != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipHidden", skipHidden));
            }
            if (skipEmpty != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipEmpty", skipEmpty));
            }
            if (simplified != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "simplified", simplified));
            }
            if (skipFailedPages != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "skipFailedPages", skipFailedPages));
            }
            localVarRequestOptions.Data = body;

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<Object>("/v2/acts/{actorId}/run-sync-get-dataset-items", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActRunSyncGetDatasetItemsPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Run Actor synchronously with input and return output Runs a specific Actor and returns its output.  The POST payload including its &#x60;Content-Type&#x60; header is passed as &#x60;INPUT&#x60; to the Actor (usually &lt;code&gt;application/json&lt;/code&gt;). The HTTP response contains Actors &#x60;OUTPUT&#x60; record from its default key-value store.  The Actor is started with the default options; you can override them using various URL query parameters. If the Actor run exceeds 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds, the HTTP response will have status 408 (Request Timeout).  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="body"></param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <returns>Object</returns>
        public Object ActRunSyncPost(string actorId, Object body, string? outputRecordKey = default(string?), double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?))
        {
            Apify.SDK.Client.ApiResponse<Object> localVarResponse = ActRunSyncPostWithHttpInfo(actorId, body, outputRecordKey, timeout, memory, maxItems, maxTotalChargeUsd, build, webhooks);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Run Actor synchronously with input and return output Runs a specific Actor and returns its output.  The POST payload including its &#x60;Content-Type&#x60; header is passed as &#x60;INPUT&#x60; to the Actor (usually &lt;code&gt;application/json&lt;/code&gt;). The HTTP response contains Actors &#x60;OUTPUT&#x60; record from its default key-value store.  The Actor is started with the default options; you can override them using various URL query parameters. If the Actor run exceeds 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds, the HTTP response will have status 408 (Request Timeout).  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="body"></param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <returns>ApiResponse of Object</returns>
        public Apify.SDK.Client.ApiResponse<Object> ActRunSyncPostWithHttpInfo(string actorId, Object body, string? outputRecordKey = default(string?), double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?))
        {
            // verify the required parameter 'actorId' is set
            if (actorId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorId' when calling ActorsActorRunsApi->ActRunSyncPost");

            // verify the required parameter 'body' is set
            if (body == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'body' when calling ActorsActorRunsApi->ActRunSyncPost");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorId", Apify.SDK.Client.ClientUtils.ParameterToString(actorId)); // path parameter
            if (outputRecordKey != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "outputRecordKey", outputRecordKey));
            }
            if (timeout != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "timeout", timeout));
            }
            if (memory != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "memory", memory));
            }
            if (maxItems != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxItems", maxItems));
            }
            if (maxTotalChargeUsd != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxTotalChargeUsd", maxTotalChargeUsd));
            }
            if (build != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "build", build));
            }
            if (webhooks != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "webhooks", webhooks));
            }
            localVarRequestOptions.Data = body;

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<Object>("/v2/acts/{actorId}/run-sync", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActRunSyncPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Run Actor synchronously with input and return output Runs a specific Actor and returns its output.  The POST payload including its &#x60;Content-Type&#x60; header is passed as &#x60;INPUT&#x60; to the Actor (usually &lt;code&gt;application/json&lt;/code&gt;). The HTTP response contains Actors &#x60;OUTPUT&#x60; record from its default key-value store.  The Actor is started with the default options; you can override them using various URL query parameters. If the Actor run exceeds 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds, the HTTP response will have status 408 (Request Timeout).  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="body"></param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        public async System.Threading.Tasks.Task<Object> ActRunSyncPostAsync(string actorId, Object body, string? outputRecordKey = default(string?), double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<Object> localVarResponse = await ActRunSyncPostWithHttpInfoAsync(actorId, body, outputRecordKey, timeout, memory, maxItems, maxTotalChargeUsd, build, webhooks, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Run Actor synchronously with input and return output Runs a specific Actor and returns its output.  The POST payload including its &#x60;Content-Type&#x60; header is passed as &#x60;INPUT&#x60; to the Actor (usually &lt;code&gt;application/json&lt;/code&gt;). The HTTP response contains Actors &#x60;OUTPUT&#x60; record from its default key-value store.  The Actor is started with the default options; you can override them using various URL query parameters. If the Actor run exceeds 300&lt;!- - MAX_ACTOR_JOB_SYNC_WAIT_SECS - -&gt; seconds, the HTTP response will have status 408 (Request Timeout).  Beware that it might be impossible to maintain an idle HTTP connection for a long period of time, due to client timeout or network conditions. Make sure your HTTP client is configured to have a long enough connection timeout. If the connection breaks, you will not receive any information about the run and its status.  To run the Actor asynchronously, use the [Run Actor](#/reference/actors/run-collection/run-actor) API endpoint instead. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="body"></param>
        /// <param name="outputRecordKey">Key of the record from run&#39;s default key-value store to be returned in the response. By default, it is &#x60;OUTPUT&#x60;.  (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<Object>> ActRunSyncPostWithHttpInfoAsync(string actorId, Object body, string? outputRecordKey = default(string?), double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), string? webhooks = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'actorId' is set
            if (actorId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorId' when calling ActorsActorRunsApi->ActRunSyncPost");

            // verify the required parameter 'body' is set
            if (body == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'body' when calling ActorsActorRunsApi->ActRunSyncPost");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorId", Apify.SDK.Client.ClientUtils.ParameterToString(actorId)); // path parameter
            if (outputRecordKey != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "outputRecordKey", outputRecordKey));
            }
            if (timeout != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "timeout", timeout));
            }
            if (memory != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "memory", memory));
            }
            if (maxItems != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxItems", maxItems));
            }
            if (maxTotalChargeUsd != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxTotalChargeUsd", maxTotalChargeUsd));
            }
            if (build != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "build", build));
            }
            if (webhooks != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "webhooks", webhooks));
            }
            localVarRequestOptions.Data = body;

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<Object>("/v2/acts/{actorId}/run-sync", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActRunSyncPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get list of runs Gets the list of runs of a specific Actor. The response is a list of objects, where each object contains basic information about a single Actor run.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 array elements.  By default, the records are sorted by the &#x60;startedAt&#x60; field in ascending order, therefore you can use pagination to incrementally fetch all records while new ones are still being created. To sort the records in descending order, use &#x60;desc&#x3D;1&#x60; parameter. You can also filter runs by status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle)). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="status">Return only runs with the provided status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle))  (optional)</param>
        /// <returns>GetUserRunsListResponse</returns>
        public GetUserRunsListResponse ActRunsGet(string actorId, double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), string? status = default(string?))
        {
            Apify.SDK.Client.ApiResponse<GetUserRunsListResponse> localVarResponse = ActRunsGetWithHttpInfo(actorId, offset, limit, desc, status);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get list of runs Gets the list of runs of a specific Actor. The response is a list of objects, where each object contains basic information about a single Actor run.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 array elements.  By default, the records are sorted by the &#x60;startedAt&#x60; field in ascending order, therefore you can use pagination to incrementally fetch all records while new ones are still being created. To sort the records in descending order, use &#x60;desc&#x3D;1&#x60; parameter. You can also filter runs by status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle)). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="status">Return only runs with the provided status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle))  (optional)</param>
        /// <returns>ApiResponse of GetUserRunsListResponse</returns>
        public Apify.SDK.Client.ApiResponse<GetUserRunsListResponse> ActRunsGetWithHttpInfo(string actorId, double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), string? status = default(string?))
        {
            // verify the required parameter 'actorId' is set
            if (actorId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorId' when calling ActorsActorRunsApi->ActRunsGet");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorId", Apify.SDK.Client.ClientUtils.ParameterToString(actorId)); // path parameter
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (desc != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "desc", desc));
            }
            if (status != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "status", status));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<GetUserRunsListResponse>("/v2/acts/{actorId}/runs", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActRunsGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get list of runs Gets the list of runs of a specific Actor. The response is a list of objects, where each object contains basic information about a single Actor run.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 array elements.  By default, the records are sorted by the &#x60;startedAt&#x60; field in ascending order, therefore you can use pagination to incrementally fetch all records while new ones are still being created. To sort the records in descending order, use &#x60;desc&#x3D;1&#x60; parameter. You can also filter runs by status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle)). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="status">Return only runs with the provided status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle))  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of GetUserRunsListResponse</returns>
        public async System.Threading.Tasks.Task<GetUserRunsListResponse> ActRunsGetAsync(string actorId, double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), string? status = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<GetUserRunsListResponse> localVarResponse = await ActRunsGetWithHttpInfoAsync(actorId, offset, limit, desc, status, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get list of runs Gets the list of runs of a specific Actor. The response is a list of objects, where each object contains basic information about a single Actor run.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 array elements.  By default, the records are sorted by the &#x60;startedAt&#x60; field in ascending order, therefore you can use pagination to incrementally fetch all records while new ones are still being created. To sort the records in descending order, use &#x60;desc&#x3D;1&#x60; parameter. You can also filter runs by status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle)). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="status">Return only runs with the provided status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle))  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (GetUserRunsListResponse)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<GetUserRunsListResponse>> ActRunsGetWithHttpInfoAsync(string actorId, double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), string? status = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'actorId' is set
            if (actorId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorId' when calling ActorsActorRunsApi->ActRunsGet");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorId", Apify.SDK.Client.ClientUtils.ParameterToString(actorId)); // path parameter
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (desc != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "desc", desc));
            }
            if (status != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "status", status));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<GetUserRunsListResponse>("/v2/acts/{actorId}/runs", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActRunsGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get last run This is not a single endpoint, but an entire group of endpoints that lets you to retrieve and manage the last run of given Actor or any of its default storages. All the endpoints require an authentication token.  The endpoints accept the same HTTP methods and query parameters as the respective storage endpoints. The base path represents the last Actor run object is:  &#x60;/v2/acts/{actorId}/runs/last{?token,status}&#x60;  Using the &#x60;status&#x60; query parameter you can ensure to only get a run with a certain status (e.g. &#x60;status&#x3D;SUCCEEDED&#x60;). The output of this endpoint and other query parameters are the same as in the [Run object](#/reference/actors/run-object) endpoint.  In order to access the default storages of the last Actor run, i.e. log, key-value store, dataset and request queue, use the following endpoints:  * &#x60;/v2/acts/{actorId}/runs/last/log{?token,status}&#x60; * &#x60;/v2/acts/{actorId}/runs/last/key-value-store{?token,status}&#x60; * &#x60;/v2/acts/{actorId}/runs/last/dataset{?token,status}&#x60; * &#x60;/v2/acts/{actorId}/runs/last/request-queue{?token,status}&#x60;  These API endpoints have the same usage as the equivalent storage endpoints. For example, &#x60;/v2/acts/{actorId}/runs/last/key-value-store&#x60; has the same HTTP method and parameters as the [Key-value store object](#/reference/key-value-stores/store-object) endpoint.  Additionally, each of the above API endpoints supports all sub-endpoints of the original one:  #### Key-value store  * &#x60;/v2/acts/{actorId}/runs/last/key-value-store/keys{?token,status}&#x60; [Key collection](#/reference/key-value-stores/key-collection) * &#x60;/v2/acts/{actorId}/runs/last/key-value-store/records/{recordKey}{?token,status}&#x60; [Record](#/reference/key-value-stores/record)  #### Dataset  * &#x60;/v2/acts/{actorId}/runs/last/dataset/items{?token,status}&#x60; [Item collection](#/reference/datasets/item-collection)  #### Request queue  * &#x60;/v2/acts/{actorId}/runs/last/request-queue/requests{?token,status}&#x60; [Request collection](#/reference/request-queues/request-collection) * &#x60;/v2/acts/{actorId}/runs/last/request-queue/requests/{requestId}{?token,status}&#x60; [Request collection](#/reference/request-queues/request) * &#x60;/v2/acts/{actorId}/runs/last/request-queue/head{?token,status}&#x60; [Queue head](#/reference/request-queues/queue-head)  For example, to download data from a dataset of the last succeeded Actor run in XML format, send HTTP GET request to the following URL:  &#x60;&#x60;&#x60; https://api.apify.com/v2/acts/{actorId}/runs/last/dataset/items?token&#x3D;{yourApiToken}&amp;format&#x3D;xml&amp;status&#x3D;SUCCEEDED &#x60;&#x60;&#x60;  In order to save new items to the dataset, send HTTP POST request with JSON payload to the same URL. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="status">Filter for the run status. (optional)</param>
        /// <returns>RunResponse</returns>
        public RunResponse ActRunsLastGet(string actorId, string? status = default(string?))
        {
            Apify.SDK.Client.ApiResponse<RunResponse> localVarResponse = ActRunsLastGetWithHttpInfo(actorId, status);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get last run This is not a single endpoint, but an entire group of endpoints that lets you to retrieve and manage the last run of given Actor or any of its default storages. All the endpoints require an authentication token.  The endpoints accept the same HTTP methods and query parameters as the respective storage endpoints. The base path represents the last Actor run object is:  &#x60;/v2/acts/{actorId}/runs/last{?token,status}&#x60;  Using the &#x60;status&#x60; query parameter you can ensure to only get a run with a certain status (e.g. &#x60;status&#x3D;SUCCEEDED&#x60;). The output of this endpoint and other query parameters are the same as in the [Run object](#/reference/actors/run-object) endpoint.  In order to access the default storages of the last Actor run, i.e. log, key-value store, dataset and request queue, use the following endpoints:  * &#x60;/v2/acts/{actorId}/runs/last/log{?token,status}&#x60; * &#x60;/v2/acts/{actorId}/runs/last/key-value-store{?token,status}&#x60; * &#x60;/v2/acts/{actorId}/runs/last/dataset{?token,status}&#x60; * &#x60;/v2/acts/{actorId}/runs/last/request-queue{?token,status}&#x60;  These API endpoints have the same usage as the equivalent storage endpoints. For example, &#x60;/v2/acts/{actorId}/runs/last/key-value-store&#x60; has the same HTTP method and parameters as the [Key-value store object](#/reference/key-value-stores/store-object) endpoint.  Additionally, each of the above API endpoints supports all sub-endpoints of the original one:  #### Key-value store  * &#x60;/v2/acts/{actorId}/runs/last/key-value-store/keys{?token,status}&#x60; [Key collection](#/reference/key-value-stores/key-collection) * &#x60;/v2/acts/{actorId}/runs/last/key-value-store/records/{recordKey}{?token,status}&#x60; [Record](#/reference/key-value-stores/record)  #### Dataset  * &#x60;/v2/acts/{actorId}/runs/last/dataset/items{?token,status}&#x60; [Item collection](#/reference/datasets/item-collection)  #### Request queue  * &#x60;/v2/acts/{actorId}/runs/last/request-queue/requests{?token,status}&#x60; [Request collection](#/reference/request-queues/request-collection) * &#x60;/v2/acts/{actorId}/runs/last/request-queue/requests/{requestId}{?token,status}&#x60; [Request collection](#/reference/request-queues/request) * &#x60;/v2/acts/{actorId}/runs/last/request-queue/head{?token,status}&#x60; [Queue head](#/reference/request-queues/queue-head)  For example, to download data from a dataset of the last succeeded Actor run in XML format, send HTTP GET request to the following URL:  &#x60;&#x60;&#x60; https://api.apify.com/v2/acts/{actorId}/runs/last/dataset/items?token&#x3D;{yourApiToken}&amp;format&#x3D;xml&amp;status&#x3D;SUCCEEDED &#x60;&#x60;&#x60;  In order to save new items to the dataset, send HTTP POST request with JSON payload to the same URL. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="status">Filter for the run status. (optional)</param>
        /// <returns>ApiResponse of RunResponse</returns>
        public Apify.SDK.Client.ApiResponse<RunResponse> ActRunsLastGetWithHttpInfo(string actorId, string? status = default(string?))
        {
            // verify the required parameter 'actorId' is set
            if (actorId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorId' when calling ActorsActorRunsApi->ActRunsLastGet");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorId", Apify.SDK.Client.ClientUtils.ParameterToString(actorId)); // path parameter
            if (status != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "status", status));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<RunResponse>("/v2/acts/{actorId}/runs/last", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActRunsLastGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get last run This is not a single endpoint, but an entire group of endpoints that lets you to retrieve and manage the last run of given Actor or any of its default storages. All the endpoints require an authentication token.  The endpoints accept the same HTTP methods and query parameters as the respective storage endpoints. The base path represents the last Actor run object is:  &#x60;/v2/acts/{actorId}/runs/last{?token,status}&#x60;  Using the &#x60;status&#x60; query parameter you can ensure to only get a run with a certain status (e.g. &#x60;status&#x3D;SUCCEEDED&#x60;). The output of this endpoint and other query parameters are the same as in the [Run object](#/reference/actors/run-object) endpoint.  In order to access the default storages of the last Actor run, i.e. log, key-value store, dataset and request queue, use the following endpoints:  * &#x60;/v2/acts/{actorId}/runs/last/log{?token,status}&#x60; * &#x60;/v2/acts/{actorId}/runs/last/key-value-store{?token,status}&#x60; * &#x60;/v2/acts/{actorId}/runs/last/dataset{?token,status}&#x60; * &#x60;/v2/acts/{actorId}/runs/last/request-queue{?token,status}&#x60;  These API endpoints have the same usage as the equivalent storage endpoints. For example, &#x60;/v2/acts/{actorId}/runs/last/key-value-store&#x60; has the same HTTP method and parameters as the [Key-value store object](#/reference/key-value-stores/store-object) endpoint.  Additionally, each of the above API endpoints supports all sub-endpoints of the original one:  #### Key-value store  * &#x60;/v2/acts/{actorId}/runs/last/key-value-store/keys{?token,status}&#x60; [Key collection](#/reference/key-value-stores/key-collection) * &#x60;/v2/acts/{actorId}/runs/last/key-value-store/records/{recordKey}{?token,status}&#x60; [Record](#/reference/key-value-stores/record)  #### Dataset  * &#x60;/v2/acts/{actorId}/runs/last/dataset/items{?token,status}&#x60; [Item collection](#/reference/datasets/item-collection)  #### Request queue  * &#x60;/v2/acts/{actorId}/runs/last/request-queue/requests{?token,status}&#x60; [Request collection](#/reference/request-queues/request-collection) * &#x60;/v2/acts/{actorId}/runs/last/request-queue/requests/{requestId}{?token,status}&#x60; [Request collection](#/reference/request-queues/request) * &#x60;/v2/acts/{actorId}/runs/last/request-queue/head{?token,status}&#x60; [Queue head](#/reference/request-queues/queue-head)  For example, to download data from a dataset of the last succeeded Actor run in XML format, send HTTP GET request to the following URL:  &#x60;&#x60;&#x60; https://api.apify.com/v2/acts/{actorId}/runs/last/dataset/items?token&#x3D;{yourApiToken}&amp;format&#x3D;xml&amp;status&#x3D;SUCCEEDED &#x60;&#x60;&#x60;  In order to save new items to the dataset, send HTTP POST request with JSON payload to the same URL. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="status">Filter for the run status. (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of RunResponse</returns>
        public async System.Threading.Tasks.Task<RunResponse> ActRunsLastGetAsync(string actorId, string? status = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<RunResponse> localVarResponse = await ActRunsLastGetWithHttpInfoAsync(actorId, status, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get last run This is not a single endpoint, but an entire group of endpoints that lets you to retrieve and manage the last run of given Actor or any of its default storages. All the endpoints require an authentication token.  The endpoints accept the same HTTP methods and query parameters as the respective storage endpoints. The base path represents the last Actor run object is:  &#x60;/v2/acts/{actorId}/runs/last{?token,status}&#x60;  Using the &#x60;status&#x60; query parameter you can ensure to only get a run with a certain status (e.g. &#x60;status&#x3D;SUCCEEDED&#x60;). The output of this endpoint and other query parameters are the same as in the [Run object](#/reference/actors/run-object) endpoint.  In order to access the default storages of the last Actor run, i.e. log, key-value store, dataset and request queue, use the following endpoints:  * &#x60;/v2/acts/{actorId}/runs/last/log{?token,status}&#x60; * &#x60;/v2/acts/{actorId}/runs/last/key-value-store{?token,status}&#x60; * &#x60;/v2/acts/{actorId}/runs/last/dataset{?token,status}&#x60; * &#x60;/v2/acts/{actorId}/runs/last/request-queue{?token,status}&#x60;  These API endpoints have the same usage as the equivalent storage endpoints. For example, &#x60;/v2/acts/{actorId}/runs/last/key-value-store&#x60; has the same HTTP method and parameters as the [Key-value store object](#/reference/key-value-stores/store-object) endpoint.  Additionally, each of the above API endpoints supports all sub-endpoints of the original one:  #### Key-value store  * &#x60;/v2/acts/{actorId}/runs/last/key-value-store/keys{?token,status}&#x60; [Key collection](#/reference/key-value-stores/key-collection) * &#x60;/v2/acts/{actorId}/runs/last/key-value-store/records/{recordKey}{?token,status}&#x60; [Record](#/reference/key-value-stores/record)  #### Dataset  * &#x60;/v2/acts/{actorId}/runs/last/dataset/items{?token,status}&#x60; [Item collection](#/reference/datasets/item-collection)  #### Request queue  * &#x60;/v2/acts/{actorId}/runs/last/request-queue/requests{?token,status}&#x60; [Request collection](#/reference/request-queues/request-collection) * &#x60;/v2/acts/{actorId}/runs/last/request-queue/requests/{requestId}{?token,status}&#x60; [Request collection](#/reference/request-queues/request) * &#x60;/v2/acts/{actorId}/runs/last/request-queue/head{?token,status}&#x60; [Queue head](#/reference/request-queues/queue-head)  For example, to download data from a dataset of the last succeeded Actor run in XML format, send HTTP GET request to the following URL:  &#x60;&#x60;&#x60; https://api.apify.com/v2/acts/{actorId}/runs/last/dataset/items?token&#x3D;{yourApiToken}&amp;format&#x3D;xml&amp;status&#x3D;SUCCEEDED &#x60;&#x60;&#x60;  In order to save new items to the dataset, send HTTP POST request with JSON payload to the same URL. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="status">Filter for the run status. (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (RunResponse)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<RunResponse>> ActRunsLastGetWithHttpInfoAsync(string actorId, string? status = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'actorId' is set
            if (actorId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorId' when calling ActorsActorRunsApi->ActRunsLastGet");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorId", Apify.SDK.Client.ClientUtils.ParameterToString(actorId)); // path parameter
            if (status != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "status", status));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<RunResponse>("/v2/acts/{actorId}/runs/last", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActRunsLastGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Run Actor Runs an Actor and immediately returns without waiting for the run to finish.  The POST payload including its &#x60;Content-Type&#x60; header is passed as &#x60;INPUT&#x60; to the Actor (usually &#x60;application/json&#x60;).  The Actor is started with the default options; you can override them using various URL query parameters.  The response is the Run object as returned by the [Get run](#/reference/actor-runs/run-object-and-its-storages/get-run) API endpoint.  If you want to wait for the run to finish and receive the actual output of the Actor as the response, please use one of the [Run Actor synchronously](#/reference/actors/run-actor-synchronously) API endpoints instead.  To fetch the Actor run results that are typically stored in the default dataset, you&#39;ll need to pass the ID received in the &#x60;defaultDatasetId&#x60; field received in the response JSON to the [Get items](#/reference/datasets/item-collection/get-items) API endpoint. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default, it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <returns>RunResponse</returns>
        public RunResponse ActRunsPost(string actorId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), double? waitForFinish = default(double?), string? webhooks = default(string?))
        {
            Apify.SDK.Client.ApiResponse<RunResponse> localVarResponse = ActRunsPostWithHttpInfo(actorId, body, timeout, memory, maxItems, maxTotalChargeUsd, build, waitForFinish, webhooks);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Run Actor Runs an Actor and immediately returns without waiting for the run to finish.  The POST payload including its &#x60;Content-Type&#x60; header is passed as &#x60;INPUT&#x60; to the Actor (usually &#x60;application/json&#x60;).  The Actor is started with the default options; you can override them using various URL query parameters.  The response is the Run object as returned by the [Get run](#/reference/actor-runs/run-object-and-its-storages/get-run) API endpoint.  If you want to wait for the run to finish and receive the actual output of the Actor as the response, please use one of the [Run Actor synchronously](#/reference/actors/run-actor-synchronously) API endpoints instead.  To fetch the Actor run results that are typically stored in the default dataset, you&#39;ll need to pass the ID received in the &#x60;defaultDatasetId&#x60; field received in the response JSON to the [Get items](#/reference/datasets/item-collection/get-items) API endpoint. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default, it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <returns>ApiResponse of RunResponse</returns>
        public Apify.SDK.Client.ApiResponse<RunResponse> ActRunsPostWithHttpInfo(string actorId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), double? waitForFinish = default(double?), string? webhooks = default(string?))
        {
            // verify the required parameter 'actorId' is set
            if (actorId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorId' when calling ActorsActorRunsApi->ActRunsPost");

            // verify the required parameter 'body' is set
            if (body == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'body' when calling ActorsActorRunsApi->ActRunsPost");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorId", Apify.SDK.Client.ClientUtils.ParameterToString(actorId)); // path parameter
            if (timeout != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "timeout", timeout));
            }
            if (memory != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "memory", memory));
            }
            if (maxItems != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxItems", maxItems));
            }
            if (maxTotalChargeUsd != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxTotalChargeUsd", maxTotalChargeUsd));
            }
            if (build != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "build", build));
            }
            if (waitForFinish != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "waitForFinish", waitForFinish));
            }
            if (webhooks != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "webhooks", webhooks));
            }
            localVarRequestOptions.Data = body;

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<RunResponse>("/v2/acts/{actorId}/runs", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActRunsPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Run Actor Runs an Actor and immediately returns without waiting for the run to finish.  The POST payload including its &#x60;Content-Type&#x60; header is passed as &#x60;INPUT&#x60; to the Actor (usually &#x60;application/json&#x60;).  The Actor is started with the default options; you can override them using various URL query parameters.  The response is the Run object as returned by the [Get run](#/reference/actor-runs/run-object-and-its-storages/get-run) API endpoint.  If you want to wait for the run to finish and receive the actual output of the Actor as the response, please use one of the [Run Actor synchronously](#/reference/actors/run-actor-synchronously) API endpoints instead.  To fetch the Actor run results that are typically stored in the default dataset, you&#39;ll need to pass the ID received in the &#x60;defaultDatasetId&#x60; field received in the response JSON to the [Get items](#/reference/datasets/item-collection/get-items) API endpoint. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default, it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of RunResponse</returns>
        public async System.Threading.Tasks.Task<RunResponse> ActRunsPostAsync(string actorId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), double? waitForFinish = default(double?), string? webhooks = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<RunResponse> localVarResponse = await ActRunsPostWithHttpInfoAsync(actorId, body, timeout, memory, maxItems, maxTotalChargeUsd, build, waitForFinish, webhooks, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Run Actor Runs an Actor and immediately returns without waiting for the run to finish.  The POST payload including its &#x60;Content-Type&#x60; header is passed as &#x60;INPUT&#x60; to the Actor (usually &#x60;application/json&#x60;).  The Actor is started with the default options; you can override them using various URL query parameters.  The response is the Run object as returned by the [Get run](#/reference/actor-runs/run-object-and-its-storages/get-run) API endpoint.  If you want to wait for the run to finish and receive the actual output of the Actor as the response, please use one of the [Run Actor synchronously](#/reference/actors/run-actor-synchronously) API endpoints instead.  To fetch the Actor run results that are typically stored in the default dataset, you&#39;ll need to pass the ID received in the &#x60;defaultDatasetId&#x60; field received in the response JSON to the [Get items](#/reference/datasets/item-collection/get-items) API endpoint. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="actorId">Actor ID or a tilde-separated owner&#39;s username and Actor name.</param>
        /// <param name="body"></param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="memory">Memory limit for the run, in megabytes. The amount of memory can be set to a power of 2 with a minimum of 128. By default, the run uses a memory limit specified in the default run configuration for the Actor.  (optional)</param>
        /// <param name="maxItems">The maximum number of items that the Actor run should return. This is useful for pay-per-result Actors, as it allows you to limit the number of results that will be charged to your subscription. You can access the maximum number of items in your Actor by using the &#x60;ACTOR_MAX_PAID_DATASET_ITEMS&#x60; environment variable.  (optional)</param>
        /// <param name="maxTotalChargeUsd">Specifies the maximum cost of the Actor run. This parameter is useful for pay-per-event Actors, as it allows you to limit the amount charged to your subscription. You can access the maximum cost in your Actor by using the &#x60;ACTOR_MAX_TOTAL_CHARGE_USD&#x60; environment variable.  (optional)</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default, it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <param name="webhooks">Specifies optional webhooks associated with the Actor run, which can be used to receive a notification e.g. when the Actor finished or failed. The value is a Base64-encoded JSON array of objects defining the webhooks. For more information, see [Webhooks documentation](https://docs.apify.com/platform/integrations/webhooks).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (RunResponse)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<RunResponse>> ActRunsPostWithHttpInfoAsync(string actorId, Object body, double? timeout = default(double?), double? memory = default(double?), double? maxItems = default(double?), double? maxTotalChargeUsd = default(double?), string? build = default(string?), double? waitForFinish = default(double?), string? webhooks = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'actorId' is set
            if (actorId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorId' when calling ActorsActorRunsApi->ActRunsPost");

            // verify the required parameter 'body' is set
            if (body == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'body' when calling ActorsActorRunsApi->ActRunsPost");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("actorId", Apify.SDK.Client.ClientUtils.ParameterToString(actorId)); // path parameter
            if (timeout != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "timeout", timeout));
            }
            if (memory != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "memory", memory));
            }
            if (maxItems != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxItems", maxItems));
            }
            if (maxTotalChargeUsd != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "maxTotalChargeUsd", maxTotalChargeUsd));
            }
            if (build != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "build", build));
            }
            if (waitForFinish != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "waitForFinish", waitForFinish));
            }
            if (webhooks != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "webhooks", webhooks));
            }
            localVarRequestOptions.Data = body;

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<RunResponse>("/v2/acts/{actorId}/runs", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActRunsPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

    }
}

﻿using Microsoft.Extensions.Logging;
using Phlex.Core.Apify.Interfaces;

namespace Phlex.Core.Apify.Services;

public class DownloadService(ILogger<DownloadService> logger) : IDownloadService
{
    public async Task BinaryDownloadAsync(HttpClient client, string requestTemplateUrl, string[] keys, IDownloadStorage storage, CancellationToken cancellationToken = default)
    {
        // Files to exclude from download
        List<string> excludeFiles = new List<string>
        { 
            "CRAWLEE_STATE",
            "INPUT",
            "RENDERING_TYPE_DETECTION_STATISTICS",
            "RENDERING_TYPE_PREDICTOR_STATE",
            "SDK-FILE-DOWNLOADER-STATE",
            "SDK_CRAWLER_STATISTICS_1",
            "SDK_CRAWLER_STATISTICS_2",
            "SDK_SESSION_POOL_STATE",
            "SITEMAP_REQUEST_LIST_STATE"
        };
        try
        {
            foreach (var key in keys)
            {
                if (!excludeFiles.Any(key.Contains))
                {
                    var requestUrl = requestTemplateUrl.Replace("##key##", key);
                    using (var stream = await client.GetStreamAsync(requestUrl, cancellationToken))
                    {
                        await storage.WriteDataItemAsync(key, stream, cancellationToken);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Apify: An error occurred: {ErrorMessage}", ex.Message);
        }
    }

    public async Task DownloadItemsAsync(string dataSetId, List<object> items, IDownloadStorage storage, CancellationToken cancellationToken = default)
    {
        int count = 1;
        foreach (var item in items)
        {
            if (item != null)
            {
                string? json = item.ToString();
                try
                {
                    if (json != null)
                    {
                        using (var stream = GenerateStreamFromString(json))
                        {
                            await storage.WriteDataItemAsync(dataSetId + "_" + count + ".json", stream, cancellationToken);
                        }
                    }

                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Apify: An error occurred downloading item: {Json}", json);
                }
            }
            count++;
        }
    }

    private static MemoryStream GenerateStreamFromString(string s)
    {
        var stream = new MemoryStream();
        var writer = new StreamWriter(stream);
        writer.Write(s);
        writer.Flush();
        stream.Position = 0;
        return stream;
    }
}

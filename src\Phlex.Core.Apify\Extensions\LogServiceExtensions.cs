﻿using Apify.SDK.Client;
using Microsoft.Extensions.Logging;

namespace Phlex.Core.Apify.Extensions
{
    public static class LogServiceExtensions
    {
        public static void LogApiException<T>(this ILogger<T> logger, ApiException ex) => logger.LogError(ex,
            "Apify: An error occurred, error code: {ErrorCode} message: {ErrorMessage} content: {ErrorContent}",
            ex.ErrorCode, ex.Message, ex.ErrorContent);
    }
}
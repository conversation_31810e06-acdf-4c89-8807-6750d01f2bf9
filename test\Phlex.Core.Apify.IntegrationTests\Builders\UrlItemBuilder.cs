﻿using Phlex.Core.Apify.Models;

namespace Phlex.Core.Apify.IntegrationTests.Builders;

public class UrlItemBuilder
{
    private Guid id;
    private string configurationName = string.Empty;
    private string url = string.Empty;
    private string domain = string.Empty;
    private int maxCrawlDepth;

    public static UrlItemBuilder Default() => new();

    public UrlItem Build()
    {
        return new UrlItem
        {
            Id = id,
            ConfigurationName = configurationName,
            Domain = domain,
            Url = url,
            MaxCrawlDepth = maxCrawlDepth
        };
    }

    public UrlItemBuilder WithId(Guid id)
    {
        this.id = id;
        return this;
    }

    public UrlItemBuilder WithConfigurationName(string configName)
    {
        this.configurationName = configName;
        return this;
    }

    public UrlItemBuilder WithDomain(string domain)
    {
        this.domain = domain;
        return this;
    }

    public UrlItemBuilder WithUrl(string url)
    {
        this.url = url;
        return this;
    }

    public UrlItemBuilder WithMaxCrawlDepth(int maxCrawlDepth)
    {
        this.maxCrawlDepth = maxCrawlDepth;
        return this;
    }
}

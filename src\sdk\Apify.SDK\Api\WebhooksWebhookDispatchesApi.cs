/*
 * Apify API
 *
 *  > **UPDATE 2025-01-14:** > We have rolled out this new Apify API Documentation. In case of any issues, please [report here](https://github.com/apify/apify-docs/issues). > The old API Documentation is still [available here](https://docs.apify.com/api/v2-old).  The Apify API (version 2) provides programmatic access to the [Apify platform](https://docs.apify.com). The API is organized around [RESTful](https://en.wikipedia.org/wiki/Representational_state_transfer) HTTP endpoints.  You can download the complete OpenAPI schema of Apify API in the [YAML](http://docs.apify.com/api/openapi.yaml) or [JSON](http://docs.apify.com/api/openapi.json) formats. The source code is also available on [GitHub](https://github.com/apify/apify-docs/tree/master/apify-api/openapi).  All requests and responses (including errors) are encoded in [JSON](http://www.json.org/) format with UTF-8 encoding, with a few exceptions that are explicitly described in the reference.  To access the API using [Node.js](https://nodejs.org/en/), we recommend the [`apify-client`](https://docs.apify.com/api/client/js) [NPM package](https://www.npmjs.com/package/apify-client).  To access the API using [Python](https://www.python.org/), we recommend the [`apify-client`](https://docs.apify.com/api/client/python) [PyPI package](https://pypi.org/project/apify-client/). The clients' functions correspond to the API endpoints and have the same parameters. This simplifies development of apps that depend on the Apify platform.  **Note:** All requests with JSON payloads need to specify the `Content-Type: application/json` HTTP header! All API endpoints support the `method` query parameter that can override the HTTP method. For example, if you want to call a POST endpoint using a GET request, simply add the query parameter `method=POST` to the URL and send the GET request. This feature is especially useful if you want to call Apify API endpoints from services that can only send GET requests.  ## Authentication <span id=\"/introduction/authentication\"></span>  You can find your API token on the [Integrations](https://console.apify.com/account#/integrations) page in the Apify Console.  To use your token in a request, either:  - Add the token to your request's `Authorization` header as `Bearer <token>`. E.g., `Authorization: Bearer xxxxxxx`. [More info](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Authorization). (Recommended). - Add it as the `token` parameter to your request URL. (Less secure).  Using your token in the request header is more secure than using it as a URL parameter because URLs are often stored in browser history and server logs. This creates a chance for someone unauthorized to access your API token.  **Do not share your API token or password with untrusted parties.**  For more information, see our [integrations](https://docs.apify.com/platform/integrations) documentation.  ## Basic usage <span id=\"/introduction/basic-usage\"></span>  To run an Actor, send a POST request to the [Run Actor](#/reference/actors/run-collection/run-actor) endpoint using either the Actor ID code (e.g. `vKg4IjxZbEYTYeW8T`) or its name (e.g. `janedoe~my-actor`):  `https://api.apify.com/v2/acts/[actor_id]/runs`  If the Actor is not runnable anonymously, you will receive a 401 or 403 [response code](https://developer.mozilla.org/en-US/docs/Web/HTTP/Status). This means you need to add your [secret API token](https://console.apify.com/account#/integrations) to the request's `Authorization` header ([recommended](#/introduction/authentication)) or as a URL query parameter `?token=[your_token]` (less secure).  Optionally, you can include the query parameters described in the [Run Actor](#/reference/actors/run-collection/run-actor) section to customize your run.  If you're using Node.js, the best way to run an Actor is using the `Apify.call()` method from the [Apify SDK](https://sdk.apify.com/docs/api/apify#apifycallactid-input-options). It runs the Actor using the account you are currently logged into (determined by the [secret API token](https://console.apify.com/account#/integrations)). The result is an [Actor run object](https://sdk.apify.com/docs/typedefs/actor-run) and its output (if any).  A typical workflow is as follows:  1. Run an Actor or task using the [Run Actor](#/reference/actors/run-collection/run-actor) or [Run task](#/reference/actor-tasks/run-collection/run-task) API endpoints. 2. Monitor the Actor run by periodically polling its progress using the [Get run](#/reference/actor-runs/run-object-and-its-storages/get-run) API endpoint. 3. Fetch the results from the [Get items](#/reference/datasets/item-collection/get-items) API endpoint using the `defaultDatasetId`, which you receive in the Run request response. Additional data may be stored in a key-value store. You can fetch them from the [Get record](#/reference/key-value-stores/record/get-record) API endpoint using the `defaultKeyValueStoreId` and the store's `key`.  **Note**: Instead of periodic polling, you can also run your [Actor](#/reference/actors/run-actor-synchronously) or [task](#/reference/actor-tasks/runs-collection/run-task-synchronously) synchronously. This will ensure that the request waits for 300 seconds (5 minutes) for the run to finish and returns its output. If the run takes longer, the request will time out and throw an error.  ## Response structure <span id=\"/introduction/response-structure\"></span>  Most API endpoints return a JSON object with the `data` property:  ``` {     \"data\": {         ...     } } ```  However, there are a few explicitly described exceptions, such as Dataset [Get items](#/reference/datasets/item-collection/get-items) or Key-value store [Get record](#/reference/key-value-stores/record/get-record) API endpoints, which return data in other formats. In case of an error, the response has the HTTP status code in the range of 4xx or 5xx and the `data` property is replaced with `error`. For example:  ``` {     \"error\": {         \"type\": \"record-not-found\",         \"message\": \"Store was not found.\"     } } ```  See [Errors](#/introduction/errors) for more details.  ## Pagination <span id=\"/introduction/pagination\"></span>  All API endpoints that return a list of records (e.g. [Get list of Actors](#/reference/actors/actor-collection/get-list-of-actors)) enforce pagination in order to limit the size of their responses.  Most of these API endpoints are paginated using the `offset` and `limit` query parameters. The only exception is [Get list of keys](#/reference/key-value-stores/key-collection/get-list-of-keys), which is paginated using the `exclusiveStartKey` query parameter.  **IMPORTANT**: Each API endpoint that supports pagination enforces a certain maximum value for the `limit` parameter, in order to reduce the load on Apify servers. The maximum limit could change in future so you should never rely on a specific value and check the responses of these API endpoints.  ### Using offset <span id=\"/introduction/pagination/using-offset\"></span>  Most API endpoints that return a list of records enable pagination using the following query parameters:  <table>   <tr>     <td><code>limit</code></td>     <td>Limits the response to contain a specific maximum number of items, e.g. <code>limit=20</code>.</td>   </tr>   <tr>     <td><code>offset</code></td>     <td>Skips a number of items from the beginning of the list, e.g. <code>offset=100</code>.</td>   </tr>   <tr>     <td><code>desc</code></td>     <td>     By default, items are sorted in the order in which they were created or added to the list.     This feature is useful when fetching all the items, because it ensures that items     created after the client started the pagination will not be skipped.     If you specify the <code>desc=1</code> parameter, the items will be returned in the reverse order,     i.e. from the newest to the oldest items.     </td>   </tr> </table>  The response of these API endpoints is always a JSON object with the following structure:  ``` {     \"data\": {         \"total\": 2560,         \"offset\": 250,         \"limit\": 1000,         \"count\": 1000,         \"desc\": false,         \"items\": [             { 1st object },             { 2nd object },             ...             { 1000th object }         ]     } } ```  The following table describes the meaning of the response properties:  <table>   <tr>     <th>Property</th>     <th>Description</th>   </tr>   <tr>     <td><code>total</code></td>     <td>The total number of items available in the list.</td>   </tr>   <tr>     <td><code>offset</code></td>     <td>The number of items that were skipped at the start.     This is equal to the <code>offset</code> query parameter if it was provided, otherwise it is <code>0</code>.</td>   </tr>   <tr>     <td><code>limit</code></td>     <td>The maximum number of items that can be returned in the HTTP response.     It equals to the <code>limit</code> query parameter if it was provided or     the maximum limit enforced for the particular API endpoint, whichever is smaller.</td>   </tr>   <tr>     <td><code>count</code></td>     <td>The actual number of items returned in the HTTP response.</td>   </tr>   <tr>     <td><code>desc</code></td>     <td><code>true</code> if data were requested in descending order and <code>false</code> otherwise.</td>   </tr>   <tr>     <td><code>items</code></td>     <td>An array of requested items.</td>   </tr> </table>  ### Using key <span id=\"/introduction/pagination/using-key\"></span>  The records in the [key-value store](https://docs.apify.com/platform/storage/key-value-store) are not ordered based on numerical indexes, but rather by their keys in the UTF-8 binary order. Therefore the [Get list of keys](#/reference/key-value-stores/key-collection/get-list-of-keys) API endpoint only supports pagination using the following query parameters:  <table>   <tr>     <td><code>limit</code></td>     <td>Limits the response to contain a specific maximum number items, e.g. <code>limit=20</code>.</td>   </tr>   <tr>     <td><code>exclusiveStartKey</code></td>     <td>Skips all records with keys up to the given key including the given key,     in the UTF-8 binary order.</td>   </tr> </table>  The response of the API endpoint is always a JSON object with following structure:  ``` {     \"data\": {         \"limit\": 1000,         \"isTruncated\": true,         \"exclusiveStartKey\": \"my-key\",         \"nextExclusiveStartKey\": \"some-other-key\",         \"items\": [             { 1st object },             { 2nd object },             ...             { 1000th object }         ]     } } ```  The following table describes the meaning of the response properties:  <table>   <tr>     <th>Property</th>     <th>Description</th>   </tr>   <tr>     <td><code>limit</code></td>     <td>The maximum number of items that can be returned in the HTTP response.     It equals to the <code>limit</code> query parameter if it was provided or     the maximum limit enforced for the particular endpoint, whichever is smaller.</td>   </tr>   <tr>     <td><code>isTruncated</code></td>     <td><code>true</code> if there are more items left to be queried. Otherwise <code>false</code>.</td>   </tr>   <tr>     <td><code>exclusiveStartKey</code></td>     <td>The last key that was skipped at the start. Is `null` for the first page.</td>   </tr>   <tr>     <td><code>nextExclusiveStartKey</code></td>     <td>The value for the <code>exclusiveStartKey</code> parameter to query the next page of items.</td>   </tr> </table>  ## Errors <span id=\"/introduction/errors\"></span>  The Apify API uses common HTTP status codes: `2xx` range for success, `4xx` range for errors caused by the caller (invalid requests) and `5xx` range for server errors (these are rare). Each error response contains a JSON object defining the `error` property, which is an object with the `type` and `message` properties that contain the error code and a human-readable error description, respectively.  For example:  ``` {     \"error\": {         \"type\": \"record-not-found\",         \"message\": \"Store was not found.\"     } } ```  Here is the table of the most common errors that can occur for many API endpoints:  <table>   <tr>     <th>status</th>     <th>type</th>     <th>message</th>   </tr>   <tr>     <td><code>400</code></td>     <td><code>invalid-request</code></td>     <td>POST data must be a JSON object</td>   </tr>   <tr>     <td><code>400</code></td>     <td><code>invalid-value</code></td>     <td>Invalid value provided: Comments required</td>   </tr>   <tr>     <td><code>400</code></td>     <td><code>invalid-record-key</code></td>     <td>Record key contains invalid character</td>   </tr>   <tr>     <td><code>401</code></td>     <td><code>token-not-provided</code></td>     <td>Authentication token was not provided</td>   </tr>   <tr>     <td><code>404</code></td>     <td><code>record-not-found</code></td>     <td>Store was not found</td>   </tr>   <tr>     <td><code>429</code></td>     <td><code>rate-limit-exceeded</code></td>     <td>You have exceeded the rate limit of 30 requests per second</td>   </tr>   <tr>     <td><code>405</code></td>     <td><code>method-not-allowed</code></td>     <td>This API endpoint can only be accessed using the following HTTP methods: OPTIONS, POST</td>   </tr> </table>  ## Rate limiting <span id=\"/introduction/rate-limiting\"></span>  All API endpoints limit the rate of requests in order to prevent overloading of Apify servers by misbehaving clients.  There are two kinds of rate limits - a global rate limit and a per-resource rate limit.  ### Global rate limit <span id=\"/introduction/rate-limiting/global-rate-limit\"></span>  The global rate limit is set to _250 000 requests per minute_. For [authenticated](#/introduction/authentication) requests, it is counted per user, and for unauthenticated requests, it is counted per IP address.  ### Per-resource rate limit <span id=\"/introduction/rate-limiting/per-resource-rate-limit\"></span>  The default per-resource rate limit is _30 requests per second per resource_, which in this context means a single Actor, a single Actor run, a single dataset, single key-value store etc. The default rate limit is applied to every API endpoint except a few select ones, which have higher rate limits. Each API endpoint returns its rate limit in `X-RateLimit-Limit` header.  These endpoints have a rate limit of _100 requests per second per resource_:  * CRUD ([get](#/reference/key-value-stores/record/get-record),   [put](#/reference/key-value-stores/record/put-record),   [delete](#/reference/key-value-stores/record/delete-record))   operations on key-value store records  These endpoints have a rate limit of _200 requests per second per resource_: * [Run Actor](#/reference/actors/run-collection/run-actor) * [Run Actor task asynchronously](#/reference/actor-tasks/runs-collection/run-task-asynchronously) * [Run Actor task synchronously](#/reference/actor-tasks/runs-collection/run-task-synchronously) * [Metamorph Actor run](#/reference/actors/metamorph-run/metamorph-run) * [Push items](#/reference/datasets/item-collection/put-items) to dataset * CRUD   ([add](#/reference/request-queues/request-collection/add-request),   [get](#/reference/request-queues/request-collection/get-request),   [update](#/reference/request-queues/request-collection/update-request),   [delete](#/reference/request-queues/request-collection/delete-request))   operations on requests in request queues  ### Rate limit exceeded errors <span id=\"/introduction/rate-limiting/rate-limit-exceeded-errors\"></span>  If the client is sending too many requests, the API endpoints respond with the HTTP status code `429 Too Many Requests` and the following body:  ``` {     \"error\": {         \"type\": \"rate-limit-exceeded\",         \"message\": \"You have exceeded the rate limit of ... requests per second\"     } } ```  ### Retrying rate-limited requests with exponential backoff <span id=\"/introduction/rate-limiting/retrying-rate-limited-requests-with-exponential-backoff\"></span>  If the client receives the rate limit error, it should wait a certain period of time and then retry the request. If the error happens again, the client should double the wait period and retry the request, and so on. This algorithm is known as _exponential backoff_ and it can be described using the following pseudo-code:  1. Define a variable `DELAY=500` 2. Send the HTTP request to the API endpoint 3. If the response has status code not equal to `429` then you are done. Otherwise:    * Wait for a period of time chosen randomly from the interval `DELAY` to `2*DELAY` milliseconds    * Double the future wait period by setting `DELAY = 2*DELAY`    * Continue with step 2  If all requests sent by the client implement the above steps, the client will automatically use the maximum available bandwidth for its requests.  Note that the Apify API clients [for JavaScript](https://docs.apify.com/api/client/js) and [for Python](https://docs.apify.com/api/client/python) use the exponential backoff algorithm transparently, so that you do not need to worry about it.  ## Referring to resources <span id=\"/introduction/referring-to-resources\"></span>  There are three main ways to refer to a resource you're accessing via API.  - the resource ID (e.g. `iKkPcIgVvwmztduf8`) - `username~resourcename` - when using this access method, you will need to use your API token, and access will only work if you have the correct permissions. - `~resourcename` - for this, you need to use an API token, and the `resourcename` refers to a resource in the API token owner's account. 
 *
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Mime;
using Apify.SDK.Client;
using Apify.SDK.Model;

namespace Apify.SDK.Api
{

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IWebhooksWebhookDispatchesApiSync : IApiAccessor
    {
        #region Synchronous Operations
        /// <summary>
        /// Get webhook dispatch
        /// </summary>
        /// <remarks>
        /// Gets webhook dispatch object with all details.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="dispatchId">Webhook dispatch ID.</param>
        /// <returns>GetWebhookDispatchResponse</returns>
        GetWebhookDispatchResponse WebhookDispatchGet(string dispatchId);

        /// <summary>
        /// Get webhook dispatch
        /// </summary>
        /// <remarks>
        /// Gets webhook dispatch object with all details.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="dispatchId">Webhook dispatch ID.</param>
        /// <returns>ApiResponse of GetWebhookDispatchResponse</returns>
        ApiResponse<GetWebhookDispatchResponse> WebhookDispatchGetWithHttpInfo(string dispatchId);
        /// <summary>
        /// Get list of webhook dispatches
        /// </summary>
        /// <remarks>
        /// Gets the list of webhook dispatches that the user have.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 records. By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of records that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of records to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <returns>WebhookDispatchList</returns>
        WebhookDispatchList WebhookDispatchesGet(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?));

        /// <summary>
        /// Get list of webhook dispatches
        /// </summary>
        /// <remarks>
        /// Gets the list of webhook dispatches that the user have.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 records. By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of records that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of records to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <returns>ApiResponse of WebhookDispatchList</returns>
        ApiResponse<WebhookDispatchList> WebhookDispatchesGetWithHttpInfo(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?));
        #endregion Synchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IWebhooksWebhookDispatchesApiAsync : IApiAccessor
    {
        #region Asynchronous Operations
        /// <summary>
        /// Get webhook dispatch
        /// </summary>
        /// <remarks>
        /// Gets webhook dispatch object with all details.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="dispatchId">Webhook dispatch ID.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of GetWebhookDispatchResponse</returns>
        System.Threading.Tasks.Task<GetWebhookDispatchResponse> WebhookDispatchGetAsync(string dispatchId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Get webhook dispatch
        /// </summary>
        /// <remarks>
        /// Gets webhook dispatch object with all details.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="dispatchId">Webhook dispatch ID.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (GetWebhookDispatchResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<GetWebhookDispatchResponse>> WebhookDispatchGetWithHttpInfoAsync(string dispatchId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Get list of webhook dispatches
        /// </summary>
        /// <remarks>
        /// Gets the list of webhook dispatches that the user have.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 records. By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of records that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of records to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of WebhookDispatchList</returns>
        System.Threading.Tasks.Task<WebhookDispatchList> WebhookDispatchesGetAsync(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Get list of webhook dispatches
        /// </summary>
        /// <remarks>
        /// Gets the list of webhook dispatches that the user have.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 records. By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of records that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of records to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (WebhookDispatchList)</returns>
        System.Threading.Tasks.Task<ApiResponse<WebhookDispatchList>> WebhookDispatchesGetWithHttpInfoAsync(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        #endregion Asynchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IWebhooksWebhookDispatchesApi : IWebhooksWebhookDispatchesApiSync, IWebhooksWebhookDispatchesApiAsync
    {

    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public partial class WebhooksWebhookDispatchesApi : IDisposable, IWebhooksWebhookDispatchesApi
    {
        private Apify.SDK.Client.ExceptionFactory _exceptionFactory = (name, response) => null;

        /// <summary>
        /// Initializes a new instance of the <see cref="WebhooksWebhookDispatchesApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <returns></returns>
        public WebhooksWebhookDispatchesApi() : this((string)null)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="WebhooksWebhookDispatchesApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        public WebhooksWebhookDispatchesApi(string basePath)
        {
            this.Configuration = Apify.SDK.Client.Configuration.MergeConfigurations(
                Apify.SDK.Client.GlobalConfiguration.Instance,
                new Apify.SDK.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Apify.SDK.Client.ApiClient(this.Configuration.BasePath);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Apify.SDK.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="WebhooksWebhookDispatchesApi"/> class using Configuration object.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        public WebhooksWebhookDispatchesApi(Apify.SDK.Client.Configuration configuration)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Configuration = Apify.SDK.Client.Configuration.MergeConfigurations(
                Apify.SDK.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Apify.SDK.Client.ApiClient(this.Configuration.BasePath);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Apify.SDK.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="WebhooksWebhookDispatchesApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public WebhooksWebhookDispatchesApi(HttpClient client, HttpClientHandler handler = null) : this(client, (string)null, handler)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="WebhooksWebhookDispatchesApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public WebhooksWebhookDispatchesApi(HttpClient client, string basePath, HttpClientHandler handler = null)
        {
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Apify.SDK.Client.Configuration.MergeConfigurations(
                Apify.SDK.Client.GlobalConfiguration.Instance,
                new Apify.SDK.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Apify.SDK.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Apify.SDK.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="WebhooksWebhookDispatchesApi"/> class using Configuration object.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public WebhooksWebhookDispatchesApi(HttpClient client, Apify.SDK.Client.Configuration configuration, HttpClientHandler handler = null)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Apify.SDK.Client.Configuration.MergeConfigurations(
                Apify.SDK.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Apify.SDK.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Apify.SDK.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="WebhooksWebhookDispatchesApi"/> class
        /// using a Configuration object and client instance.
        /// </summary>
        /// <param name="client">The client interface for synchronous API access.</param>
        /// <param name="asyncClient">The client interface for asynchronous API access.</param>
        /// <param name="configuration">The configuration object.</param>
        /// <exception cref="ArgumentNullException"></exception>
        public WebhooksWebhookDispatchesApi(Apify.SDK.Client.ISynchronousClient client, Apify.SDK.Client.IAsynchronousClient asyncClient, Apify.SDK.Client.IReadableConfiguration configuration)
        {
            if (client == null) throw new ArgumentNullException("client");
            if (asyncClient == null) throw new ArgumentNullException("asyncClient");
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Client = client;
            this.AsynchronousClient = asyncClient;
            this.Configuration = configuration;
            this.ExceptionFactory = Apify.SDK.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Disposes resources if they were created by us
        /// </summary>
        public void Dispose()
        {
            this.ApiClient?.Dispose();
        }

        /// <summary>
        /// Holds the ApiClient if created
        /// </summary>
        public Apify.SDK.Client.ApiClient ApiClient { get; set; } = null;

        /// <summary>
        /// The client for accessing this underlying API asynchronously.
        /// </summary>
        public Apify.SDK.Client.IAsynchronousClient AsynchronousClient { get; set; }

        /// <summary>
        /// The client for accessing this underlying API synchronously.
        /// </summary>
        public Apify.SDK.Client.ISynchronousClient Client { get; set; }

        /// <summary>
        /// Gets the base path of the API client.
        /// </summary>
        /// <value>The base path</value>
        public string GetBasePath()
        {
            return this.Configuration.BasePath;
        }

        /// <summary>
        /// Gets or sets the configuration object
        /// </summary>
        /// <value>An instance of the Configuration</value>
        public Apify.SDK.Client.IReadableConfiguration Configuration { get; set; }

        /// <summary>
        /// Provides a factory method hook for the creation of exceptions.
        /// </summary>
        public Apify.SDK.Client.ExceptionFactory ExceptionFactory
        {
            get
            {
                if (_exceptionFactory != null && _exceptionFactory.GetInvocationList().Length > 1)
                {
                    throw new InvalidOperationException("Multicast delegate for ExceptionFactory is unsupported.");
                }
                return _exceptionFactory;
            }
            set { _exceptionFactory = value; }
        }

        /// <summary>
        /// Get webhook dispatch Gets webhook dispatch object with all details.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="dispatchId">Webhook dispatch ID.</param>
        /// <returns>GetWebhookDispatchResponse</returns>
        public GetWebhookDispatchResponse WebhookDispatchGet(string dispatchId)
        {
            Apify.SDK.Client.ApiResponse<GetWebhookDispatchResponse> localVarResponse = WebhookDispatchGetWithHttpInfo(dispatchId);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get webhook dispatch Gets webhook dispatch object with all details.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="dispatchId">Webhook dispatch ID.</param>
        /// <returns>ApiResponse of GetWebhookDispatchResponse</returns>
        public Apify.SDK.Client.ApiResponse<GetWebhookDispatchResponse> WebhookDispatchGetWithHttpInfo(string dispatchId)
        {
            // verify the required parameter 'dispatchId' is set
            if (dispatchId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'dispatchId' when calling WebhooksWebhookDispatchesApi->WebhookDispatchGet");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("dispatchId", Apify.SDK.Client.ClientUtils.ParameterToString(dispatchId)); // path parameter

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<GetWebhookDispatchResponse>("/v2/webhook-dispatches/{dispatchId}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("WebhookDispatchGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get webhook dispatch Gets webhook dispatch object with all details.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="dispatchId">Webhook dispatch ID.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of GetWebhookDispatchResponse</returns>
        public async System.Threading.Tasks.Task<GetWebhookDispatchResponse> WebhookDispatchGetAsync(string dispatchId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<GetWebhookDispatchResponse> localVarResponse = await WebhookDispatchGetWithHttpInfoAsync(dispatchId, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get webhook dispatch Gets webhook dispatch object with all details.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="dispatchId">Webhook dispatch ID.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (GetWebhookDispatchResponse)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<GetWebhookDispatchResponse>> WebhookDispatchGetWithHttpInfoAsync(string dispatchId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'dispatchId' is set
            if (dispatchId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'dispatchId' when calling WebhooksWebhookDispatchesApi->WebhookDispatchGet");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("dispatchId", Apify.SDK.Client.ClientUtils.ParameterToString(dispatchId)); // path parameter

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<GetWebhookDispatchResponse>("/v2/webhook-dispatches/{dispatchId}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("WebhookDispatchGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get list of webhook dispatches Gets the list of webhook dispatches that the user have.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 records. By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of records that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of records to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <returns>WebhookDispatchList</returns>
        public WebhookDispatchList WebhookDispatchesGet(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?))
        {
            Apify.SDK.Client.ApiResponse<WebhookDispatchList> localVarResponse = WebhookDispatchesGetWithHttpInfo(offset, limit, desc);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get list of webhook dispatches Gets the list of webhook dispatches that the user have.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 records. By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of records that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of records to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <returns>ApiResponse of WebhookDispatchList</returns>
        public Apify.SDK.Client.ApiResponse<WebhookDispatchList> WebhookDispatchesGetWithHttpInfo(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?))
        {
            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (desc != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "desc", desc));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<WebhookDispatchList>("/v2/webhook-dispatches", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("WebhookDispatchesGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get list of webhook dispatches Gets the list of webhook dispatches that the user have.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 records. By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of records that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of records to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of WebhookDispatchList</returns>
        public async System.Threading.Tasks.Task<WebhookDispatchList> WebhookDispatchesGetAsync(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<WebhookDispatchList> localVarResponse = await WebhookDispatchesGetWithHttpInfoAsync(offset, limit, desc, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get list of webhook dispatches Gets the list of webhook dispatches that the user have.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 records. By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of records that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of records to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (WebhookDispatchList)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<WebhookDispatchList>> WebhookDispatchesGetWithHttpInfoAsync(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (desc != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "desc", desc));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<WebhookDispatchList>("/v2/webhook-dispatches", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("WebhookDispatchesGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

    }
}

/*
 * Apify API
 *
 *  > **UPDATE 2025-01-14:** > We have rolled out this new Apify API Documentation. In case of any issues, please [report here](https://github.com/apify/apify-docs/issues). > The old API Documentation is still [available here](https://docs.apify.com/api/v2-old).  The Apify API (version 2) provides programmatic access to the [Apify platform](https://docs.apify.com). The API is organized around [RESTful](https://en.wikipedia.org/wiki/Representational_state_transfer) HTTP endpoints.  You can download the complete OpenAPI schema of Apify API in the [YAML](http://docs.apify.com/api/openapi.yaml) or [JSON](http://docs.apify.com/api/openapi.json) formats. The source code is also available on [GitHub](https://github.com/apify/apify-docs/tree/master/apify-api/openapi).  All requests and responses (including errors) are encoded in [JSON](http://www.json.org/) format with UTF-8 encoding, with a few exceptions that are explicitly described in the reference.  To access the API using [Node.js](https://nodejs.org/en/), we recommend the [`apify-client`](https://docs.apify.com/api/client/js) [NPM package](https://www.npmjs.com/package/apify-client).  To access the API using [Python](https://www.python.org/), we recommend the [`apify-client`](https://docs.apify.com/api/client/python) [PyPI package](https://pypi.org/project/apify-client/). The clients' functions correspond to the API endpoints and have the same parameters. This simplifies development of apps that depend on the Apify platform.  **Note:** All requests with JSON payloads need to specify the `Content-Type: application/json` HTTP header! All API endpoints support the `method` query parameter that can override the HTTP method. For example, if you want to call a POST endpoint using a GET request, simply add the query parameter `method=POST` to the URL and send the GET request. This feature is especially useful if you want to call Apify API endpoints from services that can only send GET requests.  ## Authentication <span id=\"/introduction/authentication\"></span>  You can find your API token on the [Integrations](https://console.apify.com/account#/integrations) page in the Apify Console.  To use your token in a request, either:  - Add the token to your request's `Authorization` header as `Bearer <token>`. E.g., `Authorization: Bearer xxxxxxx`. [More info](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Authorization). (Recommended). - Add it as the `token` parameter to your request URL. (Less secure).  Using your token in the request header is more secure than using it as a URL parameter because URLs are often stored in browser history and server logs. This creates a chance for someone unauthorized to access your API token.  **Do not share your API token or password with untrusted parties.**  For more information, see our [integrations](https://docs.apify.com/platform/integrations) documentation.  ## Basic usage <span id=\"/introduction/basic-usage\"></span>  To run an Actor, send a POST request to the [Run Actor](#/reference/actors/run-collection/run-actor) endpoint using either the Actor ID code (e.g. `vKg4IjxZbEYTYeW8T`) or its name (e.g. `janedoe~my-actor`):  `https://api.apify.com/v2/acts/[actor_id]/runs`  If the Actor is not runnable anonymously, you will receive a 401 or 403 [response code](https://developer.mozilla.org/en-US/docs/Web/HTTP/Status). This means you need to add your [secret API token](https://console.apify.com/account#/integrations) to the request's `Authorization` header ([recommended](#/introduction/authentication)) or as a URL query parameter `?token=[your_token]` (less secure).  Optionally, you can include the query parameters described in the [Run Actor](#/reference/actors/run-collection/run-actor) section to customize your run.  If you're using Node.js, the best way to run an Actor is using the `Apify.call()` method from the [Apify SDK](https://sdk.apify.com/docs/api/apify#apifycallactid-input-options). It runs the Actor using the account you are currently logged into (determined by the [secret API token](https://console.apify.com/account#/integrations)). The result is an [Actor run object](https://sdk.apify.com/docs/typedefs/actor-run) and its output (if any).  A typical workflow is as follows:  1. Run an Actor or task using the [Run Actor](#/reference/actors/run-collection/run-actor) or [Run task](#/reference/actor-tasks/run-collection/run-task) API endpoints. 2. Monitor the Actor run by periodically polling its progress using the [Get run](#/reference/actor-runs/run-object-and-its-storages/get-run) API endpoint. 3. Fetch the results from the [Get items](#/reference/datasets/item-collection/get-items) API endpoint using the `defaultDatasetId`, which you receive in the Run request response. Additional data may be stored in a key-value store. You can fetch them from the [Get record](#/reference/key-value-stores/record/get-record) API endpoint using the `defaultKeyValueStoreId` and the store's `key`.  **Note**: Instead of periodic polling, you can also run your [Actor](#/reference/actors/run-actor-synchronously) or [task](#/reference/actor-tasks/runs-collection/run-task-synchronously) synchronously. This will ensure that the request waits for 300 seconds (5 minutes) for the run to finish and returns its output. If the run takes longer, the request will time out and throw an error.  ## Response structure <span id=\"/introduction/response-structure\"></span>  Most API endpoints return a JSON object with the `data` property:  ``` {     \"data\": {         ...     } } ```  However, there are a few explicitly described exceptions, such as Dataset [Get items](#/reference/datasets/item-collection/get-items) or Key-value store [Get record](#/reference/key-value-stores/record/get-record) API endpoints, which return data in other formats. In case of an error, the response has the HTTP status code in the range of 4xx or 5xx and the `data` property is replaced with `error`. For example:  ``` {     \"error\": {         \"type\": \"record-not-found\",         \"message\": \"Store was not found.\"     } } ```  See [Errors](#/introduction/errors) for more details.  ## Pagination <span id=\"/introduction/pagination\"></span>  All API endpoints that return a list of records (e.g. [Get list of Actors](#/reference/actors/actor-collection/get-list-of-actors)) enforce pagination in order to limit the size of their responses.  Most of these API endpoints are paginated using the `offset` and `limit` query parameters. The only exception is [Get list of keys](#/reference/key-value-stores/key-collection/get-list-of-keys), which is paginated using the `exclusiveStartKey` query parameter.  **IMPORTANT**: Each API endpoint that supports pagination enforces a certain maximum value for the `limit` parameter, in order to reduce the load on Apify servers. The maximum limit could change in future so you should never rely on a specific value and check the responses of these API endpoints.  ### Using offset <span id=\"/introduction/pagination/using-offset\"></span>  Most API endpoints that return a list of records enable pagination using the following query parameters:  <table>   <tr>     <td><code>limit</code></td>     <td>Limits the response to contain a specific maximum number of items, e.g. <code>limit=20</code>.</td>   </tr>   <tr>     <td><code>offset</code></td>     <td>Skips a number of items from the beginning of the list, e.g. <code>offset=100</code>.</td>   </tr>   <tr>     <td><code>desc</code></td>     <td>     By default, items are sorted in the order in which they were created or added to the list.     This feature is useful when fetching all the items, because it ensures that items     created after the client started the pagination will not be skipped.     If you specify the <code>desc=1</code> parameter, the items will be returned in the reverse order,     i.e. from the newest to the oldest items.     </td>   </tr> </table>  The response of these API endpoints is always a JSON object with the following structure:  ``` {     \"data\": {         \"total\": 2560,         \"offset\": 250,         \"limit\": 1000,         \"count\": 1000,         \"desc\": false,         \"items\": [             { 1st object },             { 2nd object },             ...             { 1000th object }         ]     } } ```  The following table describes the meaning of the response properties:  <table>   <tr>     <th>Property</th>     <th>Description</th>   </tr>   <tr>     <td><code>total</code></td>     <td>The total number of items available in the list.</td>   </tr>   <tr>     <td><code>offset</code></td>     <td>The number of items that were skipped at the start.     This is equal to the <code>offset</code> query parameter if it was provided, otherwise it is <code>0</code>.</td>   </tr>   <tr>     <td><code>limit</code></td>     <td>The maximum number of items that can be returned in the HTTP response.     It equals to the <code>limit</code> query parameter if it was provided or     the maximum limit enforced for the particular API endpoint, whichever is smaller.</td>   </tr>   <tr>     <td><code>count</code></td>     <td>The actual number of items returned in the HTTP response.</td>   </tr>   <tr>     <td><code>desc</code></td>     <td><code>true</code> if data were requested in descending order and <code>false</code> otherwise.</td>   </tr>   <tr>     <td><code>items</code></td>     <td>An array of requested items.</td>   </tr> </table>  ### Using key <span id=\"/introduction/pagination/using-key\"></span>  The records in the [key-value store](https://docs.apify.com/platform/storage/key-value-store) are not ordered based on numerical indexes, but rather by their keys in the UTF-8 binary order. Therefore the [Get list of keys](#/reference/key-value-stores/key-collection/get-list-of-keys) API endpoint only supports pagination using the following query parameters:  <table>   <tr>     <td><code>limit</code></td>     <td>Limits the response to contain a specific maximum number items, e.g. <code>limit=20</code>.</td>   </tr>   <tr>     <td><code>exclusiveStartKey</code></td>     <td>Skips all records with keys up to the given key including the given key,     in the UTF-8 binary order.</td>   </tr> </table>  The response of the API endpoint is always a JSON object with following structure:  ``` {     \"data\": {         \"limit\": 1000,         \"isTruncated\": true,         \"exclusiveStartKey\": \"my-key\",         \"nextExclusiveStartKey\": \"some-other-key\",         \"items\": [             { 1st object },             { 2nd object },             ...             { 1000th object }         ]     } } ```  The following table describes the meaning of the response properties:  <table>   <tr>     <th>Property</th>     <th>Description</th>   </tr>   <tr>     <td><code>limit</code></td>     <td>The maximum number of items that can be returned in the HTTP response.     It equals to the <code>limit</code> query parameter if it was provided or     the maximum limit enforced for the particular endpoint, whichever is smaller.</td>   </tr>   <tr>     <td><code>isTruncated</code></td>     <td><code>true</code> if there are more items left to be queried. Otherwise <code>false</code>.</td>   </tr>   <tr>     <td><code>exclusiveStartKey</code></td>     <td>The last key that was skipped at the start. Is `null` for the first page.</td>   </tr>   <tr>     <td><code>nextExclusiveStartKey</code></td>     <td>The value for the <code>exclusiveStartKey</code> parameter to query the next page of items.</td>   </tr> </table>  ## Errors <span id=\"/introduction/errors\"></span>  The Apify API uses common HTTP status codes: `2xx` range for success, `4xx` range for errors caused by the caller (invalid requests) and `5xx` range for server errors (these are rare). Each error response contains a JSON object defining the `error` property, which is an object with the `type` and `message` properties that contain the error code and a human-readable error description, respectively.  For example:  ``` {     \"error\": {         \"type\": \"record-not-found\",         \"message\": \"Store was not found.\"     } } ```  Here is the table of the most common errors that can occur for many API endpoints:  <table>   <tr>     <th>status</th>     <th>type</th>     <th>message</th>   </tr>   <tr>     <td><code>400</code></td>     <td><code>invalid-request</code></td>     <td>POST data must be a JSON object</td>   </tr>   <tr>     <td><code>400</code></td>     <td><code>invalid-value</code></td>     <td>Invalid value provided: Comments required</td>   </tr>   <tr>     <td><code>400</code></td>     <td><code>invalid-record-key</code></td>     <td>Record key contains invalid character</td>   </tr>   <tr>     <td><code>401</code></td>     <td><code>token-not-provided</code></td>     <td>Authentication token was not provided</td>   </tr>   <tr>     <td><code>404</code></td>     <td><code>record-not-found</code></td>     <td>Store was not found</td>   </tr>   <tr>     <td><code>429</code></td>     <td><code>rate-limit-exceeded</code></td>     <td>You have exceeded the rate limit of 30 requests per second</td>   </tr>   <tr>     <td><code>405</code></td>     <td><code>method-not-allowed</code></td>     <td>This API endpoint can only be accessed using the following HTTP methods: OPTIONS, POST</td>   </tr> </table>  ## Rate limiting <span id=\"/introduction/rate-limiting\"></span>  All API endpoints limit the rate of requests in order to prevent overloading of Apify servers by misbehaving clients.  There are two kinds of rate limits - a global rate limit and a per-resource rate limit.  ### Global rate limit <span id=\"/introduction/rate-limiting/global-rate-limit\"></span>  The global rate limit is set to _250 000 requests per minute_. For [authenticated](#/introduction/authentication) requests, it is counted per user, and for unauthenticated requests, it is counted per IP address.  ### Per-resource rate limit <span id=\"/introduction/rate-limiting/per-resource-rate-limit\"></span>  The default per-resource rate limit is _30 requests per second per resource_, which in this context means a single Actor, a single Actor run, a single dataset, single key-value store etc. The default rate limit is applied to every API endpoint except a few select ones, which have higher rate limits. Each API endpoint returns its rate limit in `X-RateLimit-Limit` header.  These endpoints have a rate limit of _100 requests per second per resource_:  * CRUD ([get](#/reference/key-value-stores/record/get-record),   [put](#/reference/key-value-stores/record/put-record),   [delete](#/reference/key-value-stores/record/delete-record))   operations on key-value store records  These endpoints have a rate limit of _200 requests per second per resource_: * [Run Actor](#/reference/actors/run-collection/run-actor) * [Run Actor task asynchronously](#/reference/actor-tasks/runs-collection/run-task-asynchronously) * [Run Actor task synchronously](#/reference/actor-tasks/runs-collection/run-task-synchronously) * [Metamorph Actor run](#/reference/actors/metamorph-run/metamorph-run) * [Push items](#/reference/datasets/item-collection/put-items) to dataset * CRUD   ([add](#/reference/request-queues/request-collection/add-request),   [get](#/reference/request-queues/request-collection/get-request),   [update](#/reference/request-queues/request-collection/update-request),   [delete](#/reference/request-queues/request-collection/delete-request))   operations on requests in request queues  ### Rate limit exceeded errors <span id=\"/introduction/rate-limiting/rate-limit-exceeded-errors\"></span>  If the client is sending too many requests, the API endpoints respond with the HTTP status code `429 Too Many Requests` and the following body:  ``` {     \"error\": {         \"type\": \"rate-limit-exceeded\",         \"message\": \"You have exceeded the rate limit of ... requests per second\"     } } ```  ### Retrying rate-limited requests with exponential backoff <span id=\"/introduction/rate-limiting/retrying-rate-limited-requests-with-exponential-backoff\"></span>  If the client receives the rate limit error, it should wait a certain period of time and then retry the request. If the error happens again, the client should double the wait period and retry the request, and so on. This algorithm is known as _exponential backoff_ and it can be described using the following pseudo-code:  1. Define a variable `DELAY=500` 2. Send the HTTP request to the API endpoint 3. If the response has status code not equal to `429` then you are done. Otherwise:    * Wait for a period of time chosen randomly from the interval `DELAY` to `2*DELAY` milliseconds    * Double the future wait period by setting `DELAY = 2*DELAY`    * Continue with step 2  If all requests sent by the client implement the above steps, the client will automatically use the maximum available bandwidth for its requests.  Note that the Apify API clients [for JavaScript](https://docs.apify.com/api/client/js) and [for Python](https://docs.apify.com/api/client/python) use the exponential backoff algorithm transparently, so that you do not need to worry about it.  ## Referring to resources <span id=\"/introduction/referring-to-resources\"></span>  There are three main ways to refer to a resource you're accessing via API.  - the resource ID (e.g. `iKkPcIgVvwmztduf8`) - `username~resourcename` - when using this access method, you will need to use your API token, and access will only work if you have the correct permissions. - `~resourcename` - for this, you need to use an API token, and the `resourcename` refers to a resource in the API token owner's account. 
 *
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Apify.SDK.Client.FileParameter;
using OpenAPIDateConverter = Apify.SDK.Client.OpenAPIDateConverter;

namespace Apify.SDK.Model
{
    /// <summary>
    /// RunUsage
    /// </summary>
    [DataContract(Name = "Run_usage")]
    public partial class RunUsage : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="RunUsage" /> class.
        /// </summary>
        /// <param name="aCTORCOMPUTEUNITS">aCTORCOMPUTEUNITS.</param>
        /// <param name="dATASETREADS">dATASETREADS.</param>
        /// <param name="dATASETWRITES">dATASETWRITES.</param>
        /// <param name="kEYVALUESTOREREADS">kEYVALUESTOREREADS.</param>
        /// <param name="kEYVALUESTOREWRITES">kEYVALUESTOREWRITES.</param>
        /// <param name="kEYVALUESTORELISTS">kEYVALUESTORELISTS.</param>
        /// <param name="rEQUESTQUEUEREADS">rEQUESTQUEUEREADS.</param>
        /// <param name="rEQUESTQUEUEWRITES">rEQUESTQUEUEWRITES.</param>
        /// <param name="dATATRANSFERINTERNALGBYTES">dATATRANSFERINTERNALGBYTES.</param>
        /// <param name="dATATRANSFEREXTERNALGBYTES">dATATRANSFEREXTERNALGBYTES.</param>
        /// <param name="pROXYRESIDENTIALTRANSFERGBYTES">pROXYRESIDENTIALTRANSFERGBYTES.</param>
        /// <param name="pROXYSERPS">pROXYSERPS.</param>
        public RunUsage(decimal? aCTORCOMPUTEUNITS = default(decimal?), decimal? dATASETREADS = default(decimal?), decimal? dATASETWRITES = default(decimal?), decimal? kEYVALUESTOREREADS = default(decimal?), decimal? kEYVALUESTOREWRITES = default(decimal?), decimal? kEYVALUESTORELISTS = default(decimal?), decimal? rEQUESTQUEUEREADS = default(decimal?), decimal? rEQUESTQUEUEWRITES = default(decimal?), decimal? dATATRANSFERINTERNALGBYTES = default(decimal?), decimal? dATATRANSFEREXTERNALGBYTES = default(decimal?), decimal? pROXYRESIDENTIALTRANSFERGBYTES = default(decimal?), decimal? pROXYSERPS = default(decimal?))
        {
            this.ACTOR_COMPUTE_UNITS = aCTORCOMPUTEUNITS;
            this.DATASET_READS = dATASETREADS;
            this.DATASET_WRITES = dATASETWRITES;
            this.KEY_VALUE_STORE_READS = kEYVALUESTOREREADS;
            this.KEY_VALUE_STORE_WRITES = kEYVALUESTOREWRITES;
            this.KEY_VALUE_STORE_LISTS = kEYVALUESTORELISTS;
            this.REQUEST_QUEUE_READS = rEQUESTQUEUEREADS;
            this.REQUEST_QUEUE_WRITES = rEQUESTQUEUEWRITES;
            this.DATA_TRANSFER_INTERNAL_GBYTES = dATATRANSFERINTERNALGBYTES;
            this.DATA_TRANSFER_EXTERNAL_GBYTES = dATATRANSFEREXTERNALGBYTES;
            this.PROXY_RESIDENTIAL_TRANSFER_GBYTES = pROXYRESIDENTIALTRANSFERGBYTES;
            this.PROXY_SERPS = pROXYSERPS;
        }

        /// <summary>
        /// Gets or Sets ACTOR_COMPUTE_UNITS
        /// </summary>
        /*
        <example>3</example>
        */
        [DataMember(Name = "ACTOR_COMPUTE_UNITS", EmitDefaultValue = true)]
        public decimal? ACTOR_COMPUTE_UNITS { get; set; }

        /// <summary>
        /// Gets or Sets DATASET_READS
        /// </summary>
        /*
        <example>4</example>
        */
        [DataMember(Name = "DATASET_READS", EmitDefaultValue = true)]
        public decimal? DATASET_READS { get; set; }

        /// <summary>
        /// Gets or Sets DATASET_WRITES
        /// </summary>
        /*
        <example>4</example>
        */
        [DataMember(Name = "DATASET_WRITES", EmitDefaultValue = true)]
        public decimal? DATASET_WRITES { get; set; }

        /// <summary>
        /// Gets or Sets KEY_VALUE_STORE_READS
        /// </summary>
        /*
        <example>5</example>
        */
        [DataMember(Name = "KEY_VALUE_STORE_READS", EmitDefaultValue = true)]
        public decimal? KEY_VALUE_STORE_READS { get; set; }

        /// <summary>
        /// Gets or Sets KEY_VALUE_STORE_WRITES
        /// </summary>
        /*
        <example>3</example>
        */
        [DataMember(Name = "KEY_VALUE_STORE_WRITES", EmitDefaultValue = true)]
        public decimal? KEY_VALUE_STORE_WRITES { get; set; }

        /// <summary>
        /// Gets or Sets KEY_VALUE_STORE_LISTS
        /// </summary>
        /*
        <example>5</example>
        */
        [DataMember(Name = "KEY_VALUE_STORE_LISTS", EmitDefaultValue = true)]
        public decimal? KEY_VALUE_STORE_LISTS { get; set; }

        /// <summary>
        /// Gets or Sets REQUEST_QUEUE_READS
        /// </summary>
        /*
        <example>2</example>
        */
        [DataMember(Name = "REQUEST_QUEUE_READS", EmitDefaultValue = true)]
        public decimal? REQUEST_QUEUE_READS { get; set; }

        /// <summary>
        /// Gets or Sets REQUEST_QUEUE_WRITES
        /// </summary>
        /*
        <example>1</example>
        */
        [DataMember(Name = "REQUEST_QUEUE_WRITES", EmitDefaultValue = true)]
        public decimal? REQUEST_QUEUE_WRITES { get; set; }

        /// <summary>
        /// Gets or Sets DATA_TRANSFER_INTERNAL_GBYTES
        /// </summary>
        /*
        <example>1</example>
        */
        [DataMember(Name = "DATA_TRANSFER_INTERNAL_GBYTES", EmitDefaultValue = true)]
        public decimal? DATA_TRANSFER_INTERNAL_GBYTES { get; set; }

        /// <summary>
        /// Gets or Sets DATA_TRANSFER_EXTERNAL_GBYTES
        /// </summary>
        /*
        <example>3</example>
        */
        [DataMember(Name = "DATA_TRANSFER_EXTERNAL_GBYTES?", EmitDefaultValue = true)]
        public decimal? DATA_TRANSFER_EXTERNAL_GBYTES { get; set; }

        /// <summary>
        /// Gets or Sets PROXY_RESIDENTIAL_TRANSFER_GBYTES
        /// </summary>
        /*
        <example>34</example>
        */
        [DataMember(Name = "PROXY_RESIDENTIAL_TRANSFER_GBYTES", EmitDefaultValue = true)]
        public decimal? PROXY_RESIDENTIAL_TRANSFER_GBYTES { get; set; }

        /// <summary>
        /// Gets or Sets PROXY_SERPS
        /// </summary>
        /*
        <example>3</example>
        */
        [DataMember(Name = "PROXY_SERPS", EmitDefaultValue = true)]
        public decimal? PROXY_SERPS { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class RunUsage {\n");
            sb.Append("  ACTOR_COMPUTE_UNITS: ").Append(ACTOR_COMPUTE_UNITS).Append("\n");
            sb.Append("  DATASET_READS: ").Append(DATASET_READS).Append("\n");
            sb.Append("  DATASET_WRITES: ").Append(DATASET_WRITES).Append("\n");
            sb.Append("  KEY_VALUE_STORE_READS: ").Append(KEY_VALUE_STORE_READS).Append("\n");
            sb.Append("  KEY_VALUE_STORE_WRITES: ").Append(KEY_VALUE_STORE_WRITES).Append("\n");
            sb.Append("  KEY_VALUE_STORE_LISTS: ").Append(KEY_VALUE_STORE_LISTS).Append("\n");
            sb.Append("  REQUEST_QUEUE_READS: ").Append(REQUEST_QUEUE_READS).Append("\n");
            sb.Append("  REQUEST_QUEUE_WRITES: ").Append(REQUEST_QUEUE_WRITES).Append("\n");
            sb.Append("  DATA_TRANSFER_INTERNAL_GBYTES: ").Append(DATA_TRANSFER_INTERNAL_GBYTES).Append("\n");
            sb.Append("  DATA_TRANSFER_EXTERNAL_GBYTES: ").Append(DATA_TRANSFER_EXTERNAL_GBYTES).Append("\n");
            sb.Append("  PROXY_RESIDENTIAL_TRANSFER_GBYTES: ").Append(PROXY_RESIDENTIAL_TRANSFER_GBYTES).Append("\n");
            sb.Append("  PROXY_SERPS: ").Append(PROXY_SERPS).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            yield break;
        }
    }

}

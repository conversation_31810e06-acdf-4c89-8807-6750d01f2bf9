﻿/*
 * Website Content Crawler
 *
 * Crawl websites and extract text content to feed AI models, LLM applications, vector databases, or RAG pipelines. The Actor supports rich formatting using Markdown, cleans the HTML, downloads files, and integrates well with 🦜🔗 LangChain, LlamaIndex, and the wider LLM ecosystem.
 *
 * The version of the OpenAPI document: v1.0
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.IO;
using System.Runtime.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Linq;
using System.ComponentModel.DataAnnotations;
using FileParameter = Apify.SDK.Client.FileParameter;
using OpenAPIDateConverter = Apify.SDK.Client.OpenAPIDateConverter;

namespace Apify.SDK.Actors.WebsiteContentCrawler
{
    /// <summary>
    /// IncludeURLsGlobsInner
    /// </summary>
    [DataContract(Name = "Include_URLs__globs__inner")]
    public partial class IncludeURLsGlobsInner : IValidatableObject
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="IncludeURLsGlobsInner" /> class.
        /// </summary>
        [JsonConstructorAttribute]
        protected IncludeURLsGlobsInner() { }
        /// <summary>
        /// Initializes a new instance of the <see cref="IncludeURLsGlobsInner" /> class.
        /// </summary>
        /// <param name="glob">glob (required).</param>
        public IncludeURLsGlobsInner(string glob = default(string))
        {
            // to ensure "glob" is required (not null)
            if (glob == null)
            {
                throw new ArgumentNullException("glob is a required property for IncludeURLsGlobsInner and cannot be null");
            }
            this.Glob = glob;
        }

        /// <summary>
        /// Gets or Sets Glob
        /// </summary>
        [DataMember(Name = "glob", IsRequired = true, EmitDefaultValue = true)]
        public string Glob { get; set; }

        /// <summary>
        /// Returns the string presentation of the object
        /// </summary>
        /// <returns>String presentation of the object</returns>
        public override string ToString()
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("class IncludeURLsGlobsInner {\n");
            sb.Append("  Glob: ").Append(Glob).Append("\n");
            sb.Append("}\n");
            return sb.ToString();
        }

        /// <summary>
        /// Returns the JSON string presentation of the object
        /// </summary>
        /// <returns>JSON string presentation of the object</returns>
        public virtual string ToJson()
        {
            return Newtonsoft.Json.JsonConvert.SerializeObject(this, Newtonsoft.Json.Formatting.Indented);
        }

        /// <summary>
        /// To validate all properties of the instance
        /// </summary>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation Result</returns>
        IEnumerable<ValidationResult> IValidatableObject.Validate(ValidationContext validationContext)
        {
            yield break;
        }
    }

}

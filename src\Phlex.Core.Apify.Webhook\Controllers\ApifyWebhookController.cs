using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Webhook.Models;

namespace Phlex.Core.Apify.Webhook.Controllers
{
    [ApiController]
    [AllowAnonymous]
    [IgnoreAntiforgeryToken]
    [Route("api/webhook/apify")]

    public class ApifyWebhookController : ControllerBase
    {
        private readonly IApifyNotification client;
        private readonly ApifyWebhookSettings webhookSettings;

        private readonly ILogger<ApifyWebhookController> logger;

        public ApifyWebhookController(IApifyNotification client,
            IOptions<ApifyWebhookSettings> apifyWebhookSettings,
            ILogger<ApifyWebhookController> logger
            )
        {
            this.client = client;
            this.webhookSettings = apifyWebhookSettings.Value;
            this.logger = logger;
        }

        [HttpPost]
        public IActionResult Receive([FromBody] ApifyWebhookPayload payload)
        {
            string? token = Request.Headers.Authorization;

            if (token != null && token.Equals("Bearer " + webhookSettings.SecretToken))
            {
                if (payload.resource !=null && payload.resource.status == "SUCCEEDED")
                    client.RunSucceeded(payload);
                else
                    client.RunFailed(payload);
            }
            else
            {
                string? ip = this.HttpContext.GetServerVariable("REMOTE_HOST");
                if (ip == null)
                {
                    ip = this.HttpContext.GetServerVariable("REMOTE_ADDR");
                    if (ip == null) ip = "null";
                }
                logger.LogWarning("Apify: Call to webhook receiver enpoint without valid security token. Remote IP: {IP}",ip);
            }
            return Ok();
        }
    }
}

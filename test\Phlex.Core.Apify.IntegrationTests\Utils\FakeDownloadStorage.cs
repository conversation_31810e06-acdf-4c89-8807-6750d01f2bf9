﻿using Phlex.Core.Apify.Interfaces;

namespace Phlex.Core.Apify.IntegrationTests.Utils;

public class FakeDownloadStorage : IDownloadStorage
{
    public async Task WriteDataItemAsync(string fileName, Stream fileStream, CancellationToken cancellationToken = default)
    {
        using (Stream file = File.Create(StorageHelper.BasePath + fileName))
        {
            byte[] buffer = new byte[8 * 1024];
            while ((await fileStream.ReadAsync(buffer, cancellationToken)) > 0)
            {
                await file.WriteAsync(buffer, cancellationToken);
            }
        }
    }
}

name: '$(Date:yyyyMMdd).$(Rev:r)'
parameters:
- name: Publish_Nuget
  displayName: Publish Nuget
  type: boolean
  default: false

trigger:
- develop

variables:
- name: Source_Branch
  value: $[replace(variables['Build.SourceBranch'],'refs/heads/','')]
- name: Publish_Nuget
  value: $[or(in(variables['Source_Branch'], 'main','develop'), startsWith(variables['Source_Branch'], 'release/'), eq('${{ parameters.Publish_Nuget }}', true))]
- name: Long_Lived_Branch
  value: $[or(in(variables['Source_Branch'], 'main','develop'), startsWith(variables['Source_Branch'], 'release/'))]
- name: Version_Number
  value: '1.0.0'
- name: Build_Number
  value: $[counter(variables['Version_Number'], 100)]
- name: Build_Configuration
  value: 'Release'
#Shared Variable group
- group: Nuget
- name: NuGet_Source
  value: $[variables.Source]

resources:
  repositories:
    - repository: templates
      name: Phlex.Core/Dev.Pipelines.Templates
      type: git

pool:
  vmImage: 'ubuntu-latest'
jobs:
- job: Build
  steps:
  - template: General/calculate-version.yml@templates
    parameters:
      VersionNumber: '$(Version_Number)'
      BuildNumber: '$(Build_Number)'
      BranchName: '$(Source_Branch)'
  - template: Build/dotnet/build-test-analyse.yml@templates
    parameters:
      NugetSource: '$(NuGet_Source)'
      BuildConfiguration: '$(Build_Configuration)'
      SolutionName: "Phlex.Core.Apify.sln"
      VersionNumber: '$(Version_Number)'
      TestProjects: "test/*Tests/*Tests.csproj"
      SonarProjectKey: 'Phlexglobal_Phlex.Core.Apify'
      SonarProjectName: 'Phlex.Core.Apify'
      LongLivedBranch: "variables['Long_Lived_Branch']"
  - template: Nuget/nuget-publish-includeprojects.yml@templates
    parameters:
      BuildConfiguration: '$(Build_Configuration)'
      PackagesToPack: 'src/**/*.csproj'
      PublishNuget: "variables['Publish_Nuget']"
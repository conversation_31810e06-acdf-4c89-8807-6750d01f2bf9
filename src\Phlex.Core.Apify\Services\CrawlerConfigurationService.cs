﻿using Apify.SDK.Actors.WebsiteContentCrawler;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Phlex.Core.Apify.Interfaces;

namespace Phlex.Core.Apify.Services;

public class CrawlerConfigurationService(ILogger<CrawlerConfigurationService> logger) : ICrawlerConfigurationService
{
    private readonly Dictionary<string, InputSchema> crawlerConfigMap = new ();

    public async Task<InputSchema> GetCrawlerConfigurationAsync(ICrawlerData crawlerData, string configName)
    {
        if (crawlerConfigMap.TryGetValue(configName, out var inputSchemaValue))
        {
            return inputSchemaValue;
        }

        try
        {
            var inputConfig = await crawlerData.GetConfigurationAsync(configName);
            var inputSchema = JsonConvert.DeserializeObject<InputSchema>(inputConfig.JsonConfig);
            crawlerConfigMap.Add(configName,
                inputSchema ?? throw new ArgumentException("Invalid configuration {ConfigName}.", configName));

            logger.LogInformation("Apify: Successfully loaded configuration: {ConfigName}", configName);
            return inputSchema;
        }
        catch (Exception e)
        {
            logger.LogError(e, "Apify: Failed to load configuration: {ConfigName}", configName);
            throw new ArgumentException("Invalid configuration {ConfigName}", configName);
        }
    }
}

﻿# Phlex.Core.Apify
[![Build Status](https://dev.azure.com/Phlexglobal/Phlex.Core/_apis/build/status/Phlex.Core.Apify?repoName=Phlex.Core.Apify&branchName=main)](https://dev.azure.com/Phlexglobal/Phlex.Core/_build/latest?definitionId=2727&repoName=Phlex.Core.Apify&branchName=main)

## Project Overview
Phlex.Core.Apify is a C# library that provides external clients with an easy way to Apify, actors, tasks and runs. The library uses an SDK generated by OpenAPI generator using the raw schema exposed by Apify. The SDK makes HTTP requests to the Apify APIs to make any changes.

The library is meant to facilitate shared functionality across applications using Apify.

### Project Structure
```
├── src
     ├── Phlex.Core.Apify                              # C# library providing services to manage Apify actors, tasks and runs
     ├── sdk                                              
          ├── Apify.SDK                                # SDK generated by OpenAPI generator
├── test
     ├── Phlex.Core.Apify.IntegrationTests             # Integration tests for Apify
```

## Getting Started
In order to use the library, call the `AddApify` extension method passing in the configuration object:
```csharp
services.AddApify(configuration)
```

### SDK Generation
Using OpenAPI Generator we can generate C# code to talk to their API.   

```
docker run --rm -v C:\Development\Repos\Phlex.Core.Apify:/mount openapitools/openapi-generator-cli generate -i /mount/openapi/openapi.json -g csharp -o /mount/src/sdk --library httpclient --additional-properties=packageName=Apify.SDK --additional-properties=targetFramework=net8.0 --skip-validate-spec
```

Individual crawlers have a separate OpenAPI definition
```
docker run --rm -v C:\Development\Repos\Phlex.Core.Apify:/mount openapitools/openapi-generator-cli generate -i /mount/openapi/crawler-openapi.json -g csharp -o /mount/src/sdk/wcc --library httpclient --additional-properties=packageName=Apify.WebsiteContentCrawler.SDK --additional-properties=targetFramework=net8.0 --skip-validate-spec
```

## Test Setup

Ensure user secrets are configured on your machine and point to valid values
```
{
  "Apify": {
    "BaseUri": "https://api.apify.com",
    "Token": "apify_api_{REPLACE}",
    "TestRunId": "XYZ_REPLACE_ME"
  }
}
```
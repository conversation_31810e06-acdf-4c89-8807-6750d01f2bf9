/*
 * Apify API
 *
 *  > **UPDATE 2025-01-14:** > We have rolled out this new Apify API Documentation. In case of any issues, please [report here](https://github.com/apify/apify-docs/issues). > The old API Documentation is still [available here](https://docs.apify.com/api/v2-old).  The Apify API (version 2) provides programmatic access to the [Apify platform](https://docs.apify.com). The API is organized around [RESTful](https://en.wikipedia.org/wiki/Representational_state_transfer) HTTP endpoints.  You can download the complete OpenAPI schema of Apify API in the [YAML](http://docs.apify.com/api/openapi.yaml) or [JSON](http://docs.apify.com/api/openapi.json) formats. The source code is also available on [GitHub](https://github.com/apify/apify-docs/tree/master/apify-api/openapi).  All requests and responses (including errors) are encoded in [JSON](http://www.json.org/) format with UTF-8 encoding, with a few exceptions that are explicitly described in the reference.  To access the API using [Node.js](https://nodejs.org/en/), we recommend the [`apify-client`](https://docs.apify.com/api/client/js) [NPM package](https://www.npmjs.com/package/apify-client).  To access the API using [Python](https://www.python.org/), we recommend the [`apify-client`](https://docs.apify.com/api/client/python) [PyPI package](https://pypi.org/project/apify-client/). The clients' functions correspond to the API endpoints and have the same parameters. This simplifies development of apps that depend on the Apify platform.  **Note:** All requests with JSON payloads need to specify the `Content-Type: application/json` HTTP header! All API endpoints support the `method` query parameter that can override the HTTP method. For example, if you want to call a POST endpoint using a GET request, simply add the query parameter `method=POST` to the URL and send the GET request. This feature is especially useful if you want to call Apify API endpoints from services that can only send GET requests.  ## Authentication <span id=\"/introduction/authentication\"></span>  You can find your API token on the [Integrations](https://console.apify.com/account#/integrations) page in the Apify Console.  To use your token in a request, either:  - Add the token to your request's `Authorization` header as `Bearer <token>`. E.g., `Authorization: Bearer xxxxxxx`. [More info](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Authorization). (Recommended). - Add it as the `token` parameter to your request URL. (Less secure).  Using your token in the request header is more secure than using it as a URL parameter because URLs are often stored in browser history and server logs. This creates a chance for someone unauthorized to access your API token.  **Do not share your API token or password with untrusted parties.**  For more information, see our [integrations](https://docs.apify.com/platform/integrations) documentation.  ## Basic usage <span id=\"/introduction/basic-usage\"></span>  To run an Actor, send a POST request to the [Run Actor](#/reference/actors/run-collection/run-actor) endpoint using either the Actor ID code (e.g. `vKg4IjxZbEYTYeW8T`) or its name (e.g. `janedoe~my-actor`):  `https://api.apify.com/v2/acts/[actor_id]/runs`  If the Actor is not runnable anonymously, you will receive a 401 or 403 [response code](https://developer.mozilla.org/en-US/docs/Web/HTTP/Status). This means you need to add your [secret API token](https://console.apify.com/account#/integrations) to the request's `Authorization` header ([recommended](#/introduction/authentication)) or as a URL query parameter `?token=[your_token]` (less secure).  Optionally, you can include the query parameters described in the [Run Actor](#/reference/actors/run-collection/run-actor) section to customize your run.  If you're using Node.js, the best way to run an Actor is using the `Apify.call()` method from the [Apify SDK](https://sdk.apify.com/docs/api/apify#apifycallactid-input-options). It runs the Actor using the account you are currently logged into (determined by the [secret API token](https://console.apify.com/account#/integrations)). The result is an [Actor run object](https://sdk.apify.com/docs/typedefs/actor-run) and its output (if any).  A typical workflow is as follows:  1. Run an Actor or task using the [Run Actor](#/reference/actors/run-collection/run-actor) or [Run task](#/reference/actor-tasks/run-collection/run-task) API endpoints. 2. Monitor the Actor run by periodically polling its progress using the [Get run](#/reference/actor-runs/run-object-and-its-storages/get-run) API endpoint. 3. Fetch the results from the [Get items](#/reference/datasets/item-collection/get-items) API endpoint using the `defaultDatasetId`, which you receive in the Run request response. Additional data may be stored in a key-value store. You can fetch them from the [Get record](#/reference/key-value-stores/record/get-record) API endpoint using the `defaultKeyValueStoreId` and the store's `key`.  **Note**: Instead of periodic polling, you can also run your [Actor](#/reference/actors/run-actor-synchronously) or [task](#/reference/actor-tasks/runs-collection/run-task-synchronously) synchronously. This will ensure that the request waits for 300 seconds (5 minutes) for the run to finish and returns its output. If the run takes longer, the request will time out and throw an error.  ## Response structure <span id=\"/introduction/response-structure\"></span>  Most API endpoints return a JSON object with the `data` property:  ``` {     \"data\": {         ...     } } ```  However, there are a few explicitly described exceptions, such as Dataset [Get items](#/reference/datasets/item-collection/get-items) or Key-value store [Get record](#/reference/key-value-stores/record/get-record) API endpoints, which return data in other formats. In case of an error, the response has the HTTP status code in the range of 4xx or 5xx and the `data` property is replaced with `error`. For example:  ``` {     \"error\": {         \"type\": \"record-not-found\",         \"message\": \"Store was not found.\"     } } ```  See [Errors](#/introduction/errors) for more details.  ## Pagination <span id=\"/introduction/pagination\"></span>  All API endpoints that return a list of records (e.g. [Get list of Actors](#/reference/actors/actor-collection/get-list-of-actors)) enforce pagination in order to limit the size of their responses.  Most of these API endpoints are paginated using the `offset` and `limit` query parameters. The only exception is [Get list of keys](#/reference/key-value-stores/key-collection/get-list-of-keys), which is paginated using the `exclusiveStartKey` query parameter.  **IMPORTANT**: Each API endpoint that supports pagination enforces a certain maximum value for the `limit` parameter, in order to reduce the load on Apify servers. The maximum limit could change in future so you should never rely on a specific value and check the responses of these API endpoints.  ### Using offset <span id=\"/introduction/pagination/using-offset\"></span>  Most API endpoints that return a list of records enable pagination using the following query parameters:  <table>   <tr>     <td><code>limit</code></td>     <td>Limits the response to contain a specific maximum number of items, e.g. <code>limit=20</code>.</td>   </tr>   <tr>     <td><code>offset</code></td>     <td>Skips a number of items from the beginning of the list, e.g. <code>offset=100</code>.</td>   </tr>   <tr>     <td><code>desc</code></td>     <td>     By default, items are sorted in the order in which they were created or added to the list.     This feature is useful when fetching all the items, because it ensures that items     created after the client started the pagination will not be skipped.     If you specify the <code>desc=1</code> parameter, the items will be returned in the reverse order,     i.e. from the newest to the oldest items.     </td>   </tr> </table>  The response of these API endpoints is always a JSON object with the following structure:  ``` {     \"data\": {         \"total\": 2560,         \"offset\": 250,         \"limit\": 1000,         \"count\": 1000,         \"desc\": false,         \"items\": [             { 1st object },             { 2nd object },             ...             { 1000th object }         ]     } } ```  The following table describes the meaning of the response properties:  <table>   <tr>     <th>Property</th>     <th>Description</th>   </tr>   <tr>     <td><code>total</code></td>     <td>The total number of items available in the list.</td>   </tr>   <tr>     <td><code>offset</code></td>     <td>The number of items that were skipped at the start.     This is equal to the <code>offset</code> query parameter if it was provided, otherwise it is <code>0</code>.</td>   </tr>   <tr>     <td><code>limit</code></td>     <td>The maximum number of items that can be returned in the HTTP response.     It equals to the <code>limit</code> query parameter if it was provided or     the maximum limit enforced for the particular API endpoint, whichever is smaller.</td>   </tr>   <tr>     <td><code>count</code></td>     <td>The actual number of items returned in the HTTP response.</td>   </tr>   <tr>     <td><code>desc</code></td>     <td><code>true</code> if data were requested in descending order and <code>false</code> otherwise.</td>   </tr>   <tr>     <td><code>items</code></td>     <td>An array of requested items.</td>   </tr> </table>  ### Using key <span id=\"/introduction/pagination/using-key\"></span>  The records in the [key-value store](https://docs.apify.com/platform/storage/key-value-store) are not ordered based on numerical indexes, but rather by their keys in the UTF-8 binary order. Therefore the [Get list of keys](#/reference/key-value-stores/key-collection/get-list-of-keys) API endpoint only supports pagination using the following query parameters:  <table>   <tr>     <td><code>limit</code></td>     <td>Limits the response to contain a specific maximum number items, e.g. <code>limit=20</code>.</td>   </tr>   <tr>     <td><code>exclusiveStartKey</code></td>     <td>Skips all records with keys up to the given key including the given key,     in the UTF-8 binary order.</td>   </tr> </table>  The response of the API endpoint is always a JSON object with following structure:  ``` {     \"data\": {         \"limit\": 1000,         \"isTruncated\": true,         \"exclusiveStartKey\": \"my-key\",         \"nextExclusiveStartKey\": \"some-other-key\",         \"items\": [             { 1st object },             { 2nd object },             ...             { 1000th object }         ]     } } ```  The following table describes the meaning of the response properties:  <table>   <tr>     <th>Property</th>     <th>Description</th>   </tr>   <tr>     <td><code>limit</code></td>     <td>The maximum number of items that can be returned in the HTTP response.     It equals to the <code>limit</code> query parameter if it was provided or     the maximum limit enforced for the particular endpoint, whichever is smaller.</td>   </tr>   <tr>     <td><code>isTruncated</code></td>     <td><code>true</code> if there are more items left to be queried. Otherwise <code>false</code>.</td>   </tr>   <tr>     <td><code>exclusiveStartKey</code></td>     <td>The last key that was skipped at the start. Is `null` for the first page.</td>   </tr>   <tr>     <td><code>nextExclusiveStartKey</code></td>     <td>The value for the <code>exclusiveStartKey</code> parameter to query the next page of items.</td>   </tr> </table>  ## Errors <span id=\"/introduction/errors\"></span>  The Apify API uses common HTTP status codes: `2xx` range for success, `4xx` range for errors caused by the caller (invalid requests) and `5xx` range for server errors (these are rare). Each error response contains a JSON object defining the `error` property, which is an object with the `type` and `message` properties that contain the error code and a human-readable error description, respectively.  For example:  ``` {     \"error\": {         \"type\": \"record-not-found\",         \"message\": \"Store was not found.\"     } } ```  Here is the table of the most common errors that can occur for many API endpoints:  <table>   <tr>     <th>status</th>     <th>type</th>     <th>message</th>   </tr>   <tr>     <td><code>400</code></td>     <td><code>invalid-request</code></td>     <td>POST data must be a JSON object</td>   </tr>   <tr>     <td><code>400</code></td>     <td><code>invalid-value</code></td>     <td>Invalid value provided: Comments required</td>   </tr>   <tr>     <td><code>400</code></td>     <td><code>invalid-record-key</code></td>     <td>Record key contains invalid character</td>   </tr>   <tr>     <td><code>401</code></td>     <td><code>token-not-provided</code></td>     <td>Authentication token was not provided</td>   </tr>   <tr>     <td><code>404</code></td>     <td><code>record-not-found</code></td>     <td>Store was not found</td>   </tr>   <tr>     <td><code>429</code></td>     <td><code>rate-limit-exceeded</code></td>     <td>You have exceeded the rate limit of 30 requests per second</td>   </tr>   <tr>     <td><code>405</code></td>     <td><code>method-not-allowed</code></td>     <td>This API endpoint can only be accessed using the following HTTP methods: OPTIONS, POST</td>   </tr> </table>  ## Rate limiting <span id=\"/introduction/rate-limiting\"></span>  All API endpoints limit the rate of requests in order to prevent overloading of Apify servers by misbehaving clients.  There are two kinds of rate limits - a global rate limit and a per-resource rate limit.  ### Global rate limit <span id=\"/introduction/rate-limiting/global-rate-limit\"></span>  The global rate limit is set to _250 000 requests per minute_. For [authenticated](#/introduction/authentication) requests, it is counted per user, and for unauthenticated requests, it is counted per IP address.  ### Per-resource rate limit <span id=\"/introduction/rate-limiting/per-resource-rate-limit\"></span>  The default per-resource rate limit is _30 requests per second per resource_, which in this context means a single Actor, a single Actor run, a single dataset, single key-value store etc. The default rate limit is applied to every API endpoint except a few select ones, which have higher rate limits. Each API endpoint returns its rate limit in `X-RateLimit-Limit` header.  These endpoints have a rate limit of _100 requests per second per resource_:  * CRUD ([get](#/reference/key-value-stores/record/get-record),   [put](#/reference/key-value-stores/record/put-record),   [delete](#/reference/key-value-stores/record/delete-record))   operations on key-value store records  These endpoints have a rate limit of _200 requests per second per resource_: * [Run Actor](#/reference/actors/run-collection/run-actor) * [Run Actor task asynchronously](#/reference/actor-tasks/runs-collection/run-task-asynchronously) * [Run Actor task synchronously](#/reference/actor-tasks/runs-collection/run-task-synchronously) * [Metamorph Actor run](#/reference/actors/metamorph-run/metamorph-run) * [Push items](#/reference/datasets/item-collection/put-items) to dataset * CRUD   ([add](#/reference/request-queues/request-collection/add-request),   [get](#/reference/request-queues/request-collection/get-request),   [update](#/reference/request-queues/request-collection/update-request),   [delete](#/reference/request-queues/request-collection/delete-request))   operations on requests in request queues  ### Rate limit exceeded errors <span id=\"/introduction/rate-limiting/rate-limit-exceeded-errors\"></span>  If the client is sending too many requests, the API endpoints respond with the HTTP status code `429 Too Many Requests` and the following body:  ``` {     \"error\": {         \"type\": \"rate-limit-exceeded\",         \"message\": \"You have exceeded the rate limit of ... requests per second\"     } } ```  ### Retrying rate-limited requests with exponential backoff <span id=\"/introduction/rate-limiting/retrying-rate-limited-requests-with-exponential-backoff\"></span>  If the client receives the rate limit error, it should wait a certain period of time and then retry the request. If the error happens again, the client should double the wait period and retry the request, and so on. This algorithm is known as _exponential backoff_ and it can be described using the following pseudo-code:  1. Define a variable `DELAY=500` 2. Send the HTTP request to the API endpoint 3. If the response has status code not equal to `429` then you are done. Otherwise:    * Wait for a period of time chosen randomly from the interval `DELAY` to `2*DELAY` milliseconds    * Double the future wait period by setting `DELAY = 2*DELAY`    * Continue with step 2  If all requests sent by the client implement the above steps, the client will automatically use the maximum available bandwidth for its requests.  Note that the Apify API clients [for JavaScript](https://docs.apify.com/api/client/js) and [for Python](https://docs.apify.com/api/client/python) use the exponential backoff algorithm transparently, so that you do not need to worry about it.  ## Referring to resources <span id=\"/introduction/referring-to-resources\"></span>  There are three main ways to refer to a resource you're accessing via API.  - the resource ID (e.g. `iKkPcIgVvwmztduf8`) - `username~resourcename` - when using this access method, you will need to use your API token, and access will only work if you have the correct permissions. - `~resourcename` - for this, you need to use an API token, and the `resourcename` refers to a resource in the API token owner's account. 
 *
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Mime;
using Apify.SDK.Client;
using Apify.SDK.Model;

namespace Apify.SDK.Api
{

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IActorRunsApiSync : IApiAccessor
    {
        #region Synchronous Operations
        /// <summary>
        /// Abort run
        /// </summary>
        /// <remarks>
        /// Aborts an Actor run and returns an object that contains all the details about the run.  Only runs that are starting or running are aborted. For runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTING&#x60; and &#x60;TIMED-OUT&#x60; this call does nothing. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="gracefully">If true passed, the Actor run will abort gracefully. It will send &#x60;aborting&#x60; and &#x60;persistState&#x60; event into run and force-stop the run after 30 seconds. It is helpful in cases where you plan to resurrect the run later.  (optional)</param>
        /// <returns>RunResponse</returns>
        RunResponse ActorRunAbortPost(string runId, bool? gracefully = default(bool?));

        /// <summary>
        /// Abort run
        /// </summary>
        /// <remarks>
        /// Aborts an Actor run and returns an object that contains all the details about the run.  Only runs that are starting or running are aborted. For runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTING&#x60; and &#x60;TIMED-OUT&#x60; this call does nothing. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="gracefully">If true passed, the Actor run will abort gracefully. It will send &#x60;aborting&#x60; and &#x60;persistState&#x60; event into run and force-stop the run after 30 seconds. It is helpful in cases where you plan to resurrect the run later.  (optional)</param>
        /// <returns>ApiResponse of RunResponse</returns>
        ApiResponse<RunResponse> ActorRunAbortPostWithHttpInfo(string runId, bool? gracefully = default(bool?));
        /// <summary>
        /// Delete run
        /// </summary>
        /// <remarks>
        /// Delete the run. Only finished runs can be deleted. Only the person or organization that initiated the run can delete it. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <returns></returns>
        void ActorRunDelete(string runId);

        /// <summary>
        /// Delete run
        /// </summary>
        /// <remarks>
        /// Delete the run. Only finished runs can be deleted. Only the person or organization that initiated the run can delete it. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <returns>ApiResponse of Object(void)</returns>
        ApiResponse<Object> ActorRunDeleteWithHttpInfo(string runId);
        /// <summary>
        /// Get run
        /// </summary>
        /// <remarks>
        /// This is not a single endpoint, but an entire group of endpoints that lets you retrieve the run or any of its default storages.  The endpoints accept the same HTTP methods and query parameters as the respective storage endpoints.  The base path that represents the Actor run object is:  &#x60;/v2/actor-runs/{runId}{?token}&#x60;  In order to access the default storages of the Actor run, i.e. log, key-value store, dataset and request queue, use the following endpoints:  * &#x60;/v2/actor-runs/{runId}/log{?token}&#x60; * &#x60;/v2/actor-runs/{runId}/key-value-store{?token}&#x60; * &#x60;/v2/actor-runs/{runId}/dataset{?token}&#x60; * &#x60;/v2/actor-runs/{runId}/request-queue{?token}&#x60;  These API endpoints have the same usage as the equivalent storage endpoints.  For example, &#x60;/v2/actor-runs/{runId}/key-value-store&#x60; has the same HTTP method and parameters as the [Key-value store object](#/reference/key-value-stores/store-object) endpoint.  Additionally, each of the above API endpoints supports all sub-endpoints of the original one:  #### Log  * &#x60;/v2/actor-runs/{runId}/log&#x60; [Log](#/reference/logs)  #### Key-value store  * &#x60;/v2/actor-runs/{runId}/key-value-store/keys{?token}&#x60; [Key collection](#/reference/key-value-stores/key-collection) * &#x60;/v2/actor-runs/{runId}/key-value-store/records/{recordKey}{?token}&#x60; [Record](#/reference/key-value-stores/record)  #### Dataset  * &#x60;/v2/actor-runs/{runId}/dataset/items{?token}&#x60; [Item collection](#/reference/datasets/item-collection)  #### Request queue  * &#x60;/v2/actor-runs/{runId}/request-queue/requests{?token}&#x60; [Request collection](#/reference/request-queues/request-collection) * &#x60;/v2/actor-runs/{runId}/request-queue/requests/{requestId}{?token}&#x60; [Request collection](#/reference/request-queues/request) * &#x60;/v2/actor-runs/{runId}/request-queue/head{?token}&#x60; [Queue head](#/reference/request-queues/queue-head)  For example, to download data from a dataset of the Actor run in XML format, send HTTP GET request to the following URL:  &#x60;&#x60;&#x60; https://api.apify.com/v2/actor-runs/{runId}/dataset/items?format&#x3D;xml &#x60;&#x60;&#x60;  In order to save new items to the dataset, send HTTP POST request with JSON payload to the same URL.  Gets an object that contains all the details about a specific run of an Actor.  By passing the optional &#x60;waitForFinish&#x60; parameter the API endpoint will synchronously wait for the run to finish. This is useful to avoid periodic polling when waiting for Actor run to complete.  This endpoint does not require the authentication token. Instead, calls are authenticated using a hard-to-guess ID of the run. However, if you access the endpoint without the token, certain attributes, such as &#x60;usageUsd&#x60; and &#x60;usageTotalUsd&#x60;, will be hidden. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <returns>RunResponse</returns>
        RunResponse ActorRunGet(string runId, double? waitForFinish = default(double?));

        /// <summary>
        /// Get run
        /// </summary>
        /// <remarks>
        /// This is not a single endpoint, but an entire group of endpoints that lets you retrieve the run or any of its default storages.  The endpoints accept the same HTTP methods and query parameters as the respective storage endpoints.  The base path that represents the Actor run object is:  &#x60;/v2/actor-runs/{runId}{?token}&#x60;  In order to access the default storages of the Actor run, i.e. log, key-value store, dataset and request queue, use the following endpoints:  * &#x60;/v2/actor-runs/{runId}/log{?token}&#x60; * &#x60;/v2/actor-runs/{runId}/key-value-store{?token}&#x60; * &#x60;/v2/actor-runs/{runId}/dataset{?token}&#x60; * &#x60;/v2/actor-runs/{runId}/request-queue{?token}&#x60;  These API endpoints have the same usage as the equivalent storage endpoints.  For example, &#x60;/v2/actor-runs/{runId}/key-value-store&#x60; has the same HTTP method and parameters as the [Key-value store object](#/reference/key-value-stores/store-object) endpoint.  Additionally, each of the above API endpoints supports all sub-endpoints of the original one:  #### Log  * &#x60;/v2/actor-runs/{runId}/log&#x60; [Log](#/reference/logs)  #### Key-value store  * &#x60;/v2/actor-runs/{runId}/key-value-store/keys{?token}&#x60; [Key collection](#/reference/key-value-stores/key-collection) * &#x60;/v2/actor-runs/{runId}/key-value-store/records/{recordKey}{?token}&#x60; [Record](#/reference/key-value-stores/record)  #### Dataset  * &#x60;/v2/actor-runs/{runId}/dataset/items{?token}&#x60; [Item collection](#/reference/datasets/item-collection)  #### Request queue  * &#x60;/v2/actor-runs/{runId}/request-queue/requests{?token}&#x60; [Request collection](#/reference/request-queues/request-collection) * &#x60;/v2/actor-runs/{runId}/request-queue/requests/{requestId}{?token}&#x60; [Request collection](#/reference/request-queues/request) * &#x60;/v2/actor-runs/{runId}/request-queue/head{?token}&#x60; [Queue head](#/reference/request-queues/queue-head)  For example, to download data from a dataset of the Actor run in XML format, send HTTP GET request to the following URL:  &#x60;&#x60;&#x60; https://api.apify.com/v2/actor-runs/{runId}/dataset/items?format&#x3D;xml &#x60;&#x60;&#x60;  In order to save new items to the dataset, send HTTP POST request with JSON payload to the same URL.  Gets an object that contains all the details about a specific run of an Actor.  By passing the optional &#x60;waitForFinish&#x60; parameter the API endpoint will synchronously wait for the run to finish. This is useful to avoid periodic polling when waiting for Actor run to complete.  This endpoint does not require the authentication token. Instead, calls are authenticated using a hard-to-guess ID of the run. However, if you access the endpoint without the token, certain attributes, such as &#x60;usageUsd&#x60; and &#x60;usageTotalUsd&#x60;, will be hidden. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <returns>ApiResponse of RunResponse</returns>
        ApiResponse<RunResponse> ActorRunGetWithHttpInfo(string runId, double? waitForFinish = default(double?));
        /// <summary>
        /// Metamorph run
        /// </summary>
        /// <remarks>
        /// Transforms an Actor run into a run of another Actor with a new input.  This is useful if you want to use another Actor to finish the work of your current Actor run, without the need to create a completely new run and waiting for its finish.  For the users of your Actors, the metamorph operation is transparent, they will just see your Actor got the work done.  Internally, the system stops the Docker container corresponding to the Actor run and starts a new container using a different Docker image.  All the default storages are preserved and the new input is stored under the &#x60;INPUT-METAMORPH-1&#x60; key in the same default key-value store.  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/development/programming-interface/metamorph). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Actor run ID.</param>
        /// <param name="targetActorId">ID of a target Actor that the run should be transformed into.</param>
        /// <param name="build">Optional build of the target Actor.  It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the target Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <returns>RunResponse</returns>
        RunResponse ActorRunMetamorphPost(string runId, string targetActorId, string? build = default(string?));

        /// <summary>
        /// Metamorph run
        /// </summary>
        /// <remarks>
        /// Transforms an Actor run into a run of another Actor with a new input.  This is useful if you want to use another Actor to finish the work of your current Actor run, without the need to create a completely new run and waiting for its finish.  For the users of your Actors, the metamorph operation is transparent, they will just see your Actor got the work done.  Internally, the system stops the Docker container corresponding to the Actor run and starts a new container using a different Docker image.  All the default storages are preserved and the new input is stored under the &#x60;INPUT-METAMORPH-1&#x60; key in the same default key-value store.  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/development/programming-interface/metamorph). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Actor run ID.</param>
        /// <param name="targetActorId">ID of a target Actor that the run should be transformed into.</param>
        /// <param name="build">Optional build of the target Actor.  It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the target Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <returns>ApiResponse of RunResponse</returns>
        ApiResponse<RunResponse> ActorRunMetamorphPostWithHttpInfo(string runId, string targetActorId, string? build = default(string?));
        /// <summary>
        /// Update status message
        /// </summary>
        /// <remarks>
        /// You can set a single status message on your run that will be displayed in the Apify Console UI. During an Actor run, you will typically do this in order to inform users of your Actor about the Actor&#39;s progress.  The request body must contain &#x60;runId&#x60; and &#x60;statusMessage&#x60; properties. The &#x60;isStatusMessageTerminal&#x60; property is optional and it indicates if the status message is the very last one. In the absence of a status message, the platform will try to substitute sensible defaults. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="actorRunPutRequest"></param>
        /// <returns>RunResponse</returns>
        RunResponse ActorRunPut(string runId, ActorRunPutRequest actorRunPutRequest);

        /// <summary>
        /// Update status message
        /// </summary>
        /// <remarks>
        /// You can set a single status message on your run that will be displayed in the Apify Console UI. During an Actor run, you will typically do this in order to inform users of your Actor about the Actor&#39;s progress.  The request body must contain &#x60;runId&#x60; and &#x60;statusMessage&#x60; properties. The &#x60;isStatusMessageTerminal&#x60; property is optional and it indicates if the status message is the very last one. In the absence of a status message, the platform will try to substitute sensible defaults. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="actorRunPutRequest"></param>
        /// <returns>ApiResponse of RunResponse</returns>
        ApiResponse<RunResponse> ActorRunPutWithHttpInfo(string runId, ActorRunPutRequest actorRunPutRequest);
        /// <summary>
        /// Reboot run
        /// </summary>
        /// <remarks>
        /// Reboots an Actor run and returns an object that contains all the details about the rebooted run.  Only runs that are running, i.e. runs with status &#x60;RUNNING&#x60; can be rebooted.  The run&#39;s container will be restarted, so any data not persisted in the key-value store, dataset, or request queue will be lost. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Actor run ID.</param>
        /// <returns>RunResponse</returns>
        RunResponse ActorRunRebootPost(string runId);

        /// <summary>
        /// Reboot run
        /// </summary>
        /// <remarks>
        /// Reboots an Actor run and returns an object that contains all the details about the rebooted run.  Only runs that are running, i.e. runs with status &#x60;RUNNING&#x60; can be rebooted.  The run&#39;s container will be restarted, so any data not persisted in the key-value store, dataset, or request queue will be lost. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Actor run ID.</param>
        /// <returns>ApiResponse of RunResponse</returns>
        ApiResponse<RunResponse> ActorRunRebootPostWithHttpInfo(string runId);
        /// <summary>
        /// Get user runs list
        /// </summary>
        /// <remarks>
        /// Gets a list of all runs for a user. The response is a list of objects, where each object contains basic information about a single Actor run.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 array elements.  By default, the records are sorted by the &#x60;startedAt&#x60; field in ascending order. Therefore, you can use pagination to incrementally fetch all records while new ones are still being created. To sort the records in descending order, use &#x60;desc&#x3D;1&#x60; parameter. You can also filter runs by status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle)). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value (as well as the maximum) is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="status">Return only runs with the provided status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle))  (optional)</param>
        /// <returns>GetUserRunsListResponse</returns>
        GetUserRunsListResponse ActorRunsGet(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), string? status = default(string?));

        /// <summary>
        /// Get user runs list
        /// </summary>
        /// <remarks>
        /// Gets a list of all runs for a user. The response is a list of objects, where each object contains basic information about a single Actor run.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 array elements.  By default, the records are sorted by the &#x60;startedAt&#x60; field in ascending order. Therefore, you can use pagination to incrementally fetch all records while new ones are still being created. To sort the records in descending order, use &#x60;desc&#x3D;1&#x60; parameter. You can also filter runs by status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle)). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value (as well as the maximum) is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="status">Return only runs with the provided status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle))  (optional)</param>
        /// <returns>ApiResponse of GetUserRunsListResponse</returns>
        ApiResponse<GetUserRunsListResponse> ActorRunsGetWithHttpInfo(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), string? status = default(string?));
        /// <summary>
        /// Charge events in run
        /// </summary>
        /// <remarks>
        /// Charge for events in the run of your [pay per event Actor](https://docs.apify.com/platform/actors/running/actors-in-store#pay-per-event). The event you are charging for must be one of the configured events in your Actor. If the Actor is not set up as pay per event, or if the event is not configured, the endpoint will return an error. The endpoint must be called from the Actor run itself, with the same API token that the run was started with.  :::note  Pay per events Actors are still in alpha. Please, reach out to us with any questions or feedback.  ::: 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="chargeRunRequest">Define which event, and how many times, you want to charge for.</param>
        /// <param name="idempotencyKey">Always pass a unique idempotency key (any unique string) for each charge to avoid double charging in case of retries or network errors. (optional)</param>
        /// <returns></returns>
        void PostChargeRun(string runId, ChargeRunRequest chargeRunRequest, string? idempotencyKey = default(string?));

        /// <summary>
        /// Charge events in run
        /// </summary>
        /// <remarks>
        /// Charge for events in the run of your [pay per event Actor](https://docs.apify.com/platform/actors/running/actors-in-store#pay-per-event). The event you are charging for must be one of the configured events in your Actor. If the Actor is not set up as pay per event, or if the event is not configured, the endpoint will return an error. The endpoint must be called from the Actor run itself, with the same API token that the run was started with.  :::note  Pay per events Actors are still in alpha. Please, reach out to us with any questions or feedback.  ::: 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="chargeRunRequest">Define which event, and how many times, you want to charge for.</param>
        /// <param name="idempotencyKey">Always pass a unique idempotency key (any unique string) for each charge to avoid double charging in case of retries or network errors. (optional)</param>
        /// <returns>ApiResponse of Object(void)</returns>
        ApiResponse<Object> PostChargeRunWithHttpInfo(string runId, ChargeRunRequest chargeRunRequest, string? idempotencyKey = default(string?));
        /// <summary>
        /// Resurrect run
        /// </summary>
        /// <remarks>
        /// Resurrects a finished Actor run and returns an object that contains all the details about the resurrected run. Only finished runs, i.e. runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTED&#x60; and &#x60;TIMED-OUT&#x60; can be resurrected. Run status will be updated to RUNNING and its container will be restarted with the same storages (the same behaviour as when the run gets migrated to the new server).  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/running/runs-and-builds#resurrection-of-finished-run). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the run that is being resurrected (typically &#x60;latest&#x60;). (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the run that is being resurrected. (optional)</param>
        /// <param name="memory"> (optional)</param>
        /// <returns>RunResponse</returns>
        RunResponse PostResurrectRun(string runId, string? build = default(string?), decimal? timeout = default(decimal?), decimal? memory = default(decimal?));

        /// <summary>
        /// Resurrect run
        /// </summary>
        /// <remarks>
        /// Resurrects a finished Actor run and returns an object that contains all the details about the resurrected run. Only finished runs, i.e. runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTED&#x60; and &#x60;TIMED-OUT&#x60; can be resurrected. Run status will be updated to RUNNING and its container will be restarted with the same storages (the same behaviour as when the run gets migrated to the new server).  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/running/runs-and-builds#resurrection-of-finished-run). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the run that is being resurrected (typically &#x60;latest&#x60;). (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the run that is being resurrected. (optional)</param>
        /// <param name="memory"> (optional)</param>
        /// <returns>ApiResponse of RunResponse</returns>
        ApiResponse<RunResponse> PostResurrectRunWithHttpInfo(string runId, string? build = default(string?), decimal? timeout = default(decimal?), decimal? memory = default(decimal?));
        #endregion Synchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IActorRunsApiAsync : IApiAccessor
    {
        #region Asynchronous Operations
        /// <summary>
        /// Abort run
        /// </summary>
        /// <remarks>
        /// Aborts an Actor run and returns an object that contains all the details about the run.  Only runs that are starting or running are aborted. For runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTING&#x60; and &#x60;TIMED-OUT&#x60; this call does nothing. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="gracefully">If true passed, the Actor run will abort gracefully. It will send &#x60;aborting&#x60; and &#x60;persistState&#x60; event into run and force-stop the run after 30 seconds. It is helpful in cases where you plan to resurrect the run later.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of RunResponse</returns>
        System.Threading.Tasks.Task<RunResponse> ActorRunAbortPostAsync(string runId, bool? gracefully = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Abort run
        /// </summary>
        /// <remarks>
        /// Aborts an Actor run and returns an object that contains all the details about the run.  Only runs that are starting or running are aborted. For runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTING&#x60; and &#x60;TIMED-OUT&#x60; this call does nothing. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="gracefully">If true passed, the Actor run will abort gracefully. It will send &#x60;aborting&#x60; and &#x60;persistState&#x60; event into run and force-stop the run after 30 seconds. It is helpful in cases where you plan to resurrect the run later.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (RunResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<RunResponse>> ActorRunAbortPostWithHttpInfoAsync(string runId, bool? gracefully = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Delete run
        /// </summary>
        /// <remarks>
        /// Delete the run. Only finished runs can be deleted. Only the person or organization that initiated the run can delete it. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of void</returns>
        System.Threading.Tasks.Task ActorRunDeleteAsync(string runId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Delete run
        /// </summary>
        /// <remarks>
        /// Delete the run. Only finished runs can be deleted. Only the person or organization that initiated the run can delete it. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> ActorRunDeleteWithHttpInfoAsync(string runId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Get run
        /// </summary>
        /// <remarks>
        /// This is not a single endpoint, but an entire group of endpoints that lets you retrieve the run or any of its default storages.  The endpoints accept the same HTTP methods and query parameters as the respective storage endpoints.  The base path that represents the Actor run object is:  &#x60;/v2/actor-runs/{runId}{?token}&#x60;  In order to access the default storages of the Actor run, i.e. log, key-value store, dataset and request queue, use the following endpoints:  * &#x60;/v2/actor-runs/{runId}/log{?token}&#x60; * &#x60;/v2/actor-runs/{runId}/key-value-store{?token}&#x60; * &#x60;/v2/actor-runs/{runId}/dataset{?token}&#x60; * &#x60;/v2/actor-runs/{runId}/request-queue{?token}&#x60;  These API endpoints have the same usage as the equivalent storage endpoints.  For example, &#x60;/v2/actor-runs/{runId}/key-value-store&#x60; has the same HTTP method and parameters as the [Key-value store object](#/reference/key-value-stores/store-object) endpoint.  Additionally, each of the above API endpoints supports all sub-endpoints of the original one:  #### Log  * &#x60;/v2/actor-runs/{runId}/log&#x60; [Log](#/reference/logs)  #### Key-value store  * &#x60;/v2/actor-runs/{runId}/key-value-store/keys{?token}&#x60; [Key collection](#/reference/key-value-stores/key-collection) * &#x60;/v2/actor-runs/{runId}/key-value-store/records/{recordKey}{?token}&#x60; [Record](#/reference/key-value-stores/record)  #### Dataset  * &#x60;/v2/actor-runs/{runId}/dataset/items{?token}&#x60; [Item collection](#/reference/datasets/item-collection)  #### Request queue  * &#x60;/v2/actor-runs/{runId}/request-queue/requests{?token}&#x60; [Request collection](#/reference/request-queues/request-collection) * &#x60;/v2/actor-runs/{runId}/request-queue/requests/{requestId}{?token}&#x60; [Request collection](#/reference/request-queues/request) * &#x60;/v2/actor-runs/{runId}/request-queue/head{?token}&#x60; [Queue head](#/reference/request-queues/queue-head)  For example, to download data from a dataset of the Actor run in XML format, send HTTP GET request to the following URL:  &#x60;&#x60;&#x60; https://api.apify.com/v2/actor-runs/{runId}/dataset/items?format&#x3D;xml &#x60;&#x60;&#x60;  In order to save new items to the dataset, send HTTP POST request with JSON payload to the same URL.  Gets an object that contains all the details about a specific run of an Actor.  By passing the optional &#x60;waitForFinish&#x60; parameter the API endpoint will synchronously wait for the run to finish. This is useful to avoid periodic polling when waiting for Actor run to complete.  This endpoint does not require the authentication token. Instead, calls are authenticated using a hard-to-guess ID of the run. However, if you access the endpoint without the token, certain attributes, such as &#x60;usageUsd&#x60; and &#x60;usageTotalUsd&#x60;, will be hidden. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of RunResponse</returns>
        System.Threading.Tasks.Task<RunResponse> ActorRunGetAsync(string runId, double? waitForFinish = default(double?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Get run
        /// </summary>
        /// <remarks>
        /// This is not a single endpoint, but an entire group of endpoints that lets you retrieve the run or any of its default storages.  The endpoints accept the same HTTP methods and query parameters as the respective storage endpoints.  The base path that represents the Actor run object is:  &#x60;/v2/actor-runs/{runId}{?token}&#x60;  In order to access the default storages of the Actor run, i.e. log, key-value store, dataset and request queue, use the following endpoints:  * &#x60;/v2/actor-runs/{runId}/log{?token}&#x60; * &#x60;/v2/actor-runs/{runId}/key-value-store{?token}&#x60; * &#x60;/v2/actor-runs/{runId}/dataset{?token}&#x60; * &#x60;/v2/actor-runs/{runId}/request-queue{?token}&#x60;  These API endpoints have the same usage as the equivalent storage endpoints.  For example, &#x60;/v2/actor-runs/{runId}/key-value-store&#x60; has the same HTTP method and parameters as the [Key-value store object](#/reference/key-value-stores/store-object) endpoint.  Additionally, each of the above API endpoints supports all sub-endpoints of the original one:  #### Log  * &#x60;/v2/actor-runs/{runId}/log&#x60; [Log](#/reference/logs)  #### Key-value store  * &#x60;/v2/actor-runs/{runId}/key-value-store/keys{?token}&#x60; [Key collection](#/reference/key-value-stores/key-collection) * &#x60;/v2/actor-runs/{runId}/key-value-store/records/{recordKey}{?token}&#x60; [Record](#/reference/key-value-stores/record)  #### Dataset  * &#x60;/v2/actor-runs/{runId}/dataset/items{?token}&#x60; [Item collection](#/reference/datasets/item-collection)  #### Request queue  * &#x60;/v2/actor-runs/{runId}/request-queue/requests{?token}&#x60; [Request collection](#/reference/request-queues/request-collection) * &#x60;/v2/actor-runs/{runId}/request-queue/requests/{requestId}{?token}&#x60; [Request collection](#/reference/request-queues/request) * &#x60;/v2/actor-runs/{runId}/request-queue/head{?token}&#x60; [Queue head](#/reference/request-queues/queue-head)  For example, to download data from a dataset of the Actor run in XML format, send HTTP GET request to the following URL:  &#x60;&#x60;&#x60; https://api.apify.com/v2/actor-runs/{runId}/dataset/items?format&#x3D;xml &#x60;&#x60;&#x60;  In order to save new items to the dataset, send HTTP POST request with JSON payload to the same URL.  Gets an object that contains all the details about a specific run of an Actor.  By passing the optional &#x60;waitForFinish&#x60; parameter the API endpoint will synchronously wait for the run to finish. This is useful to avoid periodic polling when waiting for Actor run to complete.  This endpoint does not require the authentication token. Instead, calls are authenticated using a hard-to-guess ID of the run. However, if you access the endpoint without the token, certain attributes, such as &#x60;usageUsd&#x60; and &#x60;usageTotalUsd&#x60;, will be hidden. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (RunResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<RunResponse>> ActorRunGetWithHttpInfoAsync(string runId, double? waitForFinish = default(double?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Metamorph run
        /// </summary>
        /// <remarks>
        /// Transforms an Actor run into a run of another Actor with a new input.  This is useful if you want to use another Actor to finish the work of your current Actor run, without the need to create a completely new run and waiting for its finish.  For the users of your Actors, the metamorph operation is transparent, they will just see your Actor got the work done.  Internally, the system stops the Docker container corresponding to the Actor run and starts a new container using a different Docker image.  All the default storages are preserved and the new input is stored under the &#x60;INPUT-METAMORPH-1&#x60; key in the same default key-value store.  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/development/programming-interface/metamorph). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Actor run ID.</param>
        /// <param name="targetActorId">ID of a target Actor that the run should be transformed into.</param>
        /// <param name="build">Optional build of the target Actor.  It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the target Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of RunResponse</returns>
        System.Threading.Tasks.Task<RunResponse> ActorRunMetamorphPostAsync(string runId, string targetActorId, string? build = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Metamorph run
        /// </summary>
        /// <remarks>
        /// Transforms an Actor run into a run of another Actor with a new input.  This is useful if you want to use another Actor to finish the work of your current Actor run, without the need to create a completely new run and waiting for its finish.  For the users of your Actors, the metamorph operation is transparent, they will just see your Actor got the work done.  Internally, the system stops the Docker container corresponding to the Actor run and starts a new container using a different Docker image.  All the default storages are preserved and the new input is stored under the &#x60;INPUT-METAMORPH-1&#x60; key in the same default key-value store.  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/development/programming-interface/metamorph). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Actor run ID.</param>
        /// <param name="targetActorId">ID of a target Actor that the run should be transformed into.</param>
        /// <param name="build">Optional build of the target Actor.  It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the target Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (RunResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<RunResponse>> ActorRunMetamorphPostWithHttpInfoAsync(string runId, string targetActorId, string? build = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Update status message
        /// </summary>
        /// <remarks>
        /// You can set a single status message on your run that will be displayed in the Apify Console UI. During an Actor run, you will typically do this in order to inform users of your Actor about the Actor&#39;s progress.  The request body must contain &#x60;runId&#x60; and &#x60;statusMessage&#x60; properties. The &#x60;isStatusMessageTerminal&#x60; property is optional and it indicates if the status message is the very last one. In the absence of a status message, the platform will try to substitute sensible defaults. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="actorRunPutRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of RunResponse</returns>
        System.Threading.Tasks.Task<RunResponse> ActorRunPutAsync(string runId, ActorRunPutRequest actorRunPutRequest, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Update status message
        /// </summary>
        /// <remarks>
        /// You can set a single status message on your run that will be displayed in the Apify Console UI. During an Actor run, you will typically do this in order to inform users of your Actor about the Actor&#39;s progress.  The request body must contain &#x60;runId&#x60; and &#x60;statusMessage&#x60; properties. The &#x60;isStatusMessageTerminal&#x60; property is optional and it indicates if the status message is the very last one. In the absence of a status message, the platform will try to substitute sensible defaults. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="actorRunPutRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (RunResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<RunResponse>> ActorRunPutWithHttpInfoAsync(string runId, ActorRunPutRequest actorRunPutRequest, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Reboot run
        /// </summary>
        /// <remarks>
        /// Reboots an Actor run and returns an object that contains all the details about the rebooted run.  Only runs that are running, i.e. runs with status &#x60;RUNNING&#x60; can be rebooted.  The run&#39;s container will be restarted, so any data not persisted in the key-value store, dataset, or request queue will be lost. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Actor run ID.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of RunResponse</returns>
        System.Threading.Tasks.Task<RunResponse> ActorRunRebootPostAsync(string runId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Reboot run
        /// </summary>
        /// <remarks>
        /// Reboots an Actor run and returns an object that contains all the details about the rebooted run.  Only runs that are running, i.e. runs with status &#x60;RUNNING&#x60; can be rebooted.  The run&#39;s container will be restarted, so any data not persisted in the key-value store, dataset, or request queue will be lost. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Actor run ID.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (RunResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<RunResponse>> ActorRunRebootPostWithHttpInfoAsync(string runId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Get user runs list
        /// </summary>
        /// <remarks>
        /// Gets a list of all runs for a user. The response is a list of objects, where each object contains basic information about a single Actor run.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 array elements.  By default, the records are sorted by the &#x60;startedAt&#x60; field in ascending order. Therefore, you can use pagination to incrementally fetch all records while new ones are still being created. To sort the records in descending order, use &#x60;desc&#x3D;1&#x60; parameter. You can also filter runs by status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle)). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value (as well as the maximum) is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="status">Return only runs with the provided status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle))  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of GetUserRunsListResponse</returns>
        System.Threading.Tasks.Task<GetUserRunsListResponse> ActorRunsGetAsync(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), string? status = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Get user runs list
        /// </summary>
        /// <remarks>
        /// Gets a list of all runs for a user. The response is a list of objects, where each object contains basic information about a single Actor run.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 array elements.  By default, the records are sorted by the &#x60;startedAt&#x60; field in ascending order. Therefore, you can use pagination to incrementally fetch all records while new ones are still being created. To sort the records in descending order, use &#x60;desc&#x3D;1&#x60; parameter. You can also filter runs by status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle)). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value (as well as the maximum) is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="status">Return only runs with the provided status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle))  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (GetUserRunsListResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<GetUserRunsListResponse>> ActorRunsGetWithHttpInfoAsync(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), string? status = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Charge events in run
        /// </summary>
        /// <remarks>
        /// Charge for events in the run of your [pay per event Actor](https://docs.apify.com/platform/actors/running/actors-in-store#pay-per-event). The event you are charging for must be one of the configured events in your Actor. If the Actor is not set up as pay per event, or if the event is not configured, the endpoint will return an error. The endpoint must be called from the Actor run itself, with the same API token that the run was started with.  :::note  Pay per events Actors are still in alpha. Please, reach out to us with any questions or feedback.  ::: 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="chargeRunRequest">Define which event, and how many times, you want to charge for.</param>
        /// <param name="idempotencyKey">Always pass a unique idempotency key (any unique string) for each charge to avoid double charging in case of retries or network errors. (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of void</returns>
        System.Threading.Tasks.Task PostChargeRunAsync(string runId, ChargeRunRequest chargeRunRequest, string? idempotencyKey = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Charge events in run
        /// </summary>
        /// <remarks>
        /// Charge for events in the run of your [pay per event Actor](https://docs.apify.com/platform/actors/running/actors-in-store#pay-per-event). The event you are charging for must be one of the configured events in your Actor. If the Actor is not set up as pay per event, or if the event is not configured, the endpoint will return an error. The endpoint must be called from the Actor run itself, with the same API token that the run was started with.  :::note  Pay per events Actors are still in alpha. Please, reach out to us with any questions or feedback.  ::: 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="chargeRunRequest">Define which event, and how many times, you want to charge for.</param>
        /// <param name="idempotencyKey">Always pass a unique idempotency key (any unique string) for each charge to avoid double charging in case of retries or network errors. (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> PostChargeRunWithHttpInfoAsync(string runId, ChargeRunRequest chargeRunRequest, string? idempotencyKey = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Resurrect run
        /// </summary>
        /// <remarks>
        /// Resurrects a finished Actor run and returns an object that contains all the details about the resurrected run. Only finished runs, i.e. runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTED&#x60; and &#x60;TIMED-OUT&#x60; can be resurrected. Run status will be updated to RUNNING and its container will be restarted with the same storages (the same behaviour as when the run gets migrated to the new server).  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/running/runs-and-builds#resurrection-of-finished-run). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the run that is being resurrected (typically &#x60;latest&#x60;). (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the run that is being resurrected. (optional)</param>
        /// <param name="memory"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of RunResponse</returns>
        System.Threading.Tasks.Task<RunResponse> PostResurrectRunAsync(string runId, string? build = default(string?), decimal? timeout = default(decimal?), decimal? memory = default(decimal?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Resurrect run
        /// </summary>
        /// <remarks>
        /// Resurrects a finished Actor run and returns an object that contains all the details about the resurrected run. Only finished runs, i.e. runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTED&#x60; and &#x60;TIMED-OUT&#x60; can be resurrected. Run status will be updated to RUNNING and its container will be restarted with the same storages (the same behaviour as when the run gets migrated to the new server).  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/running/runs-and-builds#resurrection-of-finished-run). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the run that is being resurrected (typically &#x60;latest&#x60;). (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the run that is being resurrected. (optional)</param>
        /// <param name="memory"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (RunResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<RunResponse>> PostResurrectRunWithHttpInfoAsync(string runId, string? build = default(string?), decimal? timeout = default(decimal?), decimal? memory = default(decimal?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        #endregion Asynchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IActorRunsApi : IActorRunsApiSync, IActorRunsApiAsync
    {

    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public partial class ActorRunsApi : IDisposable, IActorRunsApi
    {
        private Apify.SDK.Client.ExceptionFactory _exceptionFactory = (name, response) => null;

        /// <summary>
        /// Initializes a new instance of the <see cref="ActorRunsApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <returns></returns>
        public ActorRunsApi() : this((string)null)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ActorRunsApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        public ActorRunsApi(string basePath)
        {
            this.Configuration = Apify.SDK.Client.Configuration.MergeConfigurations(
                Apify.SDK.Client.GlobalConfiguration.Instance,
                new Apify.SDK.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Apify.SDK.Client.ApiClient(this.Configuration.BasePath);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Apify.SDK.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ActorRunsApi"/> class using Configuration object.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        public ActorRunsApi(Apify.SDK.Client.Configuration configuration)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Configuration = Apify.SDK.Client.Configuration.MergeConfigurations(
                Apify.SDK.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Apify.SDK.Client.ApiClient(this.Configuration.BasePath);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Apify.SDK.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ActorRunsApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public ActorRunsApi(HttpClient client, HttpClientHandler handler = null) : this(client, (string)null, handler)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ActorRunsApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public ActorRunsApi(HttpClient client, string basePath, HttpClientHandler handler = null)
        {
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Apify.SDK.Client.Configuration.MergeConfigurations(
                Apify.SDK.Client.GlobalConfiguration.Instance,
                new Apify.SDK.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Apify.SDK.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Apify.SDK.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ActorRunsApi"/> class using Configuration object.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public ActorRunsApi(HttpClient client, Apify.SDK.Client.Configuration configuration, HttpClientHandler handler = null)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Apify.SDK.Client.Configuration.MergeConfigurations(
                Apify.SDK.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Apify.SDK.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Apify.SDK.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ActorRunsApi"/> class
        /// using a Configuration object and client instance.
        /// </summary>
        /// <param name="client">The client interface for synchronous API access.</param>
        /// <param name="asyncClient">The client interface for asynchronous API access.</param>
        /// <param name="configuration">The configuration object.</param>
        /// <exception cref="ArgumentNullException"></exception>
        public ActorRunsApi(Apify.SDK.Client.ISynchronousClient client, Apify.SDK.Client.IAsynchronousClient asyncClient, Apify.SDK.Client.IReadableConfiguration configuration)
        {
            if (client == null) throw new ArgumentNullException("client");
            if (asyncClient == null) throw new ArgumentNullException("asyncClient");
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Client = client;
            this.AsynchronousClient = asyncClient;
            this.Configuration = configuration;
            this.ExceptionFactory = Apify.SDK.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Disposes resources if they were created by us
        /// </summary>
        public void Dispose()
        {
            this.ApiClient?.Dispose();
        }

        /// <summary>
        /// Holds the ApiClient if created
        /// </summary>
        public Apify.SDK.Client.ApiClient ApiClient { get; set; } = null;

        /// <summary>
        /// The client for accessing this underlying API asynchronously.
        /// </summary>
        public Apify.SDK.Client.IAsynchronousClient AsynchronousClient { get; set; }

        /// <summary>
        /// The client for accessing this underlying API synchronously.
        /// </summary>
        public Apify.SDK.Client.ISynchronousClient Client { get; set; }

        /// <summary>
        /// Gets the base path of the API client.
        /// </summary>
        /// <value>The base path</value>
        public string GetBasePath()
        {
            return this.Configuration.BasePath;
        }

        /// <summary>
        /// Gets or sets the configuration object
        /// </summary>
        /// <value>An instance of the Configuration</value>
        public Apify.SDK.Client.IReadableConfiguration Configuration { get; set; }

        /// <summary>
        /// Provides a factory method hook for the creation of exceptions.
        /// </summary>
        public Apify.SDK.Client.ExceptionFactory ExceptionFactory
        {
            get
            {
                if (_exceptionFactory != null && _exceptionFactory.GetInvocationList().Length > 1)
                {
                    throw new InvalidOperationException("Multicast delegate for ExceptionFactory is unsupported.");
                }
                return _exceptionFactory;
            }
            set { _exceptionFactory = value; }
        }

        /// <summary>
        /// Abort run Aborts an Actor run and returns an object that contains all the details about the run.  Only runs that are starting or running are aborted. For runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTING&#x60; and &#x60;TIMED-OUT&#x60; this call does nothing. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="gracefully">If true passed, the Actor run will abort gracefully. It will send &#x60;aborting&#x60; and &#x60;persistState&#x60; event into run and force-stop the run after 30 seconds. It is helpful in cases where you plan to resurrect the run later.  (optional)</param>
        /// <returns>RunResponse</returns>
        public RunResponse ActorRunAbortPost(string runId, bool? gracefully = default(bool?))
        {
            Apify.SDK.Client.ApiResponse<RunResponse> localVarResponse = ActorRunAbortPostWithHttpInfo(runId, gracefully);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Abort run Aborts an Actor run and returns an object that contains all the details about the run.  Only runs that are starting or running are aborted. For runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTING&#x60; and &#x60;TIMED-OUT&#x60; this call does nothing. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="gracefully">If true passed, the Actor run will abort gracefully. It will send &#x60;aborting&#x60; and &#x60;persistState&#x60; event into run and force-stop the run after 30 seconds. It is helpful in cases where you plan to resurrect the run later.  (optional)</param>
        /// <returns>ApiResponse of RunResponse</returns>
        public Apify.SDK.Client.ApiResponse<RunResponse> ActorRunAbortPostWithHttpInfo(string runId, bool? gracefully = default(bool?))
        {
            // verify the required parameter 'runId' is set
            if (runId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'runId' when calling ActorRunsApi->ActorRunAbortPost");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("runId", Apify.SDK.Client.ClientUtils.ParameterToString(runId)); // path parameter
            if (gracefully != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "gracefully", gracefully));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<RunResponse>("/v2/actor-runs/{runId}/abort", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorRunAbortPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Abort run Aborts an Actor run and returns an object that contains all the details about the run.  Only runs that are starting or running are aborted. For runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTING&#x60; and &#x60;TIMED-OUT&#x60; this call does nothing. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="gracefully">If true passed, the Actor run will abort gracefully. It will send &#x60;aborting&#x60; and &#x60;persistState&#x60; event into run and force-stop the run after 30 seconds. It is helpful in cases where you plan to resurrect the run later.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of RunResponse</returns>
        public async System.Threading.Tasks.Task<RunResponse> ActorRunAbortPostAsync(string runId, bool? gracefully = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<RunResponse> localVarResponse = await ActorRunAbortPostWithHttpInfoAsync(runId, gracefully, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Abort run Aborts an Actor run and returns an object that contains all the details about the run.  Only runs that are starting or running are aborted. For runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTING&#x60; and &#x60;TIMED-OUT&#x60; this call does nothing. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="gracefully">If true passed, the Actor run will abort gracefully. It will send &#x60;aborting&#x60; and &#x60;persistState&#x60; event into run and force-stop the run after 30 seconds. It is helpful in cases where you plan to resurrect the run later.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (RunResponse)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<RunResponse>> ActorRunAbortPostWithHttpInfoAsync(string runId, bool? gracefully = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'runId' is set
            if (runId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'runId' when calling ActorRunsApi->ActorRunAbortPost");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("runId", Apify.SDK.Client.ClientUtils.ParameterToString(runId)); // path parameter
            if (gracefully != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "gracefully", gracefully));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<RunResponse>("/v2/actor-runs/{runId}/abort", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorRunAbortPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Delete run Delete the run. Only finished runs can be deleted. Only the person or organization that initiated the run can delete it. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <returns></returns>
        public void ActorRunDelete(string runId)
        {
            ActorRunDeleteWithHttpInfo(runId);
        }

        /// <summary>
        /// Delete run Delete the run. Only finished runs can be deleted. Only the person or organization that initiated the run can delete it. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <returns>ApiResponse of Object(void)</returns>
        public Apify.SDK.Client.ApiResponse<Object> ActorRunDeleteWithHttpInfo(string runId)
        {
            // verify the required parameter 'runId' is set
            if (runId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'runId' when calling ActorRunsApi->ActorRunDelete");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("runId", Apify.SDK.Client.ClientUtils.ParameterToString(runId)); // path parameter

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Delete<Object>("/v2/actor-runs/{runId}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorRunDelete", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Delete run Delete the run. Only finished runs can be deleted. Only the person or organization that initiated the run can delete it. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of void</returns>
        public async System.Threading.Tasks.Task ActorRunDeleteAsync(string runId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            await ActorRunDeleteWithHttpInfoAsync(runId, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Delete run Delete the run. Only finished runs can be deleted. Only the person or organization that initiated the run can delete it. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<Object>> ActorRunDeleteWithHttpInfoAsync(string runId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'runId' is set
            if (runId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'runId' when calling ActorRunsApi->ActorRunDelete");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("runId", Apify.SDK.Client.ClientUtils.ParameterToString(runId)); // path parameter

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.DeleteAsync<Object>("/v2/actor-runs/{runId}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorRunDelete", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get run This is not a single endpoint, but an entire group of endpoints that lets you retrieve the run or any of its default storages.  The endpoints accept the same HTTP methods and query parameters as the respective storage endpoints.  The base path that represents the Actor run object is:  &#x60;/v2/actor-runs/{runId}{?token}&#x60;  In order to access the default storages of the Actor run, i.e. log, key-value store, dataset and request queue, use the following endpoints:  * &#x60;/v2/actor-runs/{runId}/log{?token}&#x60; * &#x60;/v2/actor-runs/{runId}/key-value-store{?token}&#x60; * &#x60;/v2/actor-runs/{runId}/dataset{?token}&#x60; * &#x60;/v2/actor-runs/{runId}/request-queue{?token}&#x60;  These API endpoints have the same usage as the equivalent storage endpoints.  For example, &#x60;/v2/actor-runs/{runId}/key-value-store&#x60; has the same HTTP method and parameters as the [Key-value store object](#/reference/key-value-stores/store-object) endpoint.  Additionally, each of the above API endpoints supports all sub-endpoints of the original one:  #### Log  * &#x60;/v2/actor-runs/{runId}/log&#x60; [Log](#/reference/logs)  #### Key-value store  * &#x60;/v2/actor-runs/{runId}/key-value-store/keys{?token}&#x60; [Key collection](#/reference/key-value-stores/key-collection) * &#x60;/v2/actor-runs/{runId}/key-value-store/records/{recordKey}{?token}&#x60; [Record](#/reference/key-value-stores/record)  #### Dataset  * &#x60;/v2/actor-runs/{runId}/dataset/items{?token}&#x60; [Item collection](#/reference/datasets/item-collection)  #### Request queue  * &#x60;/v2/actor-runs/{runId}/request-queue/requests{?token}&#x60; [Request collection](#/reference/request-queues/request-collection) * &#x60;/v2/actor-runs/{runId}/request-queue/requests/{requestId}{?token}&#x60; [Request collection](#/reference/request-queues/request) * &#x60;/v2/actor-runs/{runId}/request-queue/head{?token}&#x60; [Queue head](#/reference/request-queues/queue-head)  For example, to download data from a dataset of the Actor run in XML format, send HTTP GET request to the following URL:  &#x60;&#x60;&#x60; https://api.apify.com/v2/actor-runs/{runId}/dataset/items?format&#x3D;xml &#x60;&#x60;&#x60;  In order to save new items to the dataset, send HTTP POST request with JSON payload to the same URL.  Gets an object that contains all the details about a specific run of an Actor.  By passing the optional &#x60;waitForFinish&#x60; parameter the API endpoint will synchronously wait for the run to finish. This is useful to avoid periodic polling when waiting for Actor run to complete.  This endpoint does not require the authentication token. Instead, calls are authenticated using a hard-to-guess ID of the run. However, if you access the endpoint without the token, certain attributes, such as &#x60;usageUsd&#x60; and &#x60;usageTotalUsd&#x60;, will be hidden. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <returns>RunResponse</returns>
        public RunResponse ActorRunGet(string runId, double? waitForFinish = default(double?))
        {
            Apify.SDK.Client.ApiResponse<RunResponse> localVarResponse = ActorRunGetWithHttpInfo(runId, waitForFinish);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get run This is not a single endpoint, but an entire group of endpoints that lets you retrieve the run or any of its default storages.  The endpoints accept the same HTTP methods and query parameters as the respective storage endpoints.  The base path that represents the Actor run object is:  &#x60;/v2/actor-runs/{runId}{?token}&#x60;  In order to access the default storages of the Actor run, i.e. log, key-value store, dataset and request queue, use the following endpoints:  * &#x60;/v2/actor-runs/{runId}/log{?token}&#x60; * &#x60;/v2/actor-runs/{runId}/key-value-store{?token}&#x60; * &#x60;/v2/actor-runs/{runId}/dataset{?token}&#x60; * &#x60;/v2/actor-runs/{runId}/request-queue{?token}&#x60;  These API endpoints have the same usage as the equivalent storage endpoints.  For example, &#x60;/v2/actor-runs/{runId}/key-value-store&#x60; has the same HTTP method and parameters as the [Key-value store object](#/reference/key-value-stores/store-object) endpoint.  Additionally, each of the above API endpoints supports all sub-endpoints of the original one:  #### Log  * &#x60;/v2/actor-runs/{runId}/log&#x60; [Log](#/reference/logs)  #### Key-value store  * &#x60;/v2/actor-runs/{runId}/key-value-store/keys{?token}&#x60; [Key collection](#/reference/key-value-stores/key-collection) * &#x60;/v2/actor-runs/{runId}/key-value-store/records/{recordKey}{?token}&#x60; [Record](#/reference/key-value-stores/record)  #### Dataset  * &#x60;/v2/actor-runs/{runId}/dataset/items{?token}&#x60; [Item collection](#/reference/datasets/item-collection)  #### Request queue  * &#x60;/v2/actor-runs/{runId}/request-queue/requests{?token}&#x60; [Request collection](#/reference/request-queues/request-collection) * &#x60;/v2/actor-runs/{runId}/request-queue/requests/{requestId}{?token}&#x60; [Request collection](#/reference/request-queues/request) * &#x60;/v2/actor-runs/{runId}/request-queue/head{?token}&#x60; [Queue head](#/reference/request-queues/queue-head)  For example, to download data from a dataset of the Actor run in XML format, send HTTP GET request to the following URL:  &#x60;&#x60;&#x60; https://api.apify.com/v2/actor-runs/{runId}/dataset/items?format&#x3D;xml &#x60;&#x60;&#x60;  In order to save new items to the dataset, send HTTP POST request with JSON payload to the same URL.  Gets an object that contains all the details about a specific run of an Actor.  By passing the optional &#x60;waitForFinish&#x60; parameter the API endpoint will synchronously wait for the run to finish. This is useful to avoid periodic polling when waiting for Actor run to complete.  This endpoint does not require the authentication token. Instead, calls are authenticated using a hard-to-guess ID of the run. However, if you access the endpoint without the token, certain attributes, such as &#x60;usageUsd&#x60; and &#x60;usageTotalUsd&#x60;, will be hidden. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <returns>ApiResponse of RunResponse</returns>
        public Apify.SDK.Client.ApiResponse<RunResponse> ActorRunGetWithHttpInfo(string runId, double? waitForFinish = default(double?))
        {
            // verify the required parameter 'runId' is set
            if (runId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'runId' when calling ActorRunsApi->ActorRunGet");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("runId", Apify.SDK.Client.ClientUtils.ParameterToString(runId)); // path parameter
            if (waitForFinish != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "waitForFinish", waitForFinish));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<RunResponse>("/v2/actor-runs/{runId}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorRunGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get run This is not a single endpoint, but an entire group of endpoints that lets you retrieve the run or any of its default storages.  The endpoints accept the same HTTP methods and query parameters as the respective storage endpoints.  The base path that represents the Actor run object is:  &#x60;/v2/actor-runs/{runId}{?token}&#x60;  In order to access the default storages of the Actor run, i.e. log, key-value store, dataset and request queue, use the following endpoints:  * &#x60;/v2/actor-runs/{runId}/log{?token}&#x60; * &#x60;/v2/actor-runs/{runId}/key-value-store{?token}&#x60; * &#x60;/v2/actor-runs/{runId}/dataset{?token}&#x60; * &#x60;/v2/actor-runs/{runId}/request-queue{?token}&#x60;  These API endpoints have the same usage as the equivalent storage endpoints.  For example, &#x60;/v2/actor-runs/{runId}/key-value-store&#x60; has the same HTTP method and parameters as the [Key-value store object](#/reference/key-value-stores/store-object) endpoint.  Additionally, each of the above API endpoints supports all sub-endpoints of the original one:  #### Log  * &#x60;/v2/actor-runs/{runId}/log&#x60; [Log](#/reference/logs)  #### Key-value store  * &#x60;/v2/actor-runs/{runId}/key-value-store/keys{?token}&#x60; [Key collection](#/reference/key-value-stores/key-collection) * &#x60;/v2/actor-runs/{runId}/key-value-store/records/{recordKey}{?token}&#x60; [Record](#/reference/key-value-stores/record)  #### Dataset  * &#x60;/v2/actor-runs/{runId}/dataset/items{?token}&#x60; [Item collection](#/reference/datasets/item-collection)  #### Request queue  * &#x60;/v2/actor-runs/{runId}/request-queue/requests{?token}&#x60; [Request collection](#/reference/request-queues/request-collection) * &#x60;/v2/actor-runs/{runId}/request-queue/requests/{requestId}{?token}&#x60; [Request collection](#/reference/request-queues/request) * &#x60;/v2/actor-runs/{runId}/request-queue/head{?token}&#x60; [Queue head](#/reference/request-queues/queue-head)  For example, to download data from a dataset of the Actor run in XML format, send HTTP GET request to the following URL:  &#x60;&#x60;&#x60; https://api.apify.com/v2/actor-runs/{runId}/dataset/items?format&#x3D;xml &#x60;&#x60;&#x60;  In order to save new items to the dataset, send HTTP POST request with JSON payload to the same URL.  Gets an object that contains all the details about a specific run of an Actor.  By passing the optional &#x60;waitForFinish&#x60; parameter the API endpoint will synchronously wait for the run to finish. This is useful to avoid periodic polling when waiting for Actor run to complete.  This endpoint does not require the authentication token. Instead, calls are authenticated using a hard-to-guess ID of the run. However, if you access the endpoint without the token, certain attributes, such as &#x60;usageUsd&#x60; and &#x60;usageTotalUsd&#x60;, will be hidden. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of RunResponse</returns>
        public async System.Threading.Tasks.Task<RunResponse> ActorRunGetAsync(string runId, double? waitForFinish = default(double?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<RunResponse> localVarResponse = await ActorRunGetWithHttpInfoAsync(runId, waitForFinish, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get run This is not a single endpoint, but an entire group of endpoints that lets you retrieve the run or any of its default storages.  The endpoints accept the same HTTP methods and query parameters as the respective storage endpoints.  The base path that represents the Actor run object is:  &#x60;/v2/actor-runs/{runId}{?token}&#x60;  In order to access the default storages of the Actor run, i.e. log, key-value store, dataset and request queue, use the following endpoints:  * &#x60;/v2/actor-runs/{runId}/log{?token}&#x60; * &#x60;/v2/actor-runs/{runId}/key-value-store{?token}&#x60; * &#x60;/v2/actor-runs/{runId}/dataset{?token}&#x60; * &#x60;/v2/actor-runs/{runId}/request-queue{?token}&#x60;  These API endpoints have the same usage as the equivalent storage endpoints.  For example, &#x60;/v2/actor-runs/{runId}/key-value-store&#x60; has the same HTTP method and parameters as the [Key-value store object](#/reference/key-value-stores/store-object) endpoint.  Additionally, each of the above API endpoints supports all sub-endpoints of the original one:  #### Log  * &#x60;/v2/actor-runs/{runId}/log&#x60; [Log](#/reference/logs)  #### Key-value store  * &#x60;/v2/actor-runs/{runId}/key-value-store/keys{?token}&#x60; [Key collection](#/reference/key-value-stores/key-collection) * &#x60;/v2/actor-runs/{runId}/key-value-store/records/{recordKey}{?token}&#x60; [Record](#/reference/key-value-stores/record)  #### Dataset  * &#x60;/v2/actor-runs/{runId}/dataset/items{?token}&#x60; [Item collection](#/reference/datasets/item-collection)  #### Request queue  * &#x60;/v2/actor-runs/{runId}/request-queue/requests{?token}&#x60; [Request collection](#/reference/request-queues/request-collection) * &#x60;/v2/actor-runs/{runId}/request-queue/requests/{requestId}{?token}&#x60; [Request collection](#/reference/request-queues/request) * &#x60;/v2/actor-runs/{runId}/request-queue/head{?token}&#x60; [Queue head](#/reference/request-queues/queue-head)  For example, to download data from a dataset of the Actor run in XML format, send HTTP GET request to the following URL:  &#x60;&#x60;&#x60; https://api.apify.com/v2/actor-runs/{runId}/dataset/items?format&#x3D;xml &#x60;&#x60;&#x60;  In order to save new items to the dataset, send HTTP POST request with JSON payload to the same URL.  Gets an object that contains all the details about a specific run of an Actor.  By passing the optional &#x60;waitForFinish&#x60; parameter the API endpoint will synchronously wait for the run to finish. This is useful to avoid periodic polling when waiting for Actor run to complete.  This endpoint does not require the authentication token. Instead, calls are authenticated using a hard-to-guess ID of the run. However, if you access the endpoint without the token, certain attributes, such as &#x60;usageUsd&#x60; and &#x60;usageTotalUsd&#x60;, will be hidden. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="waitForFinish">The maximum number of seconds the server waits for the run to finish. By default it is &#x60;0&#x60;, the maximum value is &#x60;60&#x60;. &lt;!- - MAX_ACTOR_JOB_ASYNC_WAIT_SECS - -&gt; If the build finishes in time then the returned run object will have a terminal status (e.g. &#x60;SUCCEEDED&#x60;), otherwise it will have a transitional status (e.g. &#x60;RUNNING&#x60;).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (RunResponse)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<RunResponse>> ActorRunGetWithHttpInfoAsync(string runId, double? waitForFinish = default(double?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'runId' is set
            if (runId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'runId' when calling ActorRunsApi->ActorRunGet");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("runId", Apify.SDK.Client.ClientUtils.ParameterToString(runId)); // path parameter
            if (waitForFinish != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "waitForFinish", waitForFinish));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<RunResponse>("/v2/actor-runs/{runId}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorRunGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Metamorph run Transforms an Actor run into a run of another Actor with a new input.  This is useful if you want to use another Actor to finish the work of your current Actor run, without the need to create a completely new run and waiting for its finish.  For the users of your Actors, the metamorph operation is transparent, they will just see your Actor got the work done.  Internally, the system stops the Docker container corresponding to the Actor run and starts a new container using a different Docker image.  All the default storages are preserved and the new input is stored under the &#x60;INPUT-METAMORPH-1&#x60; key in the same default key-value store.  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/development/programming-interface/metamorph). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Actor run ID.</param>
        /// <param name="targetActorId">ID of a target Actor that the run should be transformed into.</param>
        /// <param name="build">Optional build of the target Actor.  It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the target Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <returns>RunResponse</returns>
        public RunResponse ActorRunMetamorphPost(string runId, string targetActorId, string? build = default(string?))
        {
            Apify.SDK.Client.ApiResponse<RunResponse> localVarResponse = ActorRunMetamorphPostWithHttpInfo(runId, targetActorId, build);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Metamorph run Transforms an Actor run into a run of another Actor with a new input.  This is useful if you want to use another Actor to finish the work of your current Actor run, without the need to create a completely new run and waiting for its finish.  For the users of your Actors, the metamorph operation is transparent, they will just see your Actor got the work done.  Internally, the system stops the Docker container corresponding to the Actor run and starts a new container using a different Docker image.  All the default storages are preserved and the new input is stored under the &#x60;INPUT-METAMORPH-1&#x60; key in the same default key-value store.  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/development/programming-interface/metamorph). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Actor run ID.</param>
        /// <param name="targetActorId">ID of a target Actor that the run should be transformed into.</param>
        /// <param name="build">Optional build of the target Actor.  It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the target Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <returns>ApiResponse of RunResponse</returns>
        public Apify.SDK.Client.ApiResponse<RunResponse> ActorRunMetamorphPostWithHttpInfo(string runId, string targetActorId, string? build = default(string?))
        {
            // verify the required parameter 'runId' is set
            if (runId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'runId' when calling ActorRunsApi->ActorRunMetamorphPost");

            // verify the required parameter 'targetActorId' is set
            if (targetActorId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'targetActorId' when calling ActorRunsApi->ActorRunMetamorphPost");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("runId", Apify.SDK.Client.ClientUtils.ParameterToString(runId)); // path parameter
            localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "targetActorId", targetActorId));
            if (build != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "build", build));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<RunResponse>("/v2/actor-runs/{runId}/metamorph", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorRunMetamorphPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Metamorph run Transforms an Actor run into a run of another Actor with a new input.  This is useful if you want to use another Actor to finish the work of your current Actor run, without the need to create a completely new run and waiting for its finish.  For the users of your Actors, the metamorph operation is transparent, they will just see your Actor got the work done.  Internally, the system stops the Docker container corresponding to the Actor run and starts a new container using a different Docker image.  All the default storages are preserved and the new input is stored under the &#x60;INPUT-METAMORPH-1&#x60; key in the same default key-value store.  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/development/programming-interface/metamorph). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Actor run ID.</param>
        /// <param name="targetActorId">ID of a target Actor that the run should be transformed into.</param>
        /// <param name="build">Optional build of the target Actor.  It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the target Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of RunResponse</returns>
        public async System.Threading.Tasks.Task<RunResponse> ActorRunMetamorphPostAsync(string runId, string targetActorId, string? build = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<RunResponse> localVarResponse = await ActorRunMetamorphPostWithHttpInfoAsync(runId, targetActorId, build, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Metamorph run Transforms an Actor run into a run of another Actor with a new input.  This is useful if you want to use another Actor to finish the work of your current Actor run, without the need to create a completely new run and waiting for its finish.  For the users of your Actors, the metamorph operation is transparent, they will just see your Actor got the work done.  Internally, the system stops the Docker container corresponding to the Actor run and starts a new container using a different Docker image.  All the default storages are preserved and the new input is stored under the &#x60;INPUT-METAMORPH-1&#x60; key in the same default key-value store.  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/development/programming-interface/metamorph). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Actor run ID.</param>
        /// <param name="targetActorId">ID of a target Actor that the run should be transformed into.</param>
        /// <param name="build">Optional build of the target Actor.  It can be either a build tag or build number. By default, the run uses the build specified in the default run configuration for the target Actor (typically &#x60;latest&#x60;).  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (RunResponse)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<RunResponse>> ActorRunMetamorphPostWithHttpInfoAsync(string runId, string targetActorId, string? build = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'runId' is set
            if (runId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'runId' when calling ActorRunsApi->ActorRunMetamorphPost");

            // verify the required parameter 'targetActorId' is set
            if (targetActorId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'targetActorId' when calling ActorRunsApi->ActorRunMetamorphPost");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("runId", Apify.SDK.Client.ClientUtils.ParameterToString(runId)); // path parameter
            localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "targetActorId", targetActorId));
            if (build != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "build", build));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<RunResponse>("/v2/actor-runs/{runId}/metamorph", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorRunMetamorphPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Update status message You can set a single status message on your run that will be displayed in the Apify Console UI. During an Actor run, you will typically do this in order to inform users of your Actor about the Actor&#39;s progress.  The request body must contain &#x60;runId&#x60; and &#x60;statusMessage&#x60; properties. The &#x60;isStatusMessageTerminal&#x60; property is optional and it indicates if the status message is the very last one. In the absence of a status message, the platform will try to substitute sensible defaults. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="actorRunPutRequest"></param>
        /// <returns>RunResponse</returns>
        public RunResponse ActorRunPut(string runId, ActorRunPutRequest actorRunPutRequest)
        {
            Apify.SDK.Client.ApiResponse<RunResponse> localVarResponse = ActorRunPutWithHttpInfo(runId, actorRunPutRequest);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Update status message You can set a single status message on your run that will be displayed in the Apify Console UI. During an Actor run, you will typically do this in order to inform users of your Actor about the Actor&#39;s progress.  The request body must contain &#x60;runId&#x60; and &#x60;statusMessage&#x60; properties. The &#x60;isStatusMessageTerminal&#x60; property is optional and it indicates if the status message is the very last one. In the absence of a status message, the platform will try to substitute sensible defaults. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="actorRunPutRequest"></param>
        /// <returns>ApiResponse of RunResponse</returns>
        public Apify.SDK.Client.ApiResponse<RunResponse> ActorRunPutWithHttpInfo(string runId, ActorRunPutRequest actorRunPutRequest)
        {
            // verify the required parameter 'runId' is set
            if (runId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'runId' when calling ActorRunsApi->ActorRunPut");

            // verify the required parameter 'actorRunPutRequest' is set
            if (actorRunPutRequest == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorRunPutRequest' when calling ActorRunsApi->ActorRunPut");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("runId", Apify.SDK.Client.ClientUtils.ParameterToString(runId)); // path parameter
            localVarRequestOptions.Data = actorRunPutRequest;

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Put<RunResponse>("/v2/actor-runs/{runId}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorRunPut", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Update status message You can set a single status message on your run that will be displayed in the Apify Console UI. During an Actor run, you will typically do this in order to inform users of your Actor about the Actor&#39;s progress.  The request body must contain &#x60;runId&#x60; and &#x60;statusMessage&#x60; properties. The &#x60;isStatusMessageTerminal&#x60; property is optional and it indicates if the status message is the very last one. In the absence of a status message, the platform will try to substitute sensible defaults. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="actorRunPutRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of RunResponse</returns>
        public async System.Threading.Tasks.Task<RunResponse> ActorRunPutAsync(string runId, ActorRunPutRequest actorRunPutRequest, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<RunResponse> localVarResponse = await ActorRunPutWithHttpInfoAsync(runId, actorRunPutRequest, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Update status message You can set a single status message on your run that will be displayed in the Apify Console UI. During an Actor run, you will typically do this in order to inform users of your Actor about the Actor&#39;s progress.  The request body must contain &#x60;runId&#x60; and &#x60;statusMessage&#x60; properties. The &#x60;isStatusMessageTerminal&#x60; property is optional and it indicates if the status message is the very last one. In the absence of a status message, the platform will try to substitute sensible defaults. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="actorRunPutRequest"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (RunResponse)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<RunResponse>> ActorRunPutWithHttpInfoAsync(string runId, ActorRunPutRequest actorRunPutRequest, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'runId' is set
            if (runId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'runId' when calling ActorRunsApi->ActorRunPut");

            // verify the required parameter 'actorRunPutRequest' is set
            if (actorRunPutRequest == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'actorRunPutRequest' when calling ActorRunsApi->ActorRunPut");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("runId", Apify.SDK.Client.ClientUtils.ParameterToString(runId)); // path parameter
            localVarRequestOptions.Data = actorRunPutRequest;

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PutAsync<RunResponse>("/v2/actor-runs/{runId}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorRunPut", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Reboot run Reboots an Actor run and returns an object that contains all the details about the rebooted run.  Only runs that are running, i.e. runs with status &#x60;RUNNING&#x60; can be rebooted.  The run&#39;s container will be restarted, so any data not persisted in the key-value store, dataset, or request queue will be lost. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Actor run ID.</param>
        /// <returns>RunResponse</returns>
        public RunResponse ActorRunRebootPost(string runId)
        {
            Apify.SDK.Client.ApiResponse<RunResponse> localVarResponse = ActorRunRebootPostWithHttpInfo(runId);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Reboot run Reboots an Actor run and returns an object that contains all the details about the rebooted run.  Only runs that are running, i.e. runs with status &#x60;RUNNING&#x60; can be rebooted.  The run&#39;s container will be restarted, so any data not persisted in the key-value store, dataset, or request queue will be lost. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Actor run ID.</param>
        /// <returns>ApiResponse of RunResponse</returns>
        public Apify.SDK.Client.ApiResponse<RunResponse> ActorRunRebootPostWithHttpInfo(string runId)
        {
            // verify the required parameter 'runId' is set
            if (runId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'runId' when calling ActorRunsApi->ActorRunRebootPost");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("runId", Apify.SDK.Client.ClientUtils.ParameterToString(runId)); // path parameter

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<RunResponse>("/v2/actor-runs/{runId}/reboot", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorRunRebootPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Reboot run Reboots an Actor run and returns an object that contains all the details about the rebooted run.  Only runs that are running, i.e. runs with status &#x60;RUNNING&#x60; can be rebooted.  The run&#39;s container will be restarted, so any data not persisted in the key-value store, dataset, or request queue will be lost. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Actor run ID.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of RunResponse</returns>
        public async System.Threading.Tasks.Task<RunResponse> ActorRunRebootPostAsync(string runId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<RunResponse> localVarResponse = await ActorRunRebootPostWithHttpInfoAsync(runId, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Reboot run Reboots an Actor run and returns an object that contains all the details about the rebooted run.  Only runs that are running, i.e. runs with status &#x60;RUNNING&#x60; can be rebooted.  The run&#39;s container will be restarted, so any data not persisted in the key-value store, dataset, or request queue will be lost. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Actor run ID.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (RunResponse)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<RunResponse>> ActorRunRebootPostWithHttpInfoAsync(string runId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'runId' is set
            if (runId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'runId' when calling ActorRunsApi->ActorRunRebootPost");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("runId", Apify.SDK.Client.ClientUtils.ParameterToString(runId)); // path parameter

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<RunResponse>("/v2/actor-runs/{runId}/reboot", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorRunRebootPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get user runs list Gets a list of all runs for a user. The response is a list of objects, where each object contains basic information about a single Actor run.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 array elements.  By default, the records are sorted by the &#x60;startedAt&#x60; field in ascending order. Therefore, you can use pagination to incrementally fetch all records while new ones are still being created. To sort the records in descending order, use &#x60;desc&#x3D;1&#x60; parameter. You can also filter runs by status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle)). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value (as well as the maximum) is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="status">Return only runs with the provided status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle))  (optional)</param>
        /// <returns>GetUserRunsListResponse</returns>
        public GetUserRunsListResponse ActorRunsGet(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), string? status = default(string?))
        {
            Apify.SDK.Client.ApiResponse<GetUserRunsListResponse> localVarResponse = ActorRunsGetWithHttpInfo(offset, limit, desc, status);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get user runs list Gets a list of all runs for a user. The response is a list of objects, where each object contains basic information about a single Actor run.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 array elements.  By default, the records are sorted by the &#x60;startedAt&#x60; field in ascending order. Therefore, you can use pagination to incrementally fetch all records while new ones are still being created. To sort the records in descending order, use &#x60;desc&#x3D;1&#x60; parameter. You can also filter runs by status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle)). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value (as well as the maximum) is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="status">Return only runs with the provided status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle))  (optional)</param>
        /// <returns>ApiResponse of GetUserRunsListResponse</returns>
        public Apify.SDK.Client.ApiResponse<GetUserRunsListResponse> ActorRunsGetWithHttpInfo(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), string? status = default(string?))
        {
            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (desc != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "desc", desc));
            }
            if (status != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "status", status));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<GetUserRunsListResponse>("/v2/actor-runs", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorRunsGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get user runs list Gets a list of all runs for a user. The response is a list of objects, where each object contains basic information about a single Actor run.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 array elements.  By default, the records are sorted by the &#x60;startedAt&#x60; field in ascending order. Therefore, you can use pagination to incrementally fetch all records while new ones are still being created. To sort the records in descending order, use &#x60;desc&#x3D;1&#x60; parameter. You can also filter runs by status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle)). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value (as well as the maximum) is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="status">Return only runs with the provided status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle))  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of GetUserRunsListResponse</returns>
        public async System.Threading.Tasks.Task<GetUserRunsListResponse> ActorRunsGetAsync(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), string? status = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<GetUserRunsListResponse> localVarResponse = await ActorRunsGetWithHttpInfoAsync(offset, limit, desc, status, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get user runs list Gets a list of all runs for a user. The response is a list of objects, where each object contains basic information about a single Actor run.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 array elements.  By default, the records are sorted by the &#x60;startedAt&#x60; field in ascending order. Therefore, you can use pagination to incrementally fetch all records while new ones are still being created. To sort the records in descending order, use &#x60;desc&#x3D;1&#x60; parameter. You can also filter runs by status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle)). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of array elements that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of array elements to return. The default value (as well as the maximum) is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;startedAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="status">Return only runs with the provided status ([available statuses](https://docs.apify.com/platform/actors/running/runs-and-builds#lifecycle))  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (GetUserRunsListResponse)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<GetUserRunsListResponse>> ActorRunsGetWithHttpInfoAsync(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), string? status = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (desc != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "desc", desc));
            }
            if (status != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "status", status));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<GetUserRunsListResponse>("/v2/actor-runs", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("ActorRunsGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Charge events in run Charge for events in the run of your [pay per event Actor](https://docs.apify.com/platform/actors/running/actors-in-store#pay-per-event). The event you are charging for must be one of the configured events in your Actor. If the Actor is not set up as pay per event, or if the event is not configured, the endpoint will return an error. The endpoint must be called from the Actor run itself, with the same API token that the run was started with.  :::note  Pay per events Actors are still in alpha. Please, reach out to us with any questions or feedback.  ::: 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="chargeRunRequest">Define which event, and how many times, you want to charge for.</param>
        /// <param name="idempotencyKey">Always pass a unique idempotency key (any unique string) for each charge to avoid double charging in case of retries or network errors. (optional)</param>
        /// <returns></returns>
        public void PostChargeRun(string runId, ChargeRunRequest chargeRunRequest, string? idempotencyKey = default(string?))
        {
            PostChargeRunWithHttpInfo(runId, chargeRunRequest, idempotencyKey);
        }

        /// <summary>
        /// Charge events in run Charge for events in the run of your [pay per event Actor](https://docs.apify.com/platform/actors/running/actors-in-store#pay-per-event). The event you are charging for must be one of the configured events in your Actor. If the Actor is not set up as pay per event, or if the event is not configured, the endpoint will return an error. The endpoint must be called from the Actor run itself, with the same API token that the run was started with.  :::note  Pay per events Actors are still in alpha. Please, reach out to us with any questions or feedback.  ::: 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="chargeRunRequest">Define which event, and how many times, you want to charge for.</param>
        /// <param name="idempotencyKey">Always pass a unique idempotency key (any unique string) for each charge to avoid double charging in case of retries or network errors. (optional)</param>
        /// <returns>ApiResponse of Object(void)</returns>
        public Apify.SDK.Client.ApiResponse<Object> PostChargeRunWithHttpInfo(string runId, ChargeRunRequest chargeRunRequest, string? idempotencyKey = default(string?))
        {
            // verify the required parameter 'runId' is set
            if (runId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'runId' when calling ActorRunsApi->PostChargeRun");

            // verify the required parameter 'chargeRunRequest' is set
            if (chargeRunRequest == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'chargeRunRequest' when calling ActorRunsApi->PostChargeRun");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("runId", Apify.SDK.Client.ClientUtils.ParameterToString(runId)); // path parameter
            if (idempotencyKey != null)
            {
                localVarRequestOptions.HeaderParameters.Add("idempotency-key", Apify.SDK.Client.ClientUtils.ParameterToString(idempotencyKey)); // header parameter
            }
            localVarRequestOptions.Data = chargeRunRequest;

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<Object>("/v2/actor-runs/{runId}/charge", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("PostChargeRun", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Charge events in run Charge for events in the run of your [pay per event Actor](https://docs.apify.com/platform/actors/running/actors-in-store#pay-per-event). The event you are charging for must be one of the configured events in your Actor. If the Actor is not set up as pay per event, or if the event is not configured, the endpoint will return an error. The endpoint must be called from the Actor run itself, with the same API token that the run was started with.  :::note  Pay per events Actors are still in alpha. Please, reach out to us with any questions or feedback.  ::: 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="chargeRunRequest">Define which event, and how many times, you want to charge for.</param>
        /// <param name="idempotencyKey">Always pass a unique idempotency key (any unique string) for each charge to avoid double charging in case of retries or network errors. (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of void</returns>
        public async System.Threading.Tasks.Task PostChargeRunAsync(string runId, ChargeRunRequest chargeRunRequest, string? idempotencyKey = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            await PostChargeRunWithHttpInfoAsync(runId, chargeRunRequest, idempotencyKey, cancellationToken).ConfigureAwait(false);
        }

        /// <summary>
        /// Charge events in run Charge for events in the run of your [pay per event Actor](https://docs.apify.com/platform/actors/running/actors-in-store#pay-per-event). The event you are charging for must be one of the configured events in your Actor. If the Actor is not set up as pay per event, or if the event is not configured, the endpoint will return an error. The endpoint must be called from the Actor run itself, with the same API token that the run was started with.  :::note  Pay per events Actors are still in alpha. Please, reach out to us with any questions or feedback.  ::: 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="chargeRunRequest">Define which event, and how many times, you want to charge for.</param>
        /// <param name="idempotencyKey">Always pass a unique idempotency key (any unique string) for each charge to avoid double charging in case of retries or network errors. (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<Object>> PostChargeRunWithHttpInfoAsync(string runId, ChargeRunRequest chargeRunRequest, string? idempotencyKey = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'runId' is set
            if (runId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'runId' when calling ActorRunsApi->PostChargeRun");

            // verify the required parameter 'chargeRunRequest' is set
            if (chargeRunRequest == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'chargeRunRequest' when calling ActorRunsApi->PostChargeRun");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("runId", Apify.SDK.Client.ClientUtils.ParameterToString(runId)); // path parameter
            if (idempotencyKey != null)
            {
                localVarRequestOptions.HeaderParameters.Add("idempotency-key", Apify.SDK.Client.ClientUtils.ParameterToString(idempotencyKey)); // header parameter
            }
            localVarRequestOptions.Data = chargeRunRequest;

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<Object>("/v2/actor-runs/{runId}/charge", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("PostChargeRun", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Resurrect run Resurrects a finished Actor run and returns an object that contains all the details about the resurrected run. Only finished runs, i.e. runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTED&#x60; and &#x60;TIMED-OUT&#x60; can be resurrected. Run status will be updated to RUNNING and its container will be restarted with the same storages (the same behaviour as when the run gets migrated to the new server).  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/running/runs-and-builds#resurrection-of-finished-run). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the run that is being resurrected (typically &#x60;latest&#x60;). (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the run that is being resurrected. (optional)</param>
        /// <param name="memory"> (optional)</param>
        /// <returns>RunResponse</returns>
        public RunResponse PostResurrectRun(string runId, string? build = default(string?), decimal? timeout = default(decimal?), decimal? memory = default(decimal?))
        {
            Apify.SDK.Client.ApiResponse<RunResponse> localVarResponse = PostResurrectRunWithHttpInfo(runId, build, timeout, memory);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Resurrect run Resurrects a finished Actor run and returns an object that contains all the details about the resurrected run. Only finished runs, i.e. runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTED&#x60; and &#x60;TIMED-OUT&#x60; can be resurrected. Run status will be updated to RUNNING and its container will be restarted with the same storages (the same behaviour as when the run gets migrated to the new server).  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/running/runs-and-builds#resurrection-of-finished-run). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the run that is being resurrected (typically &#x60;latest&#x60;). (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the run that is being resurrected. (optional)</param>
        /// <param name="memory"> (optional)</param>
        /// <returns>ApiResponse of RunResponse</returns>
        public Apify.SDK.Client.ApiResponse<RunResponse> PostResurrectRunWithHttpInfo(string runId, string? build = default(string?), decimal? timeout = default(decimal?), decimal? memory = default(decimal?))
        {
            // verify the required parameter 'runId' is set
            if (runId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'runId' when calling ActorRunsApi->PostResurrectRun");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("runId", Apify.SDK.Client.ClientUtils.ParameterToString(runId)); // path parameter
            if (build != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "build", build));
            }
            if (timeout != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "timeout", timeout));
            }
            if (memory != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "memory", memory));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<RunResponse>("/v2/actor-runs/{runId}/resurrect", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("PostResurrectRun", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Resurrect run Resurrects a finished Actor run and returns an object that contains all the details about the resurrected run. Only finished runs, i.e. runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTED&#x60; and &#x60;TIMED-OUT&#x60; can be resurrected. Run status will be updated to RUNNING and its container will be restarted with the same storages (the same behaviour as when the run gets migrated to the new server).  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/running/runs-and-builds#resurrection-of-finished-run). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the run that is being resurrected (typically &#x60;latest&#x60;). (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the run that is being resurrected. (optional)</param>
        /// <param name="memory"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of RunResponse</returns>
        public async System.Threading.Tasks.Task<RunResponse> PostResurrectRunAsync(string runId, string? build = default(string?), decimal? timeout = default(decimal?), decimal? memory = default(decimal?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<RunResponse> localVarResponse = await PostResurrectRunWithHttpInfoAsync(runId, build, timeout, memory, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Resurrect run Resurrects a finished Actor run and returns an object that contains all the details about the resurrected run. Only finished runs, i.e. runs with status &#x60;FINISHED&#x60;, &#x60;FAILED&#x60;, &#x60;ABORTED&#x60; and &#x60;TIMED-OUT&#x60; can be resurrected. Run status will be updated to RUNNING and its container will be restarted with the same storages (the same behaviour as when the run gets migrated to the new server).  For more information, see the [Actor docs](https://docs.apify.com/platform/actors/running/runs-and-builds#resurrection-of-finished-run). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="runId">Run ID.</param>
        /// <param name="build">Specifies the Actor build to run. It can be either a build tag or build number. By default, the run uses the build specified in the run that is being resurrected (typically &#x60;latest&#x60;). (optional)</param>
        /// <param name="timeout">Optional timeout for the run, in seconds. By default, the run uses a timeout specified in the run that is being resurrected. (optional)</param>
        /// <param name="memory"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (RunResponse)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<RunResponse>> PostResurrectRunWithHttpInfoAsync(string runId, string? build = default(string?), decimal? timeout = default(decimal?), decimal? memory = default(decimal?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'runId' is set
            if (runId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'runId' when calling ActorRunsApi->PostResurrectRun");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("runId", Apify.SDK.Client.ClientUtils.ParameterToString(runId)); // path parameter
            if (build != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "build", build));
            }
            if (timeout != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "timeout", timeout));
            }
            if (memory != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "memory", memory));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<RunResponse>("/v2/actor-runs/{runId}/resurrect", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("PostResurrectRun", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

    }
}

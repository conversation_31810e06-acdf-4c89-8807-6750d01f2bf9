﻿using Phlex.Core.Apify.IntegrationTests.Builders;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Models;

namespace Phlex.Core.Apify.IntegrationTests.Utils;

public class FakeCrawlerDataGrouping : ICrawlerData
{
    public Task<CrawlerConfiguration> GetConfigurationAsync(string configName)
    {
        var configuration = CrawlerConfigurationBuilder.Default().WithName(configName)
            .WithJsonConfiguration(
                "{\"startUrls\": [], \"maxCrawlDepth\": 0, \"proxyConfiguration\": {\"mode\": \"global\"}}").Build();

        return Task.FromResult(configuration);
    }

    public Task<IQueryable<UrlItem>> GetStartUrlsAsync()
    {
        var urls = new List<UrlItem>()
        {
            // Group 1 - domain1.com, config1, depth 1
            UrlItemBuilder.Default()
                .WithDomain("domain1.com")
                .WithConfigurationName("config1")
                .WithMaxCrawlDepth(1)
                .WithUrl("https://domain1.com/page1")
                .Build(),
                
            // Group 1 - same as above
            UrlItemBuilder.Default()
                .WithDomain("domain1.com")
                .WithConfigurationName("config1")
                .WithMaxCrawlDepth(1)
                .WithUrl("https://domain1.com/page1")
                .Build(),
                
            // Group 2 - domain1.com, config1, but different depth (2)
            UrlItemBuilder.Default()
                .WithDomain("domain1.com")
                .WithConfigurationName("config1")
                .WithMaxCrawlDepth(2)
                .WithUrl("https://domain1.com/page1")
                .Build(),
                
            // Group 3 - different domain
            UrlItemBuilder.Default()
                .WithDomain("domain2.com")
                .WithConfigurationName("config1")
                .WithMaxCrawlDepth(1)
                .WithUrl("https://domain2.com/page1")
                .Build(),
                
            // Group 4 - different config
            UrlItemBuilder.Default()
                .WithDomain("domain1.com")
                .WithConfigurationName("config2")
                .WithMaxCrawlDepth(1)
                .WithUrl("https://domain1.com/page1")
                .Build()
        };

        return Task.FromResult(urls.AsQueryable());
    }
}

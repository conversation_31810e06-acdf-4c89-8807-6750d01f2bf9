/*
 * Apify API
 *
 *  > **UPDATE 2025-01-14:** > We have rolled out this new Apify API Documentation. In case of any issues, please [report here](https://github.com/apify/apify-docs/issues). > The old API Documentation is still [available here](https://docs.apify.com/api/v2-old).  The Apify API (version 2) provides programmatic access to the [Apify platform](https://docs.apify.com). The API is organized around [RESTful](https://en.wikipedia.org/wiki/Representational_state_transfer) HTTP endpoints.  You can download the complete OpenAPI schema of Apify API in the [YAML](http://docs.apify.com/api/openapi.yaml) or [JSON](http://docs.apify.com/api/openapi.json) formats. The source code is also available on [GitHub](https://github.com/apify/apify-docs/tree/master/apify-api/openapi).  All requests and responses (including errors) are encoded in [JSON](http://www.json.org/) format with UTF-8 encoding, with a few exceptions that are explicitly described in the reference.  To access the API using [Node.js](https://nodejs.org/en/), we recommend the [`apify-client`](https://docs.apify.com/api/client/js) [NPM package](https://www.npmjs.com/package/apify-client).  To access the API using [Python](https://www.python.org/), we recommend the [`apify-client`](https://docs.apify.com/api/client/python) [PyPI package](https://pypi.org/project/apify-client/). The clients' functions correspond to the API endpoints and have the same parameters. This simplifies development of apps that depend on the Apify platform.  **Note:** All requests with JSON payloads need to specify the `Content-Type: application/json` HTTP header! All API endpoints support the `method` query parameter that can override the HTTP method. For example, if you want to call a POST endpoint using a GET request, simply add the query parameter `method=POST` to the URL and send the GET request. This feature is especially useful if you want to call Apify API endpoints from services that can only send GET requests.  ## Authentication <span id=\"/introduction/authentication\"></span>  You can find your API token on the [Integrations](https://console.apify.com/account#/integrations) page in the Apify Console.  To use your token in a request, either:  - Add the token to your request's `Authorization` header as `Bearer <token>`. E.g., `Authorization: Bearer xxxxxxx`. [More info](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Authorization). (Recommended). - Add it as the `token` parameter to your request URL. (Less secure).  Using your token in the request header is more secure than using it as a URL parameter because URLs are often stored in browser history and server logs. This creates a chance for someone unauthorized to access your API token.  **Do not share your API token or password with untrusted parties.**  For more information, see our [integrations](https://docs.apify.com/platform/integrations) documentation.  ## Basic usage <span id=\"/introduction/basic-usage\"></span>  To run an Actor, send a POST request to the [Run Actor](#/reference/actors/run-collection/run-actor) endpoint using either the Actor ID code (e.g. `vKg4IjxZbEYTYeW8T`) or its name (e.g. `janedoe~my-actor`):  `https://api.apify.com/v2/acts/[actor_id]/runs`  If the Actor is not runnable anonymously, you will receive a 401 or 403 [response code](https://developer.mozilla.org/en-US/docs/Web/HTTP/Status). This means you need to add your [secret API token](https://console.apify.com/account#/integrations) to the request's `Authorization` header ([recommended](#/introduction/authentication)) or as a URL query parameter `?token=[your_token]` (less secure).  Optionally, you can include the query parameters described in the [Run Actor](#/reference/actors/run-collection/run-actor) section to customize your run.  If you're using Node.js, the best way to run an Actor is using the `Apify.call()` method from the [Apify SDK](https://sdk.apify.com/docs/api/apify#apifycallactid-input-options). It runs the Actor using the account you are currently logged into (determined by the [secret API token](https://console.apify.com/account#/integrations)). The result is an [Actor run object](https://sdk.apify.com/docs/typedefs/actor-run) and its output (if any).  A typical workflow is as follows:  1. Run an Actor or task using the [Run Actor](#/reference/actors/run-collection/run-actor) or [Run task](#/reference/actor-tasks/run-collection/run-task) API endpoints. 2. Monitor the Actor run by periodically polling its progress using the [Get run](#/reference/actor-runs/run-object-and-its-storages/get-run) API endpoint. 3. Fetch the results from the [Get items](#/reference/datasets/item-collection/get-items) API endpoint using the `defaultDatasetId`, which you receive in the Run request response. Additional data may be stored in a key-value store. You can fetch them from the [Get record](#/reference/key-value-stores/record/get-record) API endpoint using the `defaultKeyValueStoreId` and the store's `key`.  **Note**: Instead of periodic polling, you can also run your [Actor](#/reference/actors/run-actor-synchronously) or [task](#/reference/actor-tasks/runs-collection/run-task-synchronously) synchronously. This will ensure that the request waits for 300 seconds (5 minutes) for the run to finish and returns its output. If the run takes longer, the request will time out and throw an error.  ## Response structure <span id=\"/introduction/response-structure\"></span>  Most API endpoints return a JSON object with the `data` property:  ``` {     \"data\": {         ...     } } ```  However, there are a few explicitly described exceptions, such as Dataset [Get items](#/reference/datasets/item-collection/get-items) or Key-value store [Get record](#/reference/key-value-stores/record/get-record) API endpoints, which return data in other formats. In case of an error, the response has the HTTP status code in the range of 4xx or 5xx and the `data` property is replaced with `error`. For example:  ``` {     \"error\": {         \"type\": \"record-not-found\",         \"message\": \"Store was not found.\"     } } ```  See [Errors](#/introduction/errors) for more details.  ## Pagination <span id=\"/introduction/pagination\"></span>  All API endpoints that return a list of records (e.g. [Get list of Actors](#/reference/actors/actor-collection/get-list-of-actors)) enforce pagination in order to limit the size of their responses.  Most of these API endpoints are paginated using the `offset` and `limit` query parameters. The only exception is [Get list of keys](#/reference/key-value-stores/key-collection/get-list-of-keys), which is paginated using the `exclusiveStartKey` query parameter.  **IMPORTANT**: Each API endpoint that supports pagination enforces a certain maximum value for the `limit` parameter, in order to reduce the load on Apify servers. The maximum limit could change in future so you should never rely on a specific value and check the responses of these API endpoints.  ### Using offset <span id=\"/introduction/pagination/using-offset\"></span>  Most API endpoints that return a list of records enable pagination using the following query parameters:  <table>   <tr>     <td><code>limit</code></td>     <td>Limits the response to contain a specific maximum number of items, e.g. <code>limit=20</code>.</td>   </tr>   <tr>     <td><code>offset</code></td>     <td>Skips a number of items from the beginning of the list, e.g. <code>offset=100</code>.</td>   </tr>   <tr>     <td><code>desc</code></td>     <td>     By default, items are sorted in the order in which they were created or added to the list.     This feature is useful when fetching all the items, because it ensures that items     created after the client started the pagination will not be skipped.     If you specify the <code>desc=1</code> parameter, the items will be returned in the reverse order,     i.e. from the newest to the oldest items.     </td>   </tr> </table>  The response of these API endpoints is always a JSON object with the following structure:  ``` {     \"data\": {         \"total\": 2560,         \"offset\": 250,         \"limit\": 1000,         \"count\": 1000,         \"desc\": false,         \"items\": [             { 1st object },             { 2nd object },             ...             { 1000th object }         ]     } } ```  The following table describes the meaning of the response properties:  <table>   <tr>     <th>Property</th>     <th>Description</th>   </tr>   <tr>     <td><code>total</code></td>     <td>The total number of items available in the list.</td>   </tr>   <tr>     <td><code>offset</code></td>     <td>The number of items that were skipped at the start.     This is equal to the <code>offset</code> query parameter if it was provided, otherwise it is <code>0</code>.</td>   </tr>   <tr>     <td><code>limit</code></td>     <td>The maximum number of items that can be returned in the HTTP response.     It equals to the <code>limit</code> query parameter if it was provided or     the maximum limit enforced for the particular API endpoint, whichever is smaller.</td>   </tr>   <tr>     <td><code>count</code></td>     <td>The actual number of items returned in the HTTP response.</td>   </tr>   <tr>     <td><code>desc</code></td>     <td><code>true</code> if data were requested in descending order and <code>false</code> otherwise.</td>   </tr>   <tr>     <td><code>items</code></td>     <td>An array of requested items.</td>   </tr> </table>  ### Using key <span id=\"/introduction/pagination/using-key\"></span>  The records in the [key-value store](https://docs.apify.com/platform/storage/key-value-store) are not ordered based on numerical indexes, but rather by their keys in the UTF-8 binary order. Therefore the [Get list of keys](#/reference/key-value-stores/key-collection/get-list-of-keys) API endpoint only supports pagination using the following query parameters:  <table>   <tr>     <td><code>limit</code></td>     <td>Limits the response to contain a specific maximum number items, e.g. <code>limit=20</code>.</td>   </tr>   <tr>     <td><code>exclusiveStartKey</code></td>     <td>Skips all records with keys up to the given key including the given key,     in the UTF-8 binary order.</td>   </tr> </table>  The response of the API endpoint is always a JSON object with following structure:  ``` {     \"data\": {         \"limit\": 1000,         \"isTruncated\": true,         \"exclusiveStartKey\": \"my-key\",         \"nextExclusiveStartKey\": \"some-other-key\",         \"items\": [             { 1st object },             { 2nd object },             ...             { 1000th object }         ]     } } ```  The following table describes the meaning of the response properties:  <table>   <tr>     <th>Property</th>     <th>Description</th>   </tr>   <tr>     <td><code>limit</code></td>     <td>The maximum number of items that can be returned in the HTTP response.     It equals to the <code>limit</code> query parameter if it was provided or     the maximum limit enforced for the particular endpoint, whichever is smaller.</td>   </tr>   <tr>     <td><code>isTruncated</code></td>     <td><code>true</code> if there are more items left to be queried. Otherwise <code>false</code>.</td>   </tr>   <tr>     <td><code>exclusiveStartKey</code></td>     <td>The last key that was skipped at the start. Is `null` for the first page.</td>   </tr>   <tr>     <td><code>nextExclusiveStartKey</code></td>     <td>The value for the <code>exclusiveStartKey</code> parameter to query the next page of items.</td>   </tr> </table>  ## Errors <span id=\"/introduction/errors\"></span>  The Apify API uses common HTTP status codes: `2xx` range for success, `4xx` range for errors caused by the caller (invalid requests) and `5xx` range for server errors (these are rare). Each error response contains a JSON object defining the `error` property, which is an object with the `type` and `message` properties that contain the error code and a human-readable error description, respectively.  For example:  ``` {     \"error\": {         \"type\": \"record-not-found\",         \"message\": \"Store was not found.\"     } } ```  Here is the table of the most common errors that can occur for many API endpoints:  <table>   <tr>     <th>status</th>     <th>type</th>     <th>message</th>   </tr>   <tr>     <td><code>400</code></td>     <td><code>invalid-request</code></td>     <td>POST data must be a JSON object</td>   </tr>   <tr>     <td><code>400</code></td>     <td><code>invalid-value</code></td>     <td>Invalid value provided: Comments required</td>   </tr>   <tr>     <td><code>400</code></td>     <td><code>invalid-record-key</code></td>     <td>Record key contains invalid character</td>   </tr>   <tr>     <td><code>401</code></td>     <td><code>token-not-provided</code></td>     <td>Authentication token was not provided</td>   </tr>   <tr>     <td><code>404</code></td>     <td><code>record-not-found</code></td>     <td>Store was not found</td>   </tr>   <tr>     <td><code>429</code></td>     <td><code>rate-limit-exceeded</code></td>     <td>You have exceeded the rate limit of 30 requests per second</td>   </tr>   <tr>     <td><code>405</code></td>     <td><code>method-not-allowed</code></td>     <td>This API endpoint can only be accessed using the following HTTP methods: OPTIONS, POST</td>   </tr> </table>  ## Rate limiting <span id=\"/introduction/rate-limiting\"></span>  All API endpoints limit the rate of requests in order to prevent overloading of Apify servers by misbehaving clients.  There are two kinds of rate limits - a global rate limit and a per-resource rate limit.  ### Global rate limit <span id=\"/introduction/rate-limiting/global-rate-limit\"></span>  The global rate limit is set to _250 000 requests per minute_. For [authenticated](#/introduction/authentication) requests, it is counted per user, and for unauthenticated requests, it is counted per IP address.  ### Per-resource rate limit <span id=\"/introduction/rate-limiting/per-resource-rate-limit\"></span>  The default per-resource rate limit is _30 requests per second per resource_, which in this context means a single Actor, a single Actor run, a single dataset, single key-value store etc. The default rate limit is applied to every API endpoint except a few select ones, which have higher rate limits. Each API endpoint returns its rate limit in `X-RateLimit-Limit` header.  These endpoints have a rate limit of _100 requests per second per resource_:  * CRUD ([get](#/reference/key-value-stores/record/get-record),   [put](#/reference/key-value-stores/record/put-record),   [delete](#/reference/key-value-stores/record/delete-record))   operations on key-value store records  These endpoints have a rate limit of _200 requests per second per resource_: * [Run Actor](#/reference/actors/run-collection/run-actor) * [Run Actor task asynchronously](#/reference/actor-tasks/runs-collection/run-task-asynchronously) * [Run Actor task synchronously](#/reference/actor-tasks/runs-collection/run-task-synchronously) * [Metamorph Actor run](#/reference/actors/metamorph-run/metamorph-run) * [Push items](#/reference/datasets/item-collection/put-items) to dataset * CRUD   ([add](#/reference/request-queues/request-collection/add-request),   [get](#/reference/request-queues/request-collection/get-request),   [update](#/reference/request-queues/request-collection/update-request),   [delete](#/reference/request-queues/request-collection/delete-request))   operations on requests in request queues  ### Rate limit exceeded errors <span id=\"/introduction/rate-limiting/rate-limit-exceeded-errors\"></span>  If the client is sending too many requests, the API endpoints respond with the HTTP status code `429 Too Many Requests` and the following body:  ``` {     \"error\": {         \"type\": \"rate-limit-exceeded\",         \"message\": \"You have exceeded the rate limit of ... requests per second\"     } } ```  ### Retrying rate-limited requests with exponential backoff <span id=\"/introduction/rate-limiting/retrying-rate-limited-requests-with-exponential-backoff\"></span>  If the client receives the rate limit error, it should wait a certain period of time and then retry the request. If the error happens again, the client should double the wait period and retry the request, and so on. This algorithm is known as _exponential backoff_ and it can be described using the following pseudo-code:  1. Define a variable `DELAY=500` 2. Send the HTTP request to the API endpoint 3. If the response has status code not equal to `429` then you are done. Otherwise:    * Wait for a period of time chosen randomly from the interval `DELAY` to `2*DELAY` milliseconds    * Double the future wait period by setting `DELAY = 2*DELAY`    * Continue with step 2  If all requests sent by the client implement the above steps, the client will automatically use the maximum available bandwidth for its requests.  Note that the Apify API clients [for JavaScript](https://docs.apify.com/api/client/js) and [for Python](https://docs.apify.com/api/client/python) use the exponential backoff algorithm transparently, so that you do not need to worry about it.  ## Referring to resources <span id=\"/introduction/referring-to-resources\"></span>  There are three main ways to refer to a resource you're accessing via API.  - the resource ID (e.g. `iKkPcIgVvwmztduf8`) - `username~resourcename` - when using this access method, you will need to use your API token, and access will only work if you have the correct permissions. - `~resourcename` - for this, you need to use an API token, and the `resourcename` refers to a resource in the API token owner's account. 
 *
 * Generated by: https://github.com/openapitools/openapi-generator.git
 */


using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Mime;
using Apify.SDK.Client;
using Apify.SDK.Model;

namespace Apify.SDK.Api
{

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IWebhooksWebhooksApiSync : IApiAccessor
    {
        #region Synchronous Operations
        /// <summary>
        /// Delete webhook
        /// </summary>
        /// <remarks>
        /// Deletes a webhook.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <returns>Object</returns>
        Object WebhookDelete(string webhookId);

        /// <summary>
        /// Delete webhook
        /// </summary>
        /// <remarks>
        /// Deletes a webhook.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <returns>ApiResponse of Object</returns>
        ApiResponse<Object> WebhookDeleteWithHttpInfo(string webhookId);
        /// <summary>
        /// Get collection
        /// </summary>
        /// <remarks>
        /// Gets a given webhook&#39;s list of dispatches.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">ID number of the webhook.</param>
        /// <returns>WebhookDispatchList</returns>
        WebhookDispatchList WebhookDispatchesGet(string webhookId);

        /// <summary>
        /// Get collection
        /// </summary>
        /// <remarks>
        /// Gets a given webhook&#39;s list of dispatches.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">ID number of the webhook.</param>
        /// <returns>ApiResponse of WebhookDispatchList</returns>
        ApiResponse<WebhookDispatchList> WebhookDispatchesGetWithHttpInfo(string webhookId);
        /// <summary>
        /// Get webhook
        /// </summary>
        /// <remarks>
        /// Gets webhook object with all details.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <returns>GetWebhookResponse</returns>
        GetWebhookResponse WebhookGet(string webhookId);

        /// <summary>
        /// Get webhook
        /// </summary>
        /// <remarks>
        /// Gets webhook object with all details.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <returns>ApiResponse of GetWebhookResponse</returns>
        ApiResponse<GetWebhookResponse> WebhookGetWithHttpInfo(string webhookId);
        /// <summary>
        /// Update webhook
        /// </summary>
        /// <remarks>
        /// Updates a webhook using values specified by a webhook object passed as JSON in the POST payload. If the object does not define a specific property, its value will not be updated.  The response is the full webhook object as returned by the [Get webhook](#/reference/webhooks/webhook-object/get-webhook) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <param name="webhookUpdate"></param>
        /// <returns>UpdateWebhookResponse</returns>
        UpdateWebhookResponse WebhookPut(string webhookId, WebhookUpdate webhookUpdate);

        /// <summary>
        /// Update webhook
        /// </summary>
        /// <remarks>
        /// Updates a webhook using values specified by a webhook object passed as JSON in the POST payload. If the object does not define a specific property, its value will not be updated.  The response is the full webhook object as returned by the [Get webhook](#/reference/webhooks/webhook-object/get-webhook) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <param name="webhookUpdate"></param>
        /// <returns>ApiResponse of UpdateWebhookResponse</returns>
        ApiResponse<UpdateWebhookResponse> WebhookPutWithHttpInfo(string webhookId, WebhookUpdate webhookUpdate);
        /// <summary>
        /// Test webhook
        /// </summary>
        /// <remarks>
        /// Tests a webhook. Creates a webhook dispatch with a dummy payload.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <returns>TestWebhookResponse</returns>
        TestWebhookResponse WebhookTestPost(string webhookId);

        /// <summary>
        /// Test webhook
        /// </summary>
        /// <remarks>
        /// Tests a webhook. Creates a webhook dispatch with a dummy payload.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <returns>ApiResponse of TestWebhookResponse</returns>
        ApiResponse<TestWebhookResponse> WebhookTestPostWithHttpInfo(string webhookId);
        /// <summary>
        /// Get list of webhooks
        /// </summary>
        /// <remarks>
        /// Gets the list of webhooks that the user created.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 records. By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of records that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of records to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <returns>GetListOfWebhooksResponse</returns>
        GetListOfWebhooksResponse WebhooksGet(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?));

        /// <summary>
        /// Get list of webhooks
        /// </summary>
        /// <remarks>
        /// Gets the list of webhooks that the user created.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 records. By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of records that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of records to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <returns>ApiResponse of GetListOfWebhooksResponse</returns>
        ApiResponse<GetListOfWebhooksResponse> WebhooksGetWithHttpInfo(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?));
        /// <summary>
        /// Create webhook
        /// </summary>
        /// <remarks>
        /// Creates a new webhook with settings provided by the webhook object passed as JSON in the payload. The response is the created webhook object.  To avoid duplicating a webhook, use the &#x60;idempotencyKey&#x60; parameter in the request body. Multiple calls to create a webhook with the same &#x60;idempotencyKey&#x60; will only create the webhook with the first call and return the existing webhook on subsequent calls. Idempotency keys must be unique, so use a UUID or another random string with enough entropy.  To assign the new webhook to an Actor or task, the request body must contain &#x60;requestUrl&#x60;, &#x60;eventTypes&#x60;, and &#x60;condition&#x60; properties.  * &#x60;requestUrl&#x60; is the webhook&#39;s target URL, to which data is sent as a POST request with a JSON payload. * &#x60;eventTypes&#x60; is a list of events that will trigger the webhook, e.g. when the Actor run succeeds. * &#x60;condition&#x60; should be an object containing the ID of the Actor or task to which the webhook will be assigned. * &#x60;payloadTemplate&#x60; is a JSON-like string, whose syntax is extended with the use of variables. * &#x60;headersTemplate&#x60; is a JSON-like string, whose syntax is extended with the use of variables. Following values will be re-written to defaults: \&quot;host\&quot;, \&quot;Content-Type\&quot;, \&quot;X-Apify-Webhook\&quot;, \&quot;X-Apify-Webhook-Dispatch-Id\&quot;, \&quot;X-Apify-Request-Origin\&quot; * &#x60;description&#x60; is an optional string. * &#x60;shouldInterpolateStrings&#x60; is a boolean indicating whether to interpolate variables contained inside strings in the &#x60;payloadTemplate&#x60;  &#x60;&#x60;&#x60;     \&quot;isAdHoc\&quot; : false,     \&quot;requestUrl\&quot; : \&quot;https://example.com\&quot;,     \&quot;eventTypes\&quot; : [         \&quot;ACTOR.RUN.SUCCEEDED\&quot;,         \&quot;ACTOR.RUN.ABORTED\&quot;     ],     \&quot;condition\&quot; : {         \&quot;actorId\&quot;: \&quot;janedoe~my-actor\&quot;,         \&quot;actorTaskId\&quot; : \&quot;W9bs9JE9v7wprjAnJ\&quot;     },     \&quot;payloadTemplate\&quot;: \&quot;\&quot;,     \&quot;headersTemplate\&quot;: \&quot;\&quot;,     \&quot;description\&quot;: \&quot;my awesome webhook\&quot;,     \&quot;shouldInterpolateStrings\&quot;: false, &#x60;&#x60;&#x60;  **Important**: The request must specify the &#x60;Content-Type: application/json&#x60; HTTP header. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookCreate"></param>
        /// <param name="limit"> (optional)</param>
        /// <param name="offset"> (optional)</param>
        /// <param name="desc"> (optional)</param>
        /// <returns>CreateWebhookResponse</returns>
        CreateWebhookResponse WebhooksPost(WebhookCreate webhookCreate, string? limit = default(string?), string? offset = default(string?), string? desc = default(string?));

        /// <summary>
        /// Create webhook
        /// </summary>
        /// <remarks>
        /// Creates a new webhook with settings provided by the webhook object passed as JSON in the payload. The response is the created webhook object.  To avoid duplicating a webhook, use the &#x60;idempotencyKey&#x60; parameter in the request body. Multiple calls to create a webhook with the same &#x60;idempotencyKey&#x60; will only create the webhook with the first call and return the existing webhook on subsequent calls. Idempotency keys must be unique, so use a UUID or another random string with enough entropy.  To assign the new webhook to an Actor or task, the request body must contain &#x60;requestUrl&#x60;, &#x60;eventTypes&#x60;, and &#x60;condition&#x60; properties.  * &#x60;requestUrl&#x60; is the webhook&#39;s target URL, to which data is sent as a POST request with a JSON payload. * &#x60;eventTypes&#x60; is a list of events that will trigger the webhook, e.g. when the Actor run succeeds. * &#x60;condition&#x60; should be an object containing the ID of the Actor or task to which the webhook will be assigned. * &#x60;payloadTemplate&#x60; is a JSON-like string, whose syntax is extended with the use of variables. * &#x60;headersTemplate&#x60; is a JSON-like string, whose syntax is extended with the use of variables. Following values will be re-written to defaults: \&quot;host\&quot;, \&quot;Content-Type\&quot;, \&quot;X-Apify-Webhook\&quot;, \&quot;X-Apify-Webhook-Dispatch-Id\&quot;, \&quot;X-Apify-Request-Origin\&quot; * &#x60;description&#x60; is an optional string. * &#x60;shouldInterpolateStrings&#x60; is a boolean indicating whether to interpolate variables contained inside strings in the &#x60;payloadTemplate&#x60;  &#x60;&#x60;&#x60;     \&quot;isAdHoc\&quot; : false,     \&quot;requestUrl\&quot; : \&quot;https://example.com\&quot;,     \&quot;eventTypes\&quot; : [         \&quot;ACTOR.RUN.SUCCEEDED\&quot;,         \&quot;ACTOR.RUN.ABORTED\&quot;     ],     \&quot;condition\&quot; : {         \&quot;actorId\&quot;: \&quot;janedoe~my-actor\&quot;,         \&quot;actorTaskId\&quot; : \&quot;W9bs9JE9v7wprjAnJ\&quot;     },     \&quot;payloadTemplate\&quot;: \&quot;\&quot;,     \&quot;headersTemplate\&quot;: \&quot;\&quot;,     \&quot;description\&quot;: \&quot;my awesome webhook\&quot;,     \&quot;shouldInterpolateStrings\&quot;: false, &#x60;&#x60;&#x60;  **Important**: The request must specify the &#x60;Content-Type: application/json&#x60; HTTP header. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookCreate"></param>
        /// <param name="limit"> (optional)</param>
        /// <param name="offset"> (optional)</param>
        /// <param name="desc"> (optional)</param>
        /// <returns>ApiResponse of CreateWebhookResponse</returns>
        ApiResponse<CreateWebhookResponse> WebhooksPostWithHttpInfo(WebhookCreate webhookCreate, string? limit = default(string?), string? offset = default(string?), string? desc = default(string?));
        #endregion Synchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IWebhooksWebhooksApiAsync : IApiAccessor
    {
        #region Asynchronous Operations
        /// <summary>
        /// Delete webhook
        /// </summary>
        /// <remarks>
        /// Deletes a webhook.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        System.Threading.Tasks.Task<Object> WebhookDeleteAsync(string webhookId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Delete webhook
        /// </summary>
        /// <remarks>
        /// Deletes a webhook.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        System.Threading.Tasks.Task<ApiResponse<Object>> WebhookDeleteWithHttpInfoAsync(string webhookId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Get collection
        /// </summary>
        /// <remarks>
        /// Gets a given webhook&#39;s list of dispatches.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">ID number of the webhook.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of WebhookDispatchList</returns>
        System.Threading.Tasks.Task<WebhookDispatchList> WebhookDispatchesGetAsync(string webhookId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Get collection
        /// </summary>
        /// <remarks>
        /// Gets a given webhook&#39;s list of dispatches.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">ID number of the webhook.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (WebhookDispatchList)</returns>
        System.Threading.Tasks.Task<ApiResponse<WebhookDispatchList>> WebhookDispatchesGetWithHttpInfoAsync(string webhookId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Get webhook
        /// </summary>
        /// <remarks>
        /// Gets webhook object with all details.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of GetWebhookResponse</returns>
        System.Threading.Tasks.Task<GetWebhookResponse> WebhookGetAsync(string webhookId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Get webhook
        /// </summary>
        /// <remarks>
        /// Gets webhook object with all details.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (GetWebhookResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<GetWebhookResponse>> WebhookGetWithHttpInfoAsync(string webhookId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Update webhook
        /// </summary>
        /// <remarks>
        /// Updates a webhook using values specified by a webhook object passed as JSON in the POST payload. If the object does not define a specific property, its value will not be updated.  The response is the full webhook object as returned by the [Get webhook](#/reference/webhooks/webhook-object/get-webhook) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <param name="webhookUpdate"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of UpdateWebhookResponse</returns>
        System.Threading.Tasks.Task<UpdateWebhookResponse> WebhookPutAsync(string webhookId, WebhookUpdate webhookUpdate, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Update webhook
        /// </summary>
        /// <remarks>
        /// Updates a webhook using values specified by a webhook object passed as JSON in the POST payload. If the object does not define a specific property, its value will not be updated.  The response is the full webhook object as returned by the [Get webhook](#/reference/webhooks/webhook-object/get-webhook) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <param name="webhookUpdate"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (UpdateWebhookResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<UpdateWebhookResponse>> WebhookPutWithHttpInfoAsync(string webhookId, WebhookUpdate webhookUpdate, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Test webhook
        /// </summary>
        /// <remarks>
        /// Tests a webhook. Creates a webhook dispatch with a dummy payload.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of TestWebhookResponse</returns>
        System.Threading.Tasks.Task<TestWebhookResponse> WebhookTestPostAsync(string webhookId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Test webhook
        /// </summary>
        /// <remarks>
        /// Tests a webhook. Creates a webhook dispatch with a dummy payload.
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (TestWebhookResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<TestWebhookResponse>> WebhookTestPostWithHttpInfoAsync(string webhookId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Get list of webhooks
        /// </summary>
        /// <remarks>
        /// Gets the list of webhooks that the user created.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 records. By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of records that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of records to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of GetListOfWebhooksResponse</returns>
        System.Threading.Tasks.Task<GetListOfWebhooksResponse> WebhooksGetAsync(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Get list of webhooks
        /// </summary>
        /// <remarks>
        /// Gets the list of webhooks that the user created.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 records. By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of records that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of records to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (GetListOfWebhooksResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<GetListOfWebhooksResponse>> WebhooksGetWithHttpInfoAsync(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        /// <summary>
        /// Create webhook
        /// </summary>
        /// <remarks>
        /// Creates a new webhook with settings provided by the webhook object passed as JSON in the payload. The response is the created webhook object.  To avoid duplicating a webhook, use the &#x60;idempotencyKey&#x60; parameter in the request body. Multiple calls to create a webhook with the same &#x60;idempotencyKey&#x60; will only create the webhook with the first call and return the existing webhook on subsequent calls. Idempotency keys must be unique, so use a UUID or another random string with enough entropy.  To assign the new webhook to an Actor or task, the request body must contain &#x60;requestUrl&#x60;, &#x60;eventTypes&#x60;, and &#x60;condition&#x60; properties.  * &#x60;requestUrl&#x60; is the webhook&#39;s target URL, to which data is sent as a POST request with a JSON payload. * &#x60;eventTypes&#x60; is a list of events that will trigger the webhook, e.g. when the Actor run succeeds. * &#x60;condition&#x60; should be an object containing the ID of the Actor or task to which the webhook will be assigned. * &#x60;payloadTemplate&#x60; is a JSON-like string, whose syntax is extended with the use of variables. * &#x60;headersTemplate&#x60; is a JSON-like string, whose syntax is extended with the use of variables. Following values will be re-written to defaults: \&quot;host\&quot;, \&quot;Content-Type\&quot;, \&quot;X-Apify-Webhook\&quot;, \&quot;X-Apify-Webhook-Dispatch-Id\&quot;, \&quot;X-Apify-Request-Origin\&quot; * &#x60;description&#x60; is an optional string. * &#x60;shouldInterpolateStrings&#x60; is a boolean indicating whether to interpolate variables contained inside strings in the &#x60;payloadTemplate&#x60;  &#x60;&#x60;&#x60;     \&quot;isAdHoc\&quot; : false,     \&quot;requestUrl\&quot; : \&quot;https://example.com\&quot;,     \&quot;eventTypes\&quot; : [         \&quot;ACTOR.RUN.SUCCEEDED\&quot;,         \&quot;ACTOR.RUN.ABORTED\&quot;     ],     \&quot;condition\&quot; : {         \&quot;actorId\&quot;: \&quot;janedoe~my-actor\&quot;,         \&quot;actorTaskId\&quot; : \&quot;W9bs9JE9v7wprjAnJ\&quot;     },     \&quot;payloadTemplate\&quot;: \&quot;\&quot;,     \&quot;headersTemplate\&quot;: \&quot;\&quot;,     \&quot;description\&quot;: \&quot;my awesome webhook\&quot;,     \&quot;shouldInterpolateStrings\&quot;: false, &#x60;&#x60;&#x60;  **Important**: The request must specify the &#x60;Content-Type: application/json&#x60; HTTP header. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookCreate"></param>
        /// <param name="limit"> (optional)</param>
        /// <param name="offset"> (optional)</param>
        /// <param name="desc"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CreateWebhookResponse</returns>
        System.Threading.Tasks.Task<CreateWebhookResponse> WebhooksPostAsync(WebhookCreate webhookCreate, string? limit = default(string?), string? offset = default(string?), string? desc = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));

        /// <summary>
        /// Create webhook
        /// </summary>
        /// <remarks>
        /// Creates a new webhook with settings provided by the webhook object passed as JSON in the payload. The response is the created webhook object.  To avoid duplicating a webhook, use the &#x60;idempotencyKey&#x60; parameter in the request body. Multiple calls to create a webhook with the same &#x60;idempotencyKey&#x60; will only create the webhook with the first call and return the existing webhook on subsequent calls. Idempotency keys must be unique, so use a UUID or another random string with enough entropy.  To assign the new webhook to an Actor or task, the request body must contain &#x60;requestUrl&#x60;, &#x60;eventTypes&#x60;, and &#x60;condition&#x60; properties.  * &#x60;requestUrl&#x60; is the webhook&#39;s target URL, to which data is sent as a POST request with a JSON payload. * &#x60;eventTypes&#x60; is a list of events that will trigger the webhook, e.g. when the Actor run succeeds. * &#x60;condition&#x60; should be an object containing the ID of the Actor or task to which the webhook will be assigned. * &#x60;payloadTemplate&#x60; is a JSON-like string, whose syntax is extended with the use of variables. * &#x60;headersTemplate&#x60; is a JSON-like string, whose syntax is extended with the use of variables. Following values will be re-written to defaults: \&quot;host\&quot;, \&quot;Content-Type\&quot;, \&quot;X-Apify-Webhook\&quot;, \&quot;X-Apify-Webhook-Dispatch-Id\&quot;, \&quot;X-Apify-Request-Origin\&quot; * &#x60;description&#x60; is an optional string. * &#x60;shouldInterpolateStrings&#x60; is a boolean indicating whether to interpolate variables contained inside strings in the &#x60;payloadTemplate&#x60;  &#x60;&#x60;&#x60;     \&quot;isAdHoc\&quot; : false,     \&quot;requestUrl\&quot; : \&quot;https://example.com\&quot;,     \&quot;eventTypes\&quot; : [         \&quot;ACTOR.RUN.SUCCEEDED\&quot;,         \&quot;ACTOR.RUN.ABORTED\&quot;     ],     \&quot;condition\&quot; : {         \&quot;actorId\&quot;: \&quot;janedoe~my-actor\&quot;,         \&quot;actorTaskId\&quot; : \&quot;W9bs9JE9v7wprjAnJ\&quot;     },     \&quot;payloadTemplate\&quot;: \&quot;\&quot;,     \&quot;headersTemplate\&quot;: \&quot;\&quot;,     \&quot;description\&quot;: \&quot;my awesome webhook\&quot;,     \&quot;shouldInterpolateStrings\&quot;: false, &#x60;&#x60;&#x60;  **Important**: The request must specify the &#x60;Content-Type: application/json&#x60; HTTP header. 
        /// </remarks>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookCreate"></param>
        /// <param name="limit"> (optional)</param>
        /// <param name="offset"> (optional)</param>
        /// <param name="desc"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CreateWebhookResponse)</returns>
        System.Threading.Tasks.Task<ApiResponse<CreateWebhookResponse>> WebhooksPostWithHttpInfoAsync(WebhookCreate webhookCreate, string? limit = default(string?), string? offset = default(string?), string? desc = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken));
        #endregion Asynchronous Operations
    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public interface IWebhooksWebhooksApi : IWebhooksWebhooksApiSync, IWebhooksWebhooksApiAsync
    {

    }

    /// <summary>
    /// Represents a collection of functions to interact with the API endpoints
    /// </summary>
    public partial class WebhooksWebhooksApi : IDisposable, IWebhooksWebhooksApi
    {
        private Apify.SDK.Client.ExceptionFactory _exceptionFactory = (name, response) => null;

        /// <summary>
        /// Initializes a new instance of the <see cref="WebhooksWebhooksApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <returns></returns>
        public WebhooksWebhooksApi() : this((string)null)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="WebhooksWebhooksApi"/> class.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        public WebhooksWebhooksApi(string basePath)
        {
            this.Configuration = Apify.SDK.Client.Configuration.MergeConfigurations(
                Apify.SDK.Client.GlobalConfiguration.Instance,
                new Apify.SDK.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Apify.SDK.Client.ApiClient(this.Configuration.BasePath);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Apify.SDK.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="WebhooksWebhooksApi"/> class using Configuration object.
        /// **IMPORTANT** This will also create an instance of HttpClient, which is less than ideal.
        /// It's better to reuse the <see href="https://docs.microsoft.com/en-us/dotnet/architecture/microservices/implement-resilient-applications/use-httpclientfactory-to-implement-resilient-http-requests#issues-with-the-original-httpclient-class-available-in-net">HttpClient and HttpClientHandler</see>.
        /// </summary>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        public WebhooksWebhooksApi(Apify.SDK.Client.Configuration configuration)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Configuration = Apify.SDK.Client.Configuration.MergeConfigurations(
                Apify.SDK.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Apify.SDK.Client.ApiClient(this.Configuration.BasePath);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Apify.SDK.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="WebhooksWebhooksApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public WebhooksWebhooksApi(HttpClient client, HttpClientHandler handler = null) : this(client, (string)null, handler)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="WebhooksWebhooksApi"/> class.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="basePath">The target service's base path in URL format.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <exception cref="ArgumentException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public WebhooksWebhooksApi(HttpClient client, string basePath, HttpClientHandler handler = null)
        {
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Apify.SDK.Client.Configuration.MergeConfigurations(
                Apify.SDK.Client.GlobalConfiguration.Instance,
                new Apify.SDK.Client.Configuration { BasePath = basePath }
            );
            this.ApiClient = new Apify.SDK.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client =  this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            this.ExceptionFactory = Apify.SDK.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="WebhooksWebhooksApi"/> class using Configuration object.
        /// </summary>
        /// <param name="client">An instance of HttpClient.</param>
        /// <param name="configuration">An instance of Configuration.</param>
        /// <param name="handler">An optional instance of HttpClientHandler that is used by HttpClient.</param>
        /// <exception cref="ArgumentNullException"></exception>
        /// <returns></returns>
        /// <remarks>
        /// Some configuration settings will not be applied without passing an HttpClientHandler.
        /// The features affected are: Setting and Retrieving Cookies, Client Certificates, Proxy settings.
        /// </remarks>
        public WebhooksWebhooksApi(HttpClient client, Apify.SDK.Client.Configuration configuration, HttpClientHandler handler = null)
        {
            if (configuration == null) throw new ArgumentNullException("configuration");
            if (client == null) throw new ArgumentNullException("client");

            this.Configuration = Apify.SDK.Client.Configuration.MergeConfigurations(
                Apify.SDK.Client.GlobalConfiguration.Instance,
                configuration
            );
            this.ApiClient = new Apify.SDK.Client.ApiClient(client, this.Configuration.BasePath, handler);
            this.Client = this.ApiClient;
            this.AsynchronousClient = this.ApiClient;
            ExceptionFactory = Apify.SDK.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="WebhooksWebhooksApi"/> class
        /// using a Configuration object and client instance.
        /// </summary>
        /// <param name="client">The client interface for synchronous API access.</param>
        /// <param name="asyncClient">The client interface for asynchronous API access.</param>
        /// <param name="configuration">The configuration object.</param>
        /// <exception cref="ArgumentNullException"></exception>
        public WebhooksWebhooksApi(Apify.SDK.Client.ISynchronousClient client, Apify.SDK.Client.IAsynchronousClient asyncClient, Apify.SDK.Client.IReadableConfiguration configuration)
        {
            if (client == null) throw new ArgumentNullException("client");
            if (asyncClient == null) throw new ArgumentNullException("asyncClient");
            if (configuration == null) throw new ArgumentNullException("configuration");

            this.Client = client;
            this.AsynchronousClient = asyncClient;
            this.Configuration = configuration;
            this.ExceptionFactory = Apify.SDK.Client.Configuration.DefaultExceptionFactory;
        }

        /// <summary>
        /// Disposes resources if they were created by us
        /// </summary>
        public void Dispose()
        {
            this.ApiClient?.Dispose();
        }

        /// <summary>
        /// Holds the ApiClient if created
        /// </summary>
        public Apify.SDK.Client.ApiClient ApiClient { get; set; } = null;

        /// <summary>
        /// The client for accessing this underlying API asynchronously.
        /// </summary>
        public Apify.SDK.Client.IAsynchronousClient AsynchronousClient { get; set; }

        /// <summary>
        /// The client for accessing this underlying API synchronously.
        /// </summary>
        public Apify.SDK.Client.ISynchronousClient Client { get; set; }

        /// <summary>
        /// Gets the base path of the API client.
        /// </summary>
        /// <value>The base path</value>
        public string GetBasePath()
        {
            return this.Configuration.BasePath;
        }

        /// <summary>
        /// Gets or sets the configuration object
        /// </summary>
        /// <value>An instance of the Configuration</value>
        public Apify.SDK.Client.IReadableConfiguration Configuration { get; set; }

        /// <summary>
        /// Provides a factory method hook for the creation of exceptions.
        /// </summary>
        public Apify.SDK.Client.ExceptionFactory ExceptionFactory
        {
            get
            {
                if (_exceptionFactory != null && _exceptionFactory.GetInvocationList().Length > 1)
                {
                    throw new InvalidOperationException("Multicast delegate for ExceptionFactory is unsupported.");
                }
                return _exceptionFactory;
            }
            set { _exceptionFactory = value; }
        }

        /// <summary>
        /// Delete webhook Deletes a webhook.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <returns>Object</returns>
        public Object WebhookDelete(string webhookId)
        {
            Apify.SDK.Client.ApiResponse<Object> localVarResponse = WebhookDeleteWithHttpInfo(webhookId);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Delete webhook Deletes a webhook.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <returns>ApiResponse of Object</returns>
        public Apify.SDK.Client.ApiResponse<Object> WebhookDeleteWithHttpInfo(string webhookId)
        {
            // verify the required parameter 'webhookId' is set
            if (webhookId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'webhookId' when calling WebhooksWebhooksApi->WebhookDelete");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("webhookId", Apify.SDK.Client.ClientUtils.ParameterToString(webhookId)); // path parameter

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Delete<Object>("/v2/webhooks/{webhookId}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("WebhookDelete", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Delete webhook Deletes a webhook.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of Object</returns>
        public async System.Threading.Tasks.Task<Object> WebhookDeleteAsync(string webhookId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<Object> localVarResponse = await WebhookDeleteWithHttpInfoAsync(webhookId, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Delete webhook Deletes a webhook.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (Object)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<Object>> WebhookDeleteWithHttpInfoAsync(string webhookId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'webhookId' is set
            if (webhookId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'webhookId' when calling WebhooksWebhooksApi->WebhookDelete");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("webhookId", Apify.SDK.Client.ClientUtils.ParameterToString(webhookId)); // path parameter

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.DeleteAsync<Object>("/v2/webhooks/{webhookId}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("WebhookDelete", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get collection Gets a given webhook&#39;s list of dispatches.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">ID number of the webhook.</param>
        /// <returns>WebhookDispatchList</returns>
        public WebhookDispatchList WebhookDispatchesGet(string webhookId)
        {
            Apify.SDK.Client.ApiResponse<WebhookDispatchList> localVarResponse = WebhookDispatchesGetWithHttpInfo(webhookId);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get collection Gets a given webhook&#39;s list of dispatches.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">ID number of the webhook.</param>
        /// <returns>ApiResponse of WebhookDispatchList</returns>
        public Apify.SDK.Client.ApiResponse<WebhookDispatchList> WebhookDispatchesGetWithHttpInfo(string webhookId)
        {
            // verify the required parameter 'webhookId' is set
            if (webhookId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'webhookId' when calling WebhooksWebhooksApi->WebhookDispatchesGet");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("webhookId", Apify.SDK.Client.ClientUtils.ParameterToString(webhookId)); // path parameter

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<WebhookDispatchList>("/v2/webhooks/{webhookId}/dispatches", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("WebhookDispatchesGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get collection Gets a given webhook&#39;s list of dispatches.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">ID number of the webhook.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of WebhookDispatchList</returns>
        public async System.Threading.Tasks.Task<WebhookDispatchList> WebhookDispatchesGetAsync(string webhookId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<WebhookDispatchList> localVarResponse = await WebhookDispatchesGetWithHttpInfoAsync(webhookId, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get collection Gets a given webhook&#39;s list of dispatches.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">ID number of the webhook.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (WebhookDispatchList)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<WebhookDispatchList>> WebhookDispatchesGetWithHttpInfoAsync(string webhookId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'webhookId' is set
            if (webhookId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'webhookId' when calling WebhooksWebhooksApi->WebhookDispatchesGet");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("webhookId", Apify.SDK.Client.ClientUtils.ParameterToString(webhookId)); // path parameter

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<WebhookDispatchList>("/v2/webhooks/{webhookId}/dispatches", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("WebhookDispatchesGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get webhook Gets webhook object with all details.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <returns>GetWebhookResponse</returns>
        public GetWebhookResponse WebhookGet(string webhookId)
        {
            Apify.SDK.Client.ApiResponse<GetWebhookResponse> localVarResponse = WebhookGetWithHttpInfo(webhookId);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get webhook Gets webhook object with all details.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <returns>ApiResponse of GetWebhookResponse</returns>
        public Apify.SDK.Client.ApiResponse<GetWebhookResponse> WebhookGetWithHttpInfo(string webhookId)
        {
            // verify the required parameter 'webhookId' is set
            if (webhookId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'webhookId' when calling WebhooksWebhooksApi->WebhookGet");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("webhookId", Apify.SDK.Client.ClientUtils.ParameterToString(webhookId)); // path parameter

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<GetWebhookResponse>("/v2/webhooks/{webhookId}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("WebhookGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get webhook Gets webhook object with all details.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of GetWebhookResponse</returns>
        public async System.Threading.Tasks.Task<GetWebhookResponse> WebhookGetAsync(string webhookId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<GetWebhookResponse> localVarResponse = await WebhookGetWithHttpInfoAsync(webhookId, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get webhook Gets webhook object with all details.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (GetWebhookResponse)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<GetWebhookResponse>> WebhookGetWithHttpInfoAsync(string webhookId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'webhookId' is set
            if (webhookId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'webhookId' when calling WebhooksWebhooksApi->WebhookGet");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("webhookId", Apify.SDK.Client.ClientUtils.ParameterToString(webhookId)); // path parameter

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<GetWebhookResponse>("/v2/webhooks/{webhookId}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("WebhookGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Update webhook Updates a webhook using values specified by a webhook object passed as JSON in the POST payload. If the object does not define a specific property, its value will not be updated.  The response is the full webhook object as returned by the [Get webhook](#/reference/webhooks/webhook-object/get-webhook) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <param name="webhookUpdate"></param>
        /// <returns>UpdateWebhookResponse</returns>
        public UpdateWebhookResponse WebhookPut(string webhookId, WebhookUpdate webhookUpdate)
        {
            Apify.SDK.Client.ApiResponse<UpdateWebhookResponse> localVarResponse = WebhookPutWithHttpInfo(webhookId, webhookUpdate);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Update webhook Updates a webhook using values specified by a webhook object passed as JSON in the POST payload. If the object does not define a specific property, its value will not be updated.  The response is the full webhook object as returned by the [Get webhook](#/reference/webhooks/webhook-object/get-webhook) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <param name="webhookUpdate"></param>
        /// <returns>ApiResponse of UpdateWebhookResponse</returns>
        public Apify.SDK.Client.ApiResponse<UpdateWebhookResponse> WebhookPutWithHttpInfo(string webhookId, WebhookUpdate webhookUpdate)
        {
            // verify the required parameter 'webhookId' is set
            if (webhookId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'webhookId' when calling WebhooksWebhooksApi->WebhookPut");

            // verify the required parameter 'webhookUpdate' is set
            if (webhookUpdate == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'webhookUpdate' when calling WebhooksWebhooksApi->WebhookPut");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("webhookId", Apify.SDK.Client.ClientUtils.ParameterToString(webhookId)); // path parameter
            localVarRequestOptions.Data = webhookUpdate;

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Put<UpdateWebhookResponse>("/v2/webhooks/{webhookId}", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("WebhookPut", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Update webhook Updates a webhook using values specified by a webhook object passed as JSON in the POST payload. If the object does not define a specific property, its value will not be updated.  The response is the full webhook object as returned by the [Get webhook](#/reference/webhooks/webhook-object/get-webhook) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <param name="webhookUpdate"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of UpdateWebhookResponse</returns>
        public async System.Threading.Tasks.Task<UpdateWebhookResponse> WebhookPutAsync(string webhookId, WebhookUpdate webhookUpdate, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<UpdateWebhookResponse> localVarResponse = await WebhookPutWithHttpInfoAsync(webhookId, webhookUpdate, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Update webhook Updates a webhook using values specified by a webhook object passed as JSON in the POST payload. If the object does not define a specific property, its value will not be updated.  The response is the full webhook object as returned by the [Get webhook](#/reference/webhooks/webhook-object/get-webhook) endpoint.  The request needs to specify the &#x60;Content-Type: application/json&#x60; HTTP header!  When providing your API authentication token, we recommend using the request&#39;s &#x60;Authorization&#x60; header, rather than the URL. ([More info](#/introduction/authentication)). 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <param name="webhookUpdate"></param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (UpdateWebhookResponse)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<UpdateWebhookResponse>> WebhookPutWithHttpInfoAsync(string webhookId, WebhookUpdate webhookUpdate, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'webhookId' is set
            if (webhookId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'webhookId' when calling WebhooksWebhooksApi->WebhookPut");

            // verify the required parameter 'webhookUpdate' is set
            if (webhookUpdate == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'webhookUpdate' when calling WebhooksWebhooksApi->WebhookPut");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("webhookId", Apify.SDK.Client.ClientUtils.ParameterToString(webhookId)); // path parameter
            localVarRequestOptions.Data = webhookUpdate;

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PutAsync<UpdateWebhookResponse>("/v2/webhooks/{webhookId}", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("WebhookPut", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Test webhook Tests a webhook. Creates a webhook dispatch with a dummy payload.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <returns>TestWebhookResponse</returns>
        public TestWebhookResponse WebhookTestPost(string webhookId)
        {
            Apify.SDK.Client.ApiResponse<TestWebhookResponse> localVarResponse = WebhookTestPostWithHttpInfo(webhookId);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Test webhook Tests a webhook. Creates a webhook dispatch with a dummy payload.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <returns>ApiResponse of TestWebhookResponse</returns>
        public Apify.SDK.Client.ApiResponse<TestWebhookResponse> WebhookTestPostWithHttpInfo(string webhookId)
        {
            // verify the required parameter 'webhookId' is set
            if (webhookId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'webhookId' when calling WebhooksWebhooksApi->WebhookTestPost");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("webhookId", Apify.SDK.Client.ClientUtils.ParameterToString(webhookId)); // path parameter

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<TestWebhookResponse>("/v2/webhooks/{webhookId}/test", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("WebhookTestPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Test webhook Tests a webhook. Creates a webhook dispatch with a dummy payload.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of TestWebhookResponse</returns>
        public async System.Threading.Tasks.Task<TestWebhookResponse> WebhookTestPostAsync(string webhookId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<TestWebhookResponse> localVarResponse = await WebhookTestPostWithHttpInfoAsync(webhookId, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Test webhook Tests a webhook. Creates a webhook dispatch with a dummy payload.
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookId">Webhook ID.</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (TestWebhookResponse)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<TestWebhookResponse>> WebhookTestPostWithHttpInfoAsync(string webhookId, System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'webhookId' is set
            if (webhookId == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'webhookId' when calling WebhooksWebhooksApi->WebhookTestPost");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            localVarRequestOptions.PathParameters.Add("webhookId", Apify.SDK.Client.ClientUtils.ParameterToString(webhookId)); // path parameter

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<TestWebhookResponse>("/v2/webhooks/{webhookId}/test", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("WebhookTestPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get list of webhooks Gets the list of webhooks that the user created.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 records. By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of records that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of records to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <returns>GetListOfWebhooksResponse</returns>
        public GetListOfWebhooksResponse WebhooksGet(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?))
        {
            Apify.SDK.Client.ApiResponse<GetListOfWebhooksResponse> localVarResponse = WebhooksGetWithHttpInfo(offset, limit, desc);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get list of webhooks Gets the list of webhooks that the user created.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 records. By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of records that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of records to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <returns>ApiResponse of GetListOfWebhooksResponse</returns>
        public Apify.SDK.Client.ApiResponse<GetListOfWebhooksResponse> WebhooksGetWithHttpInfo(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?))
        {
            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (desc != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "desc", desc));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Get<GetListOfWebhooksResponse>("/v2/webhooks", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("WebhooksGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Get list of webhooks Gets the list of webhooks that the user created.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 records. By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of records that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of records to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of GetListOfWebhooksResponse</returns>
        public async System.Threading.Tasks.Task<GetListOfWebhooksResponse> WebhooksGetAsync(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<GetListOfWebhooksResponse> localVarResponse = await WebhooksGetWithHttpInfoAsync(offset, limit, desc, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Get list of webhooks Gets the list of webhooks that the user created.  The endpoint supports pagination using the &#x60;limit&#x60; and &#x60;offset&#x60; parameters and it will not return more than 1000 records. By default, the records are sorted by the &#x60;createdAt&#x60; field in ascending order. To sort the records in descending order, use the &#x60;desc&#x3D;1&#x60; parameter. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="offset">Number of records that should be skipped at the start. The default value is &#x60;0&#x60;.  (optional)</param>
        /// <param name="limit">Maximum number of records to return. The default value as well as the maximum is &#x60;1000&#x60;.  (optional)</param>
        /// <param name="desc">If &#x60;true&#x60; or &#x60;1&#x60; then the objects are sorted by the &#x60;createdAt&#x60; field in descending order. By default, they are sorted in ascending order.  (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (GetListOfWebhooksResponse)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<GetListOfWebhooksResponse>> WebhooksGetWithHttpInfoAsync(double? offset = default(double?), double? limit = default(double?), bool? desc = default(bool?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (desc != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "desc", desc));
            }

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.GetAsync<GetListOfWebhooksResponse>("/v2/webhooks", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("WebhooksGet", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Create webhook Creates a new webhook with settings provided by the webhook object passed as JSON in the payload. The response is the created webhook object.  To avoid duplicating a webhook, use the &#x60;idempotencyKey&#x60; parameter in the request body. Multiple calls to create a webhook with the same &#x60;idempotencyKey&#x60; will only create the webhook with the first call and return the existing webhook on subsequent calls. Idempotency keys must be unique, so use a UUID or another random string with enough entropy.  To assign the new webhook to an Actor or task, the request body must contain &#x60;requestUrl&#x60;, &#x60;eventTypes&#x60;, and &#x60;condition&#x60; properties.  * &#x60;requestUrl&#x60; is the webhook&#39;s target URL, to which data is sent as a POST request with a JSON payload. * &#x60;eventTypes&#x60; is a list of events that will trigger the webhook, e.g. when the Actor run succeeds. * &#x60;condition&#x60; should be an object containing the ID of the Actor or task to which the webhook will be assigned. * &#x60;payloadTemplate&#x60; is a JSON-like string, whose syntax is extended with the use of variables. * &#x60;headersTemplate&#x60; is a JSON-like string, whose syntax is extended with the use of variables. Following values will be re-written to defaults: \&quot;host\&quot;, \&quot;Content-Type\&quot;, \&quot;X-Apify-Webhook\&quot;, \&quot;X-Apify-Webhook-Dispatch-Id\&quot;, \&quot;X-Apify-Request-Origin\&quot; * &#x60;description&#x60; is an optional string. * &#x60;shouldInterpolateStrings&#x60; is a boolean indicating whether to interpolate variables contained inside strings in the &#x60;payloadTemplate&#x60;  &#x60;&#x60;&#x60;     \&quot;isAdHoc\&quot; : false,     \&quot;requestUrl\&quot; : \&quot;https://example.com\&quot;,     \&quot;eventTypes\&quot; : [         \&quot;ACTOR.RUN.SUCCEEDED\&quot;,         \&quot;ACTOR.RUN.ABORTED\&quot;     ],     \&quot;condition\&quot; : {         \&quot;actorId\&quot;: \&quot;janedoe~my-actor\&quot;,         \&quot;actorTaskId\&quot; : \&quot;W9bs9JE9v7wprjAnJ\&quot;     },     \&quot;payloadTemplate\&quot;: \&quot;\&quot;,     \&quot;headersTemplate\&quot;: \&quot;\&quot;,     \&quot;description\&quot;: \&quot;my awesome webhook\&quot;,     \&quot;shouldInterpolateStrings\&quot;: false, &#x60;&#x60;&#x60;  **Important**: The request must specify the &#x60;Content-Type: application/json&#x60; HTTP header. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookCreate"></param>
        /// <param name="limit"> (optional)</param>
        /// <param name="offset"> (optional)</param>
        /// <param name="desc"> (optional)</param>
        /// <returns>CreateWebhookResponse</returns>
        public CreateWebhookResponse WebhooksPost(WebhookCreate webhookCreate, string? limit = default(string?), string? offset = default(string?), string? desc = default(string?))
        {
            Apify.SDK.Client.ApiResponse<CreateWebhookResponse> localVarResponse = WebhooksPostWithHttpInfo(webhookCreate, limit, offset, desc);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Create webhook Creates a new webhook with settings provided by the webhook object passed as JSON in the payload. The response is the created webhook object.  To avoid duplicating a webhook, use the &#x60;idempotencyKey&#x60; parameter in the request body. Multiple calls to create a webhook with the same &#x60;idempotencyKey&#x60; will only create the webhook with the first call and return the existing webhook on subsequent calls. Idempotency keys must be unique, so use a UUID or another random string with enough entropy.  To assign the new webhook to an Actor or task, the request body must contain &#x60;requestUrl&#x60;, &#x60;eventTypes&#x60;, and &#x60;condition&#x60; properties.  * &#x60;requestUrl&#x60; is the webhook&#39;s target URL, to which data is sent as a POST request with a JSON payload. * &#x60;eventTypes&#x60; is a list of events that will trigger the webhook, e.g. when the Actor run succeeds. * &#x60;condition&#x60; should be an object containing the ID of the Actor or task to which the webhook will be assigned. * &#x60;payloadTemplate&#x60; is a JSON-like string, whose syntax is extended with the use of variables. * &#x60;headersTemplate&#x60; is a JSON-like string, whose syntax is extended with the use of variables. Following values will be re-written to defaults: \&quot;host\&quot;, \&quot;Content-Type\&quot;, \&quot;X-Apify-Webhook\&quot;, \&quot;X-Apify-Webhook-Dispatch-Id\&quot;, \&quot;X-Apify-Request-Origin\&quot; * &#x60;description&#x60; is an optional string. * &#x60;shouldInterpolateStrings&#x60; is a boolean indicating whether to interpolate variables contained inside strings in the &#x60;payloadTemplate&#x60;  &#x60;&#x60;&#x60;     \&quot;isAdHoc\&quot; : false,     \&quot;requestUrl\&quot; : \&quot;https://example.com\&quot;,     \&quot;eventTypes\&quot; : [         \&quot;ACTOR.RUN.SUCCEEDED\&quot;,         \&quot;ACTOR.RUN.ABORTED\&quot;     ],     \&quot;condition\&quot; : {         \&quot;actorId\&quot;: \&quot;janedoe~my-actor\&quot;,         \&quot;actorTaskId\&quot; : \&quot;W9bs9JE9v7wprjAnJ\&quot;     },     \&quot;payloadTemplate\&quot;: \&quot;\&quot;,     \&quot;headersTemplate\&quot;: \&quot;\&quot;,     \&quot;description\&quot;: \&quot;my awesome webhook\&quot;,     \&quot;shouldInterpolateStrings\&quot;: false, &#x60;&#x60;&#x60;  **Important**: The request must specify the &#x60;Content-Type: application/json&#x60; HTTP header. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookCreate"></param>
        /// <param name="limit"> (optional)</param>
        /// <param name="offset"> (optional)</param>
        /// <param name="desc"> (optional)</param>
        /// <returns>ApiResponse of CreateWebhookResponse</returns>
        public Apify.SDK.Client.ApiResponse<CreateWebhookResponse> WebhooksPostWithHttpInfo(WebhookCreate webhookCreate, string? limit = default(string?), string? offset = default(string?), string? desc = default(string?))
        {
            // verify the required parameter 'webhookCreate' is set
            if (webhookCreate == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'webhookCreate' when calling WebhooksWebhooksApi->WebhooksPost");

            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };

            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (desc != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "desc", desc));
            }
            localVarRequestOptions.Data = webhookCreate;

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request
            var localVarResponse = this.Client.Post<CreateWebhookResponse>("/v2/webhooks", localVarRequestOptions, this.Configuration);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("WebhooksPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

        /// <summary>
        /// Create webhook Creates a new webhook with settings provided by the webhook object passed as JSON in the payload. The response is the created webhook object.  To avoid duplicating a webhook, use the &#x60;idempotencyKey&#x60; parameter in the request body. Multiple calls to create a webhook with the same &#x60;idempotencyKey&#x60; will only create the webhook with the first call and return the existing webhook on subsequent calls. Idempotency keys must be unique, so use a UUID or another random string with enough entropy.  To assign the new webhook to an Actor or task, the request body must contain &#x60;requestUrl&#x60;, &#x60;eventTypes&#x60;, and &#x60;condition&#x60; properties.  * &#x60;requestUrl&#x60; is the webhook&#39;s target URL, to which data is sent as a POST request with a JSON payload. * &#x60;eventTypes&#x60; is a list of events that will trigger the webhook, e.g. when the Actor run succeeds. * &#x60;condition&#x60; should be an object containing the ID of the Actor or task to which the webhook will be assigned. * &#x60;payloadTemplate&#x60; is a JSON-like string, whose syntax is extended with the use of variables. * &#x60;headersTemplate&#x60; is a JSON-like string, whose syntax is extended with the use of variables. Following values will be re-written to defaults: \&quot;host\&quot;, \&quot;Content-Type\&quot;, \&quot;X-Apify-Webhook\&quot;, \&quot;X-Apify-Webhook-Dispatch-Id\&quot;, \&quot;X-Apify-Request-Origin\&quot; * &#x60;description&#x60; is an optional string. * &#x60;shouldInterpolateStrings&#x60; is a boolean indicating whether to interpolate variables contained inside strings in the &#x60;payloadTemplate&#x60;  &#x60;&#x60;&#x60;     \&quot;isAdHoc\&quot; : false,     \&quot;requestUrl\&quot; : \&quot;https://example.com\&quot;,     \&quot;eventTypes\&quot; : [         \&quot;ACTOR.RUN.SUCCEEDED\&quot;,         \&quot;ACTOR.RUN.ABORTED\&quot;     ],     \&quot;condition\&quot; : {         \&quot;actorId\&quot;: \&quot;janedoe~my-actor\&quot;,         \&quot;actorTaskId\&quot; : \&quot;W9bs9JE9v7wprjAnJ\&quot;     },     \&quot;payloadTemplate\&quot;: \&quot;\&quot;,     \&quot;headersTemplate\&quot;: \&quot;\&quot;,     \&quot;description\&quot;: \&quot;my awesome webhook\&quot;,     \&quot;shouldInterpolateStrings\&quot;: false, &#x60;&#x60;&#x60;  **Important**: The request must specify the &#x60;Content-Type: application/json&#x60; HTTP header. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookCreate"></param>
        /// <param name="limit"> (optional)</param>
        /// <param name="offset"> (optional)</param>
        /// <param name="desc"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of CreateWebhookResponse</returns>
        public async System.Threading.Tasks.Task<CreateWebhookResponse> WebhooksPostAsync(WebhookCreate webhookCreate, string? limit = default(string?), string? offset = default(string?), string? desc = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            Apify.SDK.Client.ApiResponse<CreateWebhookResponse> localVarResponse = await WebhooksPostWithHttpInfoAsync(webhookCreate, limit, offset, desc, cancellationToken).ConfigureAwait(false);
            return localVarResponse.Data;
        }

        /// <summary>
        /// Create webhook Creates a new webhook with settings provided by the webhook object passed as JSON in the payload. The response is the created webhook object.  To avoid duplicating a webhook, use the &#x60;idempotencyKey&#x60; parameter in the request body. Multiple calls to create a webhook with the same &#x60;idempotencyKey&#x60; will only create the webhook with the first call and return the existing webhook on subsequent calls. Idempotency keys must be unique, so use a UUID or another random string with enough entropy.  To assign the new webhook to an Actor or task, the request body must contain &#x60;requestUrl&#x60;, &#x60;eventTypes&#x60;, and &#x60;condition&#x60; properties.  * &#x60;requestUrl&#x60; is the webhook&#39;s target URL, to which data is sent as a POST request with a JSON payload. * &#x60;eventTypes&#x60; is a list of events that will trigger the webhook, e.g. when the Actor run succeeds. * &#x60;condition&#x60; should be an object containing the ID of the Actor or task to which the webhook will be assigned. * &#x60;payloadTemplate&#x60; is a JSON-like string, whose syntax is extended with the use of variables. * &#x60;headersTemplate&#x60; is a JSON-like string, whose syntax is extended with the use of variables. Following values will be re-written to defaults: \&quot;host\&quot;, \&quot;Content-Type\&quot;, \&quot;X-Apify-Webhook\&quot;, \&quot;X-Apify-Webhook-Dispatch-Id\&quot;, \&quot;X-Apify-Request-Origin\&quot; * &#x60;description&#x60; is an optional string. * &#x60;shouldInterpolateStrings&#x60; is a boolean indicating whether to interpolate variables contained inside strings in the &#x60;payloadTemplate&#x60;  &#x60;&#x60;&#x60;     \&quot;isAdHoc\&quot; : false,     \&quot;requestUrl\&quot; : \&quot;https://example.com\&quot;,     \&quot;eventTypes\&quot; : [         \&quot;ACTOR.RUN.SUCCEEDED\&quot;,         \&quot;ACTOR.RUN.ABORTED\&quot;     ],     \&quot;condition\&quot; : {         \&quot;actorId\&quot;: \&quot;janedoe~my-actor\&quot;,         \&quot;actorTaskId\&quot; : \&quot;W9bs9JE9v7wprjAnJ\&quot;     },     \&quot;payloadTemplate\&quot;: \&quot;\&quot;,     \&quot;headersTemplate\&quot;: \&quot;\&quot;,     \&quot;description\&quot;: \&quot;my awesome webhook\&quot;,     \&quot;shouldInterpolateStrings\&quot;: false, &#x60;&#x60;&#x60;  **Important**: The request must specify the &#x60;Content-Type: application/json&#x60; HTTP header. 
        /// </summary>
        /// <exception cref="Apify.SDK.Client.ApiException">Thrown when fails to make API call</exception>
        /// <param name="webhookCreate"></param>
        /// <param name="limit"> (optional)</param>
        /// <param name="offset"> (optional)</param>
        /// <param name="desc"> (optional)</param>
        /// <param name="cancellationToken">Cancellation Token to cancel the request.</param>
        /// <returns>Task of ApiResponse (CreateWebhookResponse)</returns>
        public async System.Threading.Tasks.Task<Apify.SDK.Client.ApiResponse<CreateWebhookResponse>> WebhooksPostWithHttpInfoAsync(WebhookCreate webhookCreate, string? limit = default(string?), string? offset = default(string?), string? desc = default(string?), System.Threading.CancellationToken cancellationToken = default(global::System.Threading.CancellationToken))
        {
            // verify the required parameter 'webhookCreate' is set
            if (webhookCreate == null)
                throw new Apify.SDK.Client.ApiException(400, "Missing required parameter 'webhookCreate' when calling WebhooksWebhooksApi->WebhooksPost");


            Apify.SDK.Client.RequestOptions localVarRequestOptions = new Apify.SDK.Client.RequestOptions();

            string[] _contentTypes = new string[] {
                "application/json"
            };

            // to determine the Accept header
            string[] _accepts = new string[] {
                "application/json"
            };


            var localVarContentType = Apify.SDK.Client.ClientUtils.SelectHeaderContentType(_contentTypes);
            if (localVarContentType != null) localVarRequestOptions.HeaderParameters.Add("Content-Type", localVarContentType);

            var localVarAccept = Apify.SDK.Client.ClientUtils.SelectHeaderAccept(_accepts);
            if (localVarAccept != null) localVarRequestOptions.HeaderParameters.Add("Accept", localVarAccept);

            if (limit != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "limit", limit));
            }
            if (offset != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "offset", offset));
            }
            if (desc != null)
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "desc", desc));
            }
            localVarRequestOptions.Data = webhookCreate;

            // authentication (apiKey) required
            if (!string.IsNullOrEmpty(this.Configuration.GetApiKeyWithPrefix("token")))
            {
                localVarRequestOptions.QueryParameters.Add(Apify.SDK.Client.ClientUtils.ParameterToMultiMap("", "token", this.Configuration.GetApiKeyWithPrefix("token")));
            }
            // authentication (httpBearer) required
            // bearer authentication required
            if (!string.IsNullOrEmpty(this.Configuration.AccessToken) && !localVarRequestOptions.HeaderParameters.ContainsKey("Authorization"))
            {
                localVarRequestOptions.HeaderParameters.Add("Authorization", "Bearer " + this.Configuration.AccessToken);
            }

            // make the HTTP request

            var localVarResponse = await this.AsynchronousClient.PostAsync<CreateWebhookResponse>("/v2/webhooks", localVarRequestOptions, this.Configuration, cancellationToken).ConfigureAwait(false);

            if (this.ExceptionFactory != null)
            {
                Exception _exception = this.ExceptionFactory("WebhooksPost", localVarResponse);
                if (_exception != null) throw _exception;
            }

            return localVarResponse;
        }

    }
}

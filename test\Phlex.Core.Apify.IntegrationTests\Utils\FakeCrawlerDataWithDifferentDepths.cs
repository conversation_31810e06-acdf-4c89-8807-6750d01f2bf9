﻿using Phlex.Core.Apify.IntegrationTests.Builders;
using Phlex.Core.Apify.Interfaces;
using Phlex.Core.Apify.Models;

namespace Phlex.Core.Apify.IntegrationTests.Utils;

public class FakeCrawlerDataWithDifferentDepths : ICrawlerData
{
    public Task<CrawlerConfiguration> GetConfigurationAsync(string configName)
    {
        var configuration = CrawlerConfigurationBuilder.Default().WithName(configName)
            .WithJsonConfiguration(
                "{\"startUrls\": [], \"maxCrawlDepth\": 0, \"proxyConfiguration\": {\"mode\": \"global\"}}").Build();

        return Task.FromResult(configuration);
    }

    public Task<IQueryable<UrlItem>> GetStartUrlsAsync()
    {
        var urls = new List<UrlItem>()
        {
            UrlItemBuilder.Default()
                .WithDomain("domain1.com")
                .WithConfigurationName("config1")
                .WithMaxCrawlDepth(1)
                .WithUrl("https://domain1.com/page1")
                .Build(),
            UrlItemBuilder.Default()
                .WithDomain("domain1.com")
                .WithConfigurationName("config1")
                .WithMaxCrawlDepth(2)
                .WithUrl("https://domain1.com/page2")
                .Build()
        };

        return Task.FromResult(urls.AsQueryable());
    }
}

﻿using Phlex.Core.Apify.Models;

namespace Phlex.Core.Apify.IntegrationTests.Builders;

public class CrawlerConfigurationBuilder
{
    private string name = string.Empty;
    private string jsonConfig = string.Empty;

    public static CrawlerConfigurationBuilder Default() => new();

    public CrawlerConfiguration Build()
    {
        return new CrawlerConfiguration
        {
            Name = name,
            JsonConfig = jsonConfig
        };
    }

    public CrawlerConfigurationBuilder WithName(string name)
    {
        this.name = name;
        return this;
    }

    public CrawlerConfigurationBuilder WithJsonConfiguration(string jsonConfig)
    {
        this.jsonConfig = jsonConfig;
        return this;
    }
}

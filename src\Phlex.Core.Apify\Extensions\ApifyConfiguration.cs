﻿using Microsoft.Extensions.Configuration;

namespace Phlex.Core.Apify.Extensions
{
    public class ApifyConfiguration
    {
        public const string ConfigKey = "Apify";

        public required string BaseUri { get; set; } 
        
        public required string AccessToken { get; set; }

        public required string WebhookSecurityToken { get; set; }

        public static ApifyConfiguration Get(IConfiguration config)
        {
            var cfg = config.GetSection(ConfigKey).Get<ApifyConfiguration>();

            if (cfg == null)
            {
                throw new ArgumentException("Apify config is missing.");
            }
            if (string.IsNullOrEmpty(cfg.BaseUri))
            {
                throw new ArgumentException("Apify config is missing the base URI.");
            }
            if (string.IsNullOrEmpty(cfg.AccessToken))
            {
                throw new ArgumentException("Apify config is missing the token.");
            }

            return cfg;
        }
    }
}